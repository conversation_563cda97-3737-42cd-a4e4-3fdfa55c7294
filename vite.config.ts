import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  base: '/staging/TAM_Development/tam_storyline_tool/dist/',
  build: {
    assetsDir: 'assets',
    // Increase chunk size warning limit to 1MB (1000 kB)
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      external: [
        // Exclude native binaries from bundling
        /\.node$/,
        /tailwindcss-oxide/
      ],
      output: {
        // Manual chunk splitting for better caching and loading
        manualChunks: (id) => {
          // Vendor libraries
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react';
            }
            if (id.includes('framer-motion') || id.includes('gsap')) {
              return 'vendor-animation';
            }
            if (id.includes('@tailwindcss')) {
              return 'vendor-ui';
            }
            // Other vendor dependencies
            return 'vendor';
          }

          // Generator components (dynamic imports will handle the splitting)
          if (id.includes('/components/')) {
            if (id.includes('FadeGenerator') || id.includes('SlideGenerator') ||
                id.includes('BounceGenerator') || id.includes('ZoomGenerator')) {
              return 'generators-basic';
            }
            if (id.includes('PulseGenerator') || id.includes('ShakeGenerator') ||
                id.includes('TiltGenerator') || id.includes('RotateGenerator')) {
              return 'generators-advanced';
            }
            if (id.includes('MorphGenerator') || id.includes('GlitchGenerator') ||
                id.includes('NeonGenerator') || id.includes('MagneticGenerator')) {
              return 'generators-effects';
            }
            if (id.includes('Transform3DGenerator') || id.includes('PathDrawGenerator') ||
                id.includes('ConfettiGenerator') || id.includes('PhysicsGenerator')) {
              return 'generators-3d';
            }
          }
        },
      },
    },
  },
})
