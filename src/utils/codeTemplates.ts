export type FillConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  origin: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center' | 'left' | 'right' | 'top' | 'bottom';
  direction: 'reveal' | 'conceal';
  duration: number;
  delay: number;
  easing: 'power2.out' | 'power3.out' | 'back.out(1.7)' | 'elastic.out(1, 0.3)' | 'ease-out' | 'linear';
  stagger: number;
  color: string;
  shape: 'rectangle' | 'circle' | 'rounded-rectangle' | 'ellipse';
  borderRadius: number;
};

export type TiltConfig = {
  objectIds: string[];
  maxTilt: number;
  speed: number;
  glare: boolean;
  scale: number;
};

export type RotateConfig = {
  objectIds: string[];
  axis: 'X' | 'Y' | 'Z';
  degrees: number;
  duration: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
};

export type FadeConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'fadeIn' | 'fadeOut' | 'fadeToggle';
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  initialOpacity: number;
};

export type SlideConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
};

export type PulseConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline' | 'continuous';
  scale: number;
  duration: number;
  iterations: number;
  transformOrigin: 'center' | 'top' | 'bottom' | 'left' | 'right';
};

export type ShakeConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  intensity: number;
  duration: number;
  direction: 'horizontal' | 'vertical' | 'both';
  frequency: number;
};

export type BounceConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  height: number;
  bounces: number;
  duration: number;
  delay: number;
};

export type ZoomConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'zoomIn' | 'zoomOut' | 'zoomToggle';
  scale: number;
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  transformOrigin: 'center' | 'top' | 'bottom' | 'left' | 'right';
};

export type MorphConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'skew' | 'perspective' | 'matrix' | 'shape';
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  // Skew properties
  skewX: number;
  skewY: number;
  // Perspective properties
  perspective: number;
  rotateX: number;
  rotateY: number;
  // Matrix properties (for advanced users)
  scaleX: number;
  scaleY: number;
  translateX: number;
  translateY: number;
  // Shape morphing (combines multiple effects)
  shapeType: 'diamond' | 'parallelogram' | 'trapezoid' | 'custom';
  intensity: number;
};

export type GlitchConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'digital' | 'rgb' | 'shake' | 'corrupt';
  duration: number;
  delay: number;
  intensity: number;
  speed: number;
  iterations: number;
};

export type NeonConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'glow' | 'pulse' | 'flicker' | 'rainbow';
  duration: number;
  delay: number;
  color: string;
  intensity: number;
  blurRadius: number;
  spreadRadius: number;
};

export type MagneticConfig = {
  objectIds: string[];
  strength: number;
  distance: number;
  speed: number;
  returnSpeed: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
};

export type Transform3DConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'cardFlip' | 'rotate3D' | 'perspective' | 'depth' | 'cube';
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  // Rotation properties
  rotateX: number;
  rotateY: number;
  rotateZ: number;
  // Transform properties
  translateZ: number;
  perspective: number;
  transformOrigin: 'center' | 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  // Card flip specific
  flipDirection: 'horizontal' | 'vertical';
  // 3D rotation specific
  rotationSpeed: number;
  continuous: boolean;
};

export type PathDrawConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  library: 'gsap' | 'anime';
  direction: 'forward' | 'reverse';
  duration: number;
  delay: number;
  easing: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
  pathData: string;
  allPaths?: string[]; // Store all paths from uploaded SVG
  loop: boolean;
  // Animation-only options (don't affect SVG appearance)
  stagger: number; // Delay between multiple paths
};

export type ConfettiConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  particleCount: number;
  colors: string[];
  shapes: ('square' | 'circle' | 'star' | 'triangle' | 'heart' | 'plus' | 'ribbon' | 'squiggle' | 'streamer')[];
  duration: number;
  delay: number;
  origin: {
    x: number; // 0-1 relative to element
    y: number; // 0-1 relative to element
  };
  spread: number; // degrees
  angle: number; // degrees
  gravity: number;
  startVelocity: number;
  scalar: number; // size multiplier
};

export type PhysicsConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'drop' | 'bounce' | 'explode' | 'attract' | 'repel';
  objectCount: number;
  objectShape: 'circle' | 'rectangle' | 'polygon';
  objectSize: number; // 10-100 pixels
  gravity: number; // 0-2
  restitution: number; // 0-1 (bounciness)
  friction: number; // 0-1
  duration: number; // how long physics runs
  delay: number;
  colors: string[];
  initialVelocity: {
    x: number; // -100 to 100
    y: number; // -100 to 100
  };
};

export type RippleConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  effect: 'material' | 'water' | 'pulse' | 'shockwave';
  size: 'small' | 'medium' | 'large' | 'auto';
  color: string;
  opacity: number; // 0-1
  duration: number; // 0.3-3 seconds
  delay: number;
  origin: 'center' | 'click' | 'custom';
  customOrigin: {
    x: number; // 0-100 percentage
    y: number; // 0-100 percentage
  };
  allowMultiple: boolean; // Allow overlapping ripples
  easing: 'power2.out' | 'power3.out' | 'back.out(1.7)' | 'elastic.out(1, 0.3)' | 'ease-out' | 'linear';
};

export type TypewriterConfig = {
  objectIds: string[];
  trigger: 'click' | 'hover' | 'timeline';
  strings: string[];
  typeSpeed: number;
  backSpeed: number;
  startDelay: number;
  backDelay: number;
  loop: boolean;
  loopCount: number;
  showCursor: boolean;
  cursorChar: string;
  smartBackspace: boolean;
  shuffle: boolean;
  fadeOut: boolean;
  fadeOutDelay: number;
};

export const generateTiltCode = (config: TiltConfig): string => {
  const { objectIds, maxTilt, speed, glare, scale } = config;

  return `
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function(id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Apply essential CSS properties for proper tilt functionality
      el.style.transformStyle = "preserve-3d";
      el.style.willChange = "transform";
      el.style.transformOrigin = "center center";
      el.style.display = "inline-block";
      el.style.position = "relative";
      el.style.zIndex = 2;

      VanillaTilt.init(el, {
        max: ${maxTilt},
        speed: ${speed},
        glare: ${glare},
        "max-glare": ${glare ? '0.2' : '0'},
        scale: ${scale}
      });
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim();
};

export const generateRotateCode = (config: RotateConfig): string => {
  const { objectIds, axis, degrees, duration, easing } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      let rotation = 0;

      el.style.transformStyle = "preserve-3d";
      el.style.transition = "transform ${duration}s";

      el.addEventListener("click", function () {
        rotation += ${degrees};
        gsap.to(el, {
          duration: ${duration},
          ["rotation" + "${axis}"]: rotation,
          transformOrigin: "center center",
          ease: "${easing}"
        });
      });
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateFadeCode = (config: FadeConfig): string => {
  const { objectIds, trigger, effect, duration, delay, easing, initialOpacity } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${effect === 'fadeIn' && trigger === 'timeline' ? `el.style.opacity = "${initialOpacity / 100}";` : ''}

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${duration},
          ${effect === 'fadeIn' ? 'opacity: 1' : effect === 'fadeOut' ? 'opacity: 0' : 'opacity: window.getComputedStyle(el).opacity > 0.5 ? 0 : 1'},
          ease: "${easing}"
        });
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        ${effect === 'fadeIn' ? `
        // Set initial opacity before animating
        el.style.opacity = "${initialOpacity / 100}";` : ''}

        setTimeout(function() {
          ${effect === 'fadeToggle' ? `
          const currentOpacity = window.getComputedStyle(el).opacity;
          const targetOpacity = parseFloat(currentOpacity) > 0.5 ? 0 : 1;
          gsap.to(el, {
            duration: ${duration},
            opacity: targetOpacity,
            ease: "${easing}"
          });` : `
          gsap.to(el, {
            duration: ${duration},
            ${effect === 'fadeIn' ? 'opacity: 1' : 'opacity: 0'},
            ease: "${easing}"
          });`}
        }, ${delay * 1000});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateSlideCode = (config: SlideConfig): string => {
  const { objectIds, trigger, direction, distance, duration, delay, easing } = config;

  // Calculate transform values based on direction
  const getTransformValues = (dir: string, dist: number) => {
    switch (dir) {
      case 'left': return { x: -dist, y: 0 };
      case 'right': return { x: dist, y: 0 };
      case 'up': return { x: 0, y: -dist };
      case 'down': return { x: 0, y: dist };
      default: return { x: 0, y: 0 };
    }
  };

  const { x: initialX, y: initialY } = getTransformValues(direction, distance);

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${trigger === 'timeline' ? `
      // Set initial position for timeline trigger using percentage units
      el.style.transform = "translate(${initialX}%, ${initialY}%)";
      el.style.opacity = "0";` : ''}

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${duration},
          x: 0,
          y: 0,
          opacity: 1,
          ease: "${easing}"
        });
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        // Set initial position before animating using percentage units
        el.style.transform = "translate(${initialX}%, ${initialY}%)";
        el.style.opacity = "0";

        setTimeout(function() {
          gsap.to(el, {
            duration: ${duration},
            x: 0,
            y: 0,
            opacity: 1,
            ease: "${easing}"
          });
        }, ${delay * 1000});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generatePulseCode = (config: PulseConfig): string => {
  const { objectIds, trigger, scale, duration, iterations, transformOrigin } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${trigger === 'continuous' ? `
      // Continuous pulse effect - start immediately
      gsap.to(el, {
        duration: ${duration},
        scale: ${scale},
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        transformOrigin: "${transformOrigin} ${transformOrigin}"
      });` : trigger === 'timeline' ? `

      // Timeline trigger - execute immediately
      gsap.to(el, {
        duration: ${duration / 2},
        scale: ${scale},
        repeat: ${iterations * 2 - 1},
        yoyo: true,
        ease: "power2.inOut",
        transformOrigin: "${transformOrigin} ${transformOrigin}"
      });` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        gsap.to(el, {
          duration: ${duration / 2},
          scale: ${scale},
          repeat: ${iterations * 2 - 1},
          yoyo: true,
          ease: "power2.inOut",
          transformOrigin: "${transformOrigin} ${transformOrigin}"
        });
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateShakeCode = (config: ShakeConfig): string => {
  const { objectIds, trigger, intensity, duration, direction, frequency } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately
      const timeline = gsap.timeline();
      const shakeCount = Math.ceil(${duration} * ${frequency}); // ${frequency} shakes per second

      for (let i = 0; i < shakeCount; i++) {
        const xOffset = ${direction === 'vertical' ? '0' : `(Math.random() - 0.5) * ${intensity * 2}`};
        const yOffset = ${direction === 'horizontal' ? '0' : `(Math.random() - 0.5) * ${intensity * 2}`};

        timeline.to(el, {
          duration: ${duration / frequency},
          x: ${direction === 'vertical' ? '0' : 'xOffset + "%"'},
          y: ${direction === 'horizontal' ? '0' : 'yOffset + "%"'},
          ease: "power2.inOut"
        });
      }

      // Return to original position
      timeline.to(el, {
        duration: ${duration / frequency},
        x: 0,
        y: 0,
        ease: "power2.out"
      });` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        const timeline = gsap.timeline();
        const shakeCount = Math.ceil(${duration} * ${frequency}); // ${frequency} shakes per second

        for (let i = 0; i < shakeCount; i++) {
          const xOffset = ${direction === 'vertical' ? '0' : `(Math.random() - 0.5) * ${intensity * 2}`};
          const yOffset = ${direction === 'horizontal' ? '0' : `(Math.random() - 0.5) * ${intensity * 2}`};

          timeline.to(el, {
            duration: ${duration / frequency},
            x: ${direction === 'vertical' ? '0' : 'xOffset + "%"'},
            y: ${direction === 'horizontal' ? '0' : 'yOffset + "%"'},
            ease: "power2.inOut"
          });
        }

        // Return to original position
        timeline.to(el, {
          duration: ${duration / frequency},
          x: 0,
          y: 0,
          ease: "power2.out"
        });
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateBounceCode = (config: BounceConfig): string => {
  const { objectIds, trigger, height, bounces, duration, delay } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        const timeline = gsap.timeline();

        for (let i = 0; i < ${bounces}; i++) {
          const bounceHeight = ${height} * (1 - i / ${bounces}); // Decreasing height in percentage
          timeline.to(el, {
            duration: ${duration / (bounces * 2)},
            y: "-" + bounceHeight + "%",
            ease: "power2.out"
          }).to(el, {
            duration: ${duration / (bounces * 2)},
            y: 0,
            ease: "power2.in"
          });
        }
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        setTimeout(function() {
          const timeline = gsap.timeline();

          for (let i = 0; i < ${bounces}; i++) {
            const bounceHeight = ${height} * (1 - i / ${bounces}); // Decreasing height in percentage
            timeline.to(el, {
              duration: ${duration / (bounces * 2)},
              y: "-" + bounceHeight + "%",
              ease: "power2.out"
            }).to(el, {
              duration: ${duration / (bounces * 2)},
              y: 0,
              ease: "power2.in"
            });
          }
        }, ${delay * 1000});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateZoomCode = (config: ZoomConfig): string => {
  const { objectIds, trigger, effect, scale, duration, delay, easing, transformOrigin } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${effect === 'zoomIn' && trigger === 'timeline' ? `el.style.transform = "scale(0)";` : ''}

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${duration},
          ${effect === 'zoomIn' ? `scale: ${scale}` : effect === 'zoomOut' ? `scale: 0` : `scale: el.style.transform.includes('scale') ? 1 : ${scale}`},
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}"
        });
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        ${effect === 'zoomIn' ? `
        // Set initial scale before animating
        el.style.transform = "scale(0)";` : ''}

        setTimeout(function() {
          ${effect === 'zoomToggle' ? `
          const currentScale = el.style.transform.match(/scale\\(([^)]+)\\)/);
          const isScaled = currentScale && parseFloat(currentScale[1]) > 1;
          const targetScale = isScaled ? 1 : ${scale};
          gsap.to(el, {
            duration: ${duration},
            scale: targetScale,
            ease: "${easing}",
            transformOrigin: "${transformOrigin} ${transformOrigin}"
          });` : `
          gsap.to(el, {
            duration: ${duration},
            ${effect === 'zoomIn' ? `scale: ${scale}` : `scale: 0`},
            ease: "${easing}",
            transformOrigin: "${transformOrigin} ${transformOrigin}"
          });`}
        }, ${delay * 1000});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateMorphCode = (config: MorphConfig): string => {
  const {
    objectIds,
    trigger,
    effect,
    duration,
    delay,
    easing,
    skewX,
    skewY,
    perspective,
    rotateX,
    rotateY,
    scaleX,
    scaleY,
    translateX,
    translateY,
    shapeType,
    intensity
  } = config;

  // Generate transform string based on effect type
  const getTransformString = () => {
    switch (effect) {
      case 'skew':
        return `skew(${skewX}deg, ${skewY}deg)`;

      case 'perspective':
        return `perspective(${perspective * 10}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;

      case 'matrix': {
        // Create a 2D transformation matrix (no rotation applied)
        const sin = Math.sin(0); // Always 0 for no rotation
        return `matrix(${scaleX}, ${sin}, ${-sin}, ${scaleY}, ${translateX}, ${translateY})`;
      }

      case 'shape':
        switch (shapeType) {
          case 'diamond':
            return `rotate(45deg) scale(${1 + intensity * 0.1})`;
          case 'parallelogram':
            return `skew(${intensity * 2}deg, 0deg) scale(${1 + intensity * 0.05})`;
          case 'trapezoid':
            return `perspective(${(50 + intensity * 10) * 10}px) rotateX(${intensity * 3}deg)`;
          default:
            return `skew(${intensity}deg, ${intensity * 0.5}deg) scale(${1 + intensity * 0.1})`;
        }

      default:
        return `skew(${skewX}deg, ${skewY}deg)`;
    }
  };

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original transform for reset
      const originalTransform = el.style.transform || 'none';

      // Set transform origin for better morphing
      el.style.transformOrigin = "center center";
      el.style.transformStyle = "preserve-3d";

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${duration},
          transform: "${getTransformString()}",
          ease: "${easing}",
          onComplete: function() {
            // Optional: Reset after animation completes
            // gsap.to(el, { duration: 0.5, transform: originalTransform, ease: "power2.out" });
          }
        });
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        setTimeout(function() {
          gsap.to(el, {
            duration: ${duration},
            transform: "${getTransformString()}",
            ease: "${easing}"
          });
        }, ${delay * 1000});
      });

      ${trigger === 'hover' ? `
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${duration * 0.7},
          transform: originalTransform,
          ease: "${easing}"
        });
      });` : ''}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateGlitchCode = (config: GlitchConfig): string => {
  const { objectIds, trigger, effect, duration, delay, intensity, speed, iterations } = config;

  const getGlitchEffect = () => {
    switch (effect) {
      case 'digital':
        return `
        // Digital glitch effect
        const timeline = gsap.timeline({ repeat: ${iterations - 1} });

        // Create multiple rapid transforms
        for (let i = 0; i < ${Math.ceil(speed)}; i++) {
          timeline.to(el, {
            duration: ${duration / speed / 4},
            x: (Math.random() - 0.5) * ${intensity} + "%",
            y: (Math.random() - 0.5) * ${intensity * 0.5} + "%",
            scaleX: 1 + (Math.random() - 0.5) * ${intensity * 0.01},
            scaleY: 1 + (Math.random() - 0.5) * ${intensity * 0.01},
            ease: "none"
          }).to(el, {
            duration: ${duration / speed / 4},
            x: 0,
            y: 0,
            scaleX: 1,
            scaleY: 1,
            ease: "power2.out"
          });
        }`;

      case 'rgb':
        return `
        // RGB split glitch effect
        const timeline = gsap.timeline({ repeat: ${iterations - 1} });

        // Create pseudo-elements for RGB channels
        const beforeEl = window.getComputedStyle(el, '::before');
        const afterEl = window.getComputedStyle(el, '::after');

        // Add CSS for RGB split effect
        const style = document.createElement('style');
        style.textContent = \`
          [data-acc-text="\${id}"]::before,
          [data-acc-text="\${id}"]::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            mix-blend-mode: screen;
          }
          [data-acc-text="\${id}"]::before {
            color: #ff0000;
            animation: glitch-before ${duration}s infinite;
          }
          [data-acc-text="\${id}"]::after {
            color: #00ffff;
            animation: glitch-after ${duration}s infinite;
          }
          @keyframes glitch-before {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-${intensity}%, ${intensity}%); }
            40% { transform: translate(-${intensity}%, -${intensity}%); }
            60% { transform: translate(${intensity}%, ${intensity}%); }
            80% { transform: translate(${intensity}%, -${intensity}%); }
          }
          @keyframes glitch-after {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(${intensity}%, ${intensity}%); }
            40% { transform: translate(${intensity}%, -${intensity}%); }
            60% { transform: translate(-${intensity}%, ${intensity}%); }
            80% { transform: translate(-${intensity}%, -${intensity}%); }
          }
        \`;
        document.head.appendChild(style);

        // Set data attribute for content
        el.setAttribute('data-text', el.textContent || el.innerText);
        el.style.position = 'relative';

        // Remove animation after duration
        setTimeout(() => {
          document.head.removeChild(style);
        }, ${duration * iterations * 1000});`;

      case 'shake':
        return `
        // Intense shake glitch
        const timeline = gsap.timeline({ repeat: ${iterations - 1} });

        for (let i = 0; i < ${speed * 2}; i++) {
          timeline.to(el, {
            duration: ${duration / (speed * 4)},
            x: (Math.random() - 0.5) * ${intensity * 2} + "%",
            y: (Math.random() - 0.5) * ${intensity * 2} + "%",
            rotation: (Math.random() - 0.5) * ${intensity * 0.5},
            ease: "none"
          });
        }

        timeline.to(el, {
          duration: ${duration / speed},
          x: 0,
          y: 0,
          rotation: 0,
          ease: "power2.out"
        });`;

      case 'corrupt':
        return `
        // Data corruption effect
        const timeline = gsap.timeline({ repeat: ${iterations - 1} });

        timeline.to(el, {
          duration: ${duration / 3},
          scaleX: 1 + ${intensity * 0.1},
          scaleY: 1 - ${intensity * 0.05},
          skewX: ${intensity * 2},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${duration / 3},
          scaleX: 1 - ${intensity * 0.05},
          scaleY: 1 + ${intensity * 0.1},
          skewX: -${intensity},
          skewY: ${intensity},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${duration / 3},
          scaleX: 1,
          scaleY: 1,
          skewX: 0,
          skewY: 0,
          ease: "power2.out"
        });`;

      default:
        return `
        // Default digital glitch
        const timeline = gsap.timeline({ repeat: ${iterations - 1} });
        timeline.to(el, {
          duration: ${duration},
          x: (Math.random() - 0.5) * ${intensity},
          y: (Math.random() - 0.5) * ${intensity},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${duration / 2},
          x: 0,
          y: 0,
          ease: "power2.out"
        });`;
    }
  };

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${getGlitchEffect()}
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        setTimeout(function() {
          ${getGlitchEffect()}
        }, ${delay * 1000});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateNeonCode = (config: NeonConfig): string => {
  const { objectIds, trigger, effect, duration, delay, color, intensity, blurRadius, spreadRadius } = config;

  const getNeonEffect = () => {
    const baseGlow = `0 0 ${blurRadius * 5}px ${color}, 0 0 ${blurRadius * 10}px ${color}, 0 0 ${blurRadius * 15}px ${color}`;
    const intenseGlow = `0 0 ${blurRadius * intensity * 5}px ${color}, 0 0 ${blurRadius * intensity * 10}px ${color}, 0 0 ${blurRadius * intensity * 15}px ${color}, 0 0 ${spreadRadius * 5}px ${color}`;

    switch (effect) {
      case 'glow':
        return `
        // Static neon glow effect
        el.style.textShadow = "${intenseGlow}";
        el.style.color = "${color}";
        el.style.transition = "text-shadow ${duration}s ease";`;

      case 'pulse':
        return `
        // Pulsing neon effect
        const timeline = gsap.timeline({ repeat: -1, yoyo: true });

        timeline.to(el, {
          duration: ${duration / 2},
          textShadow: "${intenseGlow}",
          color: "${color}",
          ease: "power2.inOut"
        }).to(el, {
          duration: ${duration / 2},
          textShadow: "${baseGlow}",
          ease: "power2.inOut"
        });`;

      case 'flicker':
        return `
        // Flickering neon effect
        const flickerTimeline = gsap.timeline({ repeat: -1 });

        // Random flicker pattern
        const flickerPattern = [1, 0.8, 1, 0.3, 1, 0.9, 0.1, 1, 0.7, 1];

        flickerPattern.forEach((opacity, index) => {
          flickerTimeline.to(el, {
            duration: ${duration / 10},
            textShadow: opacity > 0.5 ? "${intenseGlow}" : "${baseGlow}",
            color: opacity > 0.5 ? "${color}" : "rgba(${color.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ') || '255, 255, 255'}, 0.5)",
            ease: "none"
          });
        });`;

      case 'rainbow':
        return `
        // Rainbow neon effect
        const colors = ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];
        const timeline = gsap.timeline({ repeat: -1 });

        colors.forEach((rainbowColor, index) => {
          const rainbowGlow = \`0 0 ${blurRadius * 5}px \${rainbowColor}, 0 0 ${blurRadius * 10}px \${rainbowColor}, 0 0 ${blurRadius * 15}px \${rainbowColor}\`;
          timeline.to(el, {
            duration: ${duration / 7}, // 7 colors in rainbow
            textShadow: rainbowGlow,
            color: rainbowColor,
            ease: "power2.inOut"
          });
        });`;

      default:
        return `
        // Default glow effect
        el.style.textShadow = "${intenseGlow}";
        el.style.color = "${color}";`;
    }
  };

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original styles
      const originalTextShadow = el.style.textShadow || 'none';
      const originalColor = el.style.color || window.getComputedStyle(el).color;

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${getNeonEffect()}
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        setTimeout(function() {
          ${getNeonEffect()}
        }, ${delay * 1000});
      });

      ${trigger === 'hover' ? `
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${duration * 0.3},
          textShadow: originalTextShadow,
          color: originalColor,
          ease: "power2.out"
        });
      });` : ''}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateMagneticCode = (config: MagneticConfig): string => {
  const { objectIds, strength, distance, speed, returnSpeed, easing } = config;

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original position
      const originalRect = el.getBoundingClientRect();
      const originalX = originalRect.left + originalRect.width / 2;
      const originalY = originalRect.top + originalRect.height / 2;

      // Set up element for smooth transforms
      el.style.willChange = "transform";
      el.style.transformOrigin = "center center";

      let isHovering = false;
      let animationFrame;

      // Create magnetic area (larger than the element) - distance is percentage of viewport
      const magneticArea = document.createElement('div');
      magneticArea.style.position = 'absolute';
      const distancePixels = ${distance} * Math.min(window.innerWidth, window.innerHeight) / 100;
      magneticArea.style.left = (originalRect.left - distancePixels) + 'px';
      magneticArea.style.top = (originalRect.top - distancePixels) + 'px';
      magneticArea.style.width = (originalRect.width + distancePixels * 2) + 'px';
      magneticArea.style.height = (originalRect.height + distancePixels * 2) + 'px';
      magneticArea.style.pointerEvents = 'none';
      magneticArea.style.zIndex = '9999';
      document.body.appendChild(magneticArea);

      function updatePosition(mouseX, mouseY) {
        if (!isHovering) return;

        const rect = el.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // Calculate distance from mouse to element center
        const deltaX = mouseX - centerX;
        const deltaY = mouseY - centerY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Only apply magnetic effect if within range (distance is percentage of viewport)
        const maxDistance = ${distance} * Math.min(window.innerWidth, window.innerHeight) / 100;
        if (distance < maxDistance) {
          // Calculate magnetic force (stronger when closer)
          const force = Math.max(0, 1 - distance / maxDistance);
          const moveX = deltaX * force * ${strength};
          const moveY = deltaY * force * ${strength};

          gsap.to(el, {
            duration: ${speed},
            x: moveX,
            y: moveY,
            ease: "${easing}",
            overwrite: true
          });
        }
      }

      function handleMouseMove(e) {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }

        animationFrame = requestAnimationFrame(() => {
          updatePosition(e.clientX, e.clientY);
        });
      }

      function handleMouseEnter() {
        isHovering = true;
        document.addEventListener('mousemove', handleMouseMove);
      }

      function handleMouseLeave() {
        isHovering = false;
        document.removeEventListener('mousemove', handleMouseMove);

        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }

        // Return to original position
        gsap.to(el, {
          duration: ${returnSpeed},
          x: 0,
          y: 0,
          ease: "${easing}"
        });
      }

      // Add event listeners to the magnetic area
      magneticArea.addEventListener('mouseenter', handleMouseEnter);
      magneticArea.addEventListener('mouseleave', handleMouseLeave);

      // Also add listeners to the element itself for better UX
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);

    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateTransform3DCode = (config: Transform3DConfig): string => {
  const {
    objectIds,
    trigger,
    effect,
    duration,
    delay,
    easing,
    rotateX,
    rotateY,
    rotateZ,
    translateZ,
    perspective,
    transformOrigin,
    flipDirection,
    rotationSpeed,
    continuous
  } = config;

  const getEffectCode = () => {
    switch (effect) {
      case 'cardFlip':
        return flipDirection === 'horizontal' ? `
        // Card flip horizontal
        gsap.to(el, {
          duration: ${duration},
          rotateY: 180,
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });` : `
        // Card flip vertical
        gsap.to(el, {
          duration: ${duration},
          rotateX: 180,
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });`;

      case 'rotate3D':
        return continuous ? `
        // Continuous 3D rotation
        gsap.to(el, {
          duration: ${rotationSpeed},
          rotateX: ${rotateX},
          rotateY: ${rotateY},
          rotateZ: ${rotateZ},
          repeat: -1,
          ease: "none",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });` : `
        // Single 3D rotation
        gsap.to(el, {
          duration: ${duration},
          rotateX: ${rotateX},
          rotateY: ${rotateY},
          rotateZ: ${rotateZ},
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });`;

      case 'perspective':
        return `
        // 3D perspective tilt
        gsap.to(el, {
          duration: ${duration},
          rotateX: ${rotateX},
          rotateY: ${rotateY},
          transformPerspective: ${perspective * 10},
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });`;

      case 'depth':
        return `
        // 3D depth movement
        gsap.to(el, {
          duration: ${duration},
          translateZ: ${translateZ * 10},
          rotateX: ${rotateX * 0.3},
          rotateY: ${rotateY * 0.3},
          transformPerspective: ${perspective * 10},
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });`;

      case 'cube':
        return `
        // 3D cube rotation
        const timeline = gsap.timeline();
        timeline
          .to(el, {
            duration: ${duration / 3},
            rotateY: 90,
            ease: "${easing}",
            transformOrigin: "${transformOrigin} ${transformOrigin}",
            transformStyle: "preserve-3d"
          })
          .to(el, {
            duration: ${duration / 3},
            rotateX: 90,
            ease: "${easing}"
          })
          .to(el, {
            duration: ${duration / 3},
            rotateZ: 90,
            ease: "${easing}"
          });`;

      default:
        return `
        // Default 3D transform
        gsap.to(el, {
          duration: ${duration},
          rotateX: ${rotateX},
          rotateY: ${rotateY},
          rotateZ: ${rotateZ},
          ease: "${easing}",
          transformOrigin: "${transformOrigin} ${transformOrigin}",
          transformStyle: "preserve-3d"
        });`;
    }
  };

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Set up 3D transform properties
      el.style.transformStyle = "preserve-3d";
      el.style.transformPerspective = "${perspective * 10}px";
      el.style.willChange = "transform";
      el.style.backfaceVisibility = "hidden";

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${getEffectCode()}
      }, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
        setTimeout(function() {
          ${getEffectCode()}
        }, ${delay * 1000});
      });

      ${trigger === 'hover' ? `
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${duration * 0.7},
          rotateX: 0,
          rotateY: 0,
          rotateZ: 0,
          translateZ: 0,
          ease: "${easing}"
        });
      });` : ''}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generatePathDrawCode = (config: PathDrawConfig): string => {
  const { library } = config;

  if (library === 'anime') {
    return generateAnimePathDrawCode(config);
  } else {
    return generateGSAPPathDrawCode(config);
  }
};

const generateAnimePathDrawCode = (config: PathDrawConfig): string => {
  const { objectIds, trigger, direction, duration, delay, easing, loop, stagger } = config;

  return `
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/animejs@3.2.2/lib/anime.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function(id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Find existing SVG paths in the element
      const paths = el.querySelectorAll('path');

      if (paths.length === 0) {
        console.error("No SVG paths found in element with ID: " + id);
        return;
      }

      // Set initial state for all paths
      paths.forEach(function(path) {
        const pathLength = path.getTotalLength();
        path.style.strokeDasharray = pathLength;
        path.style.strokeDashoffset = ${direction === 'forward' ? 'pathLength' : '0'};
      });

      function animatePath() {
        paths.forEach(function(path, index) {
          const pathLength = path.getTotalLength();
          anime({
            targets: path,
            strokeDashoffset: ${direction === 'forward' ? '0' : 'pathLength'},
            duration: ${duration * 1000},
            delay: ${delay * 1000} + (index * ${(stagger || 0) * 1000}),
            easing: '${easing === 'ease' ? 'easeInOutQuad' : easing === 'ease-in' ? 'easeInQuad' : easing === 'ease-out' ? 'easeOutQuad' : easing === 'ease-in-out' ? 'easeInOutQuad' : 'linear'}',
            ${loop ? 'loop: true,' : ''}
            complete: function() {
              ${loop ? '' : '// Animation complete'}
            }
          });
        });
      }

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(animatePath, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", animatePath);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim();
};

const generateGSAPPathDrawCode = (config: PathDrawConfig): string => {
  const { objectIds, trigger, direction, duration, delay, easing, loop, stagger } = config;

  return `
// Load GSAP DrawSVG plugin (now free as of 2025!)
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/DrawSVGPlugin.min.js";
script.onload = function () {
  // Register the DrawSVG plugin
  gsap.registerPlugin(DrawSVGPlugin);

  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Replace element content with the uploaded SVG
      el.innerHTML = '';

      // Find existing SVG paths in the element
      const paths = el.querySelectorAll('path');

      if (paths.length === 0) {
        console.error("No SVG paths found in element with ID: " + id);
        return;
      }

      // Animate all paths found in the element
      paths.forEach(function(path) {
        // Set initial state using DrawSVG plugin
        gsap.set(path, {
          drawSVG: ${direction === 'forward' ? '"0%"' : '"100%"'}
        });
      });

      function animatePath() {
        paths.forEach(function(path, index) {
          gsap.to(path, {
            drawSVG: ${direction === 'forward' ? '"100%"' : '"0%"'},
            duration: ${duration},
            delay: ${delay} + (index * ${stagger || 0}),
            ease: "${easing}",
            ${loop ? 'repeat: -1,' : ''}
            onComplete: function() {
              ${loop ? '' : '// Animation complete'}
            }
          });
        });
      }

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      setTimeout(animatePath, ${delay * 1000});` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", animatePath);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim();
};

export const generateConfettiCode = (config: ConfettiConfig): string => {
  const { objectIds, trigger, particleCount, colors, shapes, duration, delay, origin, spread, angle, gravity, startVelocity, scalar } = config;

  const confettiOptions = {
    particleCount,
    colors: colors.length > 0 ? colors : undefined,
    shapes: shapes.length > 0 ? shapes : undefined,
    spread,
    angle,
    gravity,
    startVelocity,
    scalar,
    ticks: Math.max(50, duration * 60), // Convert duration to ticks (roughly 60fps)
  };

  // Remove undefined values for cleaner code
  const cleanOptions = Object.fromEntries(
    Object.entries(confettiOptions).filter(([_, value]) => value !== undefined)
  );

  // Create custom shapes helper
  const customShapes = shapes.filter(shape => !['square', 'circle', 'star'].includes(shape));
  const builtInShapes = shapes.filter(shape => ['square', 'circle', 'star'].includes(shape));

  return `
// Load confetti.js library
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(objectIds)};

  // Create custom shapes
  const customShapes = {};
  ${customShapes.map(shape => {
    const paths = {
      triangle: 'M0 10 L5 0 L10 10z',
      heart: 'M5 0 C2.5 -2.5 0 0 0 2.5 C0 5 2.5 7.5 5 10 C7.5 7.5 10 5 10 2.5 C10 0 7.5 -2.5 5 0z',
      plus: 'M4 0 L6 0 L6 4 L10 4 L10 6 L6 6 L6 10 L4 10 L4 6 L0 6 L0 4 L4 4z',
      ribbon: 'M0 3 Q3 0 6 3 Q9 6 12 3 Q15 0 18 3 L18 7 Q15 10 12 7 Q9 4 6 7 Q3 10 0 7 Z',
      squiggle: 'M0 8 Q2 2 4 8 Q6 14 8 8 Q10 2 12 8 Q14 14 16 8 Q18 2 20 8',
      streamer: 'M0 2 Q5 0 10 2 Q15 4 20 2 Q25 0 30 2 L30 4 Q25 6 20 4 Q15 2 10 4 Q5 6 0 4 Z'
    };
    return `customShapes.${shape} = confetti.shapeFromPath({ path: '${paths[shape]}' });`;
  }).join('\n  ')}

  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      function launchConfetti() {
        setTimeout(function() {
          // Get element position for confetti origin
          const rect = el.getBoundingClientRect();
          const originX = (rect.left + rect.width * ${origin.x}) / window.innerWidth;
          const originY = (rect.top + rect.height * ${origin.y}) / window.innerHeight;

          // Combine built-in and custom shapes
          const allShapes = [${builtInShapes.map(s => `'${s}'`).join(', ')}${builtInShapes.length > 0 && customShapes.length > 0 ? ', ' : ''}${customShapes.map(s => `customShapes.${s}`).join(', ')}];

          const confettiOptions = ${JSON.stringify({...cleanOptions, shapes: undefined}, null, 12)};
          confettiOptions.origin = { x: originX, y: originY };
          ${shapes.length > 0 ? 'confettiOptions.shapes = allShapes;' : ''}

          confetti(confettiOptions);
        }, ${delay * 1000});
      }

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      launchConfetti();` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", launchConfetti);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim();
};

export const generatePhysicsCode = (config: PhysicsConfig): string => {
  const { objectIds, trigger, effect, objectCount, objectShape, objectSize, gravity, restitution, friction, duration, delay, colors, initialVelocity } = config;

  const getPhysicsEffect = () => {
    switch (effect) {
      case 'drop':
        return `
        // Drop objects with gravity
        for (let i = 0; i < ${objectCount}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top - 50 - Math.random() * 100;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${objectShape === 'circle' ? 'circle' : objectShape === 'rectangle' ? 'rectangle' : 'polygon'}(
            x, y,
            ${objectShape === 'circle' ? `${objectSize / 2}` : objectShape === 'rectangle' ? `${objectSize}, ${objectSize}` : `${objectSize / 2}, 6`},
            {
              restitution: ${restitution},
              friction: ${friction},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);
        }`;

      case 'bounce':
        return `
        // Bouncing objects
        for (let i = 0; i < ${objectCount}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top + rect.height / 2;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${objectShape === 'circle' ? 'circle' : objectShape === 'rectangle' ? 'rectangle' : 'polygon'}(
            x, y,
            ${objectShape === 'circle' ? `${objectSize / 2}` : objectShape === 'rectangle' ? `${objectSize}, ${objectSize}` : `${objectSize / 2}, 6`},
            {
              restitution: ${restitution},
              friction: ${friction},
              render: {
                fillStyle: color
              }
            }
          );

          // Add initial velocity for bouncing
          Body.setVelocity(body, {
            x: (Math.random() - 0.5) * ${initialVelocity.x},
            y: ${initialVelocity.y}
          });

          World.add(world, body);
        }`;

      case 'explode':
        return `
        // Exploding objects from center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${objectCount}; i++) {
          const angle = (i / ${objectCount}) * Math.PI * 2;
          const distance = 20 + Math.random() * 30;
          const x = centerX + Math.cos(angle) * distance;
          const y = centerY + Math.sin(angle) * distance;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${objectShape === 'circle' ? 'circle' : objectShape === 'rectangle' ? 'rectangle' : 'polygon'}(
            x, y,
            ${objectShape === 'circle' ? `${objectSize / 2}` : objectShape === 'rectangle' ? `${objectSize}, ${objectSize}` : `${objectSize / 2}, 6`},
            {
              restitution: ${restitution},
              friction: ${friction},
              render: {
                fillStyle: color
              }
            }
          );

          // Explosive velocity
          const force = 0.1 + Math.random() * 0.2;
          Body.setVelocity(body, {
            x: Math.cos(angle) * force * ${initialVelocity.x},
            y: Math.sin(angle) * force * ${initialVelocity.y}
          });

          World.add(world, body);
        }`;

      case 'attract':
        return `
        // Objects attracted to center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${objectCount}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top + Math.random() * rect.height;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${objectShape === 'circle' ? 'circle' : objectShape === 'rectangle' ? 'rectangle' : 'polygon'}(
            x, y,
            ${objectShape === 'circle' ? `${objectSize / 2}` : objectShape === 'rectangle' ? `${objectSize}, ${objectSize}` : `${objectSize / 2}, 6`},
            {
              restitution: ${restitution},
              friction: ${friction},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);

          // Apply attraction force towards center
          setInterval(() => {
            const dx = centerX - body.position.x;
            const dy = centerY - body.position.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance > 10) {
              const force = 0.001;
              Body.applyForce(body, body.position, {
                x: (dx / distance) * force,
                y: (dy / distance) * force
              });
            }
          }, 16);
        }`;

      case 'repel':
        return `
        // Objects repelled from center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${objectCount}; i++) {
          const x = centerX + (Math.random() - 0.5) * 100;
          const y = centerY + (Math.random() - 0.5) * 100;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${objectShape === 'circle' ? 'circle' : objectShape === 'rectangle' ? 'rectangle' : 'polygon'}(
            x, y,
            ${objectShape === 'circle' ? `${objectSize / 2}` : objectShape === 'rectangle' ? `${objectSize}, ${objectSize}` : `${objectSize / 2}, 6`},
            {
              restitution: ${restitution},
              friction: ${friction},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);

          // Apply repulsion force from center
          setInterval(() => {
            const dx = body.position.x - centerX;
            const dy = body.position.y - centerY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < 200 && distance > 0) {
              const force = 0.002;
              Body.applyForce(body, body.position, {
                x: (dx / distance) * force,
                y: (dy / distance) * force
              });
            }
          }, 16);
        }`;

      default:
        return `
        // Default drop effect
        for (let i = 0; i < ${objectCount}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top - 50;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.circle(x, y, ${objectSize / 2}, {
            restitution: ${restitution},
            friction: ${friction},
            render: {
              fillStyle: color
            }
          });

          World.add(world, body);
        }`;
    }
  };

  return `
// Load Matter.js library
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js";
script.onload = function () {
  const { Engine, Render, World, Bodies, Body, Mouse, MouseConstraint } = Matter;
  const ids = ${JSON.stringify(objectIds)};
  const colors = ${JSON.stringify(colors.length > 0 ? colors : ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'])};

  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      function createPhysicsScene() {
        setTimeout(function() {
          // Get element position and size
          const rect = el.getBoundingClientRect();

          // Create physics engine
          const engine = Engine.create();
          const world = engine.world;

          // Set gravity
          engine.world.gravity.y = ${gravity};

          // Create renderer
          const render = Render.create({
            element: document.body,
            engine: engine,
            options: {
              width: window.innerWidth,
              height: window.innerHeight,
              wireframes: false,
              background: 'transparent',
              showAngleIndicator: false,
              showVelocity: false
            }
          });

          // Position renderer over the page
          render.canvas.style.position = 'fixed';
          render.canvas.style.top = '0';
          render.canvas.style.left = '0';
          render.canvas.style.pointerEvents = 'none';
          render.canvas.style.zIndex = '9999';

          // Create boundaries (invisible walls)
          const ground = Bodies.rectangle(window.innerWidth / 2, window.innerHeight + 50, window.innerWidth, 100, { isStatic: true, render: { visible: false } });
          const leftWall = Bodies.rectangle(-50, window.innerHeight / 2, 100, window.innerHeight, { isStatic: true, render: { visible: false } });
          const rightWall = Bodies.rectangle(window.innerWidth + 50, window.innerHeight / 2, 100, window.innerHeight, { isStatic: true, render: { visible: false } });

          World.add(world, [ground, leftWall, rightWall]);

          // Create physics objects based on effect
          ${getPhysicsEffect()}

          // Run the engine and renderer
          Engine.run(engine);
          Render.run(render);

          // Clean up after duration
          setTimeout(() => {
            Render.stop(render);
            Engine.clear(engine);
            render.canvas.remove();
          }, ${duration * 1000});

        }, ${delay * 1000});
      }

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      createPhysicsScene();` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", createPhysicsScene);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim();
};

export const generateRippleCode = (config: RippleConfig): string => {
  const { objectIds, trigger, effect, size, color, opacity, duration, delay, origin, customOrigin, allowMultiple, easing } = config;

  const getRippleSize = () => {
    switch (size) {
      case 'small': return '100';
      case 'medium': return '200';
      case 'large': return '400';
      case 'auto': return 'Math.max(rect.width, rect.height) * 2';
      default: return '200';
    }
  };

  const getRippleEffect = () => {
    const rippleSize = getRippleSize();

    switch (effect) {
      case 'material':
        return `
        // Material Design ripple effect
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.backgroundColor = '${color}';
        ripple.style.opacity = '${opacity}';
        ripple.style.pointerEvents = 'none';
        ripple.style.transform = 'scale(0)';
        ripple.style.zIndex = '1000';

        // Calculate ripple position
        let rippleX, rippleY;
        if ('${origin}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          rippleX = event.clientX - rect.left;
          rippleY = event.clientY - rect.top;
        } else if ('${origin}' === 'custom') {
          const rect = el.getBoundingClientRect();
          rippleX = (${customOrigin.x} / 100) * rect.width;
          rippleY = (${customOrigin.y} / 100) * rect.height;
        } else {
          // Center origin
          const rect = el.getBoundingClientRect();
          rippleX = rect.width / 2;
          rippleY = rect.height / 2;
        }

        const rippleSize = ${rippleSize};
        ripple.style.width = rippleSize + 'px';
        ripple.style.height = rippleSize + 'px';
        ripple.style.left = (rippleX - rippleSize / 2) + 'px';
        ripple.style.top = (rippleY - rippleSize / 2) + 'px';

        el.appendChild(ripple);

        // Animate ripple with better scaling
        gsap.fromTo(ripple,
          {
            scale: 0,
            opacity: ${opacity}
          },
          {
            duration: ${duration},
            scale: 1,
            opacity: 0,
            ease: "${easing}",
            onComplete: function() {
              if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
              }
            }
          }
        );`;

      case 'water':
        return `
        // Water ripple effect with multiple waves
        const createWave = (delay) => {
          const wave = document.createElement('div');
          wave.style.position = 'absolute';
          wave.style.borderRadius = '50%';
          wave.style.border = '2px solid ${color}';
          wave.style.backgroundColor = 'transparent';
          wave.style.opacity = '${opacity}';
          wave.style.pointerEvents = 'none';
          wave.style.transform = 'scale(0)';
          wave.style.zIndex = '1000';

          // Calculate wave position
          let waveX, waveY;
          if ('${origin}' === 'click' && event && event.type === 'click') {
            const rect = el.getBoundingClientRect();
            waveX = event.clientX - rect.left;
            waveY = event.clientY - rect.top;
          } else if ('${origin}' === 'custom') {
            const rect = el.getBoundingClientRect();
            waveX = (${customOrigin.x} / 100) * rect.width;
            waveY = (${customOrigin.y} / 100) * rect.height;
          } else {
            const rect = el.getBoundingClientRect();
            waveX = rect.width / 2;
            waveY = rect.height / 2;
          }

          const waveSize = ${rippleSize};
          wave.style.width = waveSize + 'px';
          wave.style.height = waveSize + 'px';
          wave.style.left = (waveX - waveSize / 2) + 'px';
          wave.style.top = (waveY - waveSize / 2) + 'px';

          el.appendChild(wave);

          gsap.to(wave, {
            duration: ${duration},
            scale: 1,
            opacity: 0,
            ease: "${easing}",
            delay: delay,
            onComplete: function() {
              if (wave.parentNode) {
                wave.parentNode.removeChild(wave);
              }
            }
          });
        };

        // Create multiple waves for water effect with better timing
        createWave(0);
        createWave(${duration * 0.15});
        createWave(${duration * 0.3});`;

      case 'pulse':
        return `
        // Pulse ripple effect
        const pulse = document.createElement('div');
        pulse.style.position = 'absolute';
        pulse.style.borderRadius = '50%';
        pulse.style.backgroundColor = '${color}';
        pulse.style.opacity = '${opacity}';
        pulse.style.pointerEvents = 'none';
        pulse.style.transform = 'scale(1)';
        pulse.style.zIndex = '1000';

        // Calculate pulse position
        let pulseX, pulseY;
        if ('${origin}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          pulseX = event.clientX - rect.left;
          pulseY = event.clientY - rect.top;
        } else if ('${origin}' === 'custom') {
          const rect = el.getBoundingClientRect();
          pulseX = (${customOrigin.x} / 100) * rect.width;
          pulseY = (${customOrigin.y} / 100) * rect.height;
        } else {
          const rect = el.getBoundingClientRect();
          pulseX = rect.width / 2;
          pulseY = rect.height / 2;
        }

        const pulseSize = ${rippleSize};
        pulse.style.width = pulseSize + 'px';
        pulse.style.height = pulseSize + 'px';
        pulse.style.left = (pulseX - pulseSize / 2) + 'px';
        pulse.style.top = (pulseY - pulseSize / 2) + 'px';

        el.appendChild(pulse);

        // Pulse animation
        gsap.to(pulse, {
          duration: ${duration / 2},
          scale: 1.5,
          opacity: ${opacity * 0.7},
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: function() {
            if (pulse.parentNode) {
              pulse.parentNode.removeChild(pulse);
            }
          }
        });`;

      case 'shockwave':
        return `
        // Shockwave ripple effect
        const shockwave = document.createElement('div');
        shockwave.style.position = 'absolute';
        shockwave.style.borderRadius = '50%';
        shockwave.style.border = '3px solid ${color}';
        shockwave.style.backgroundColor = 'transparent';
        shockwave.style.opacity = '${opacity}';
        shockwave.style.pointerEvents = 'none';
        shockwave.style.transform = 'scale(0)';
        shockwave.style.zIndex = '1000';
        shockwave.style.boxShadow = '0 0 20px ${color}';

        // Calculate shockwave position
        let shockX, shockY;
        if ('${origin}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          shockX = event.clientX - rect.left;
          shockY = event.clientY - rect.top;
        } else if ('${origin}' === 'custom') {
          const rect = el.getBoundingClientRect();
          shockX = (${customOrigin.x} / 100) * rect.width;
          shockY = (${customOrigin.y} / 100) * rect.height;
        } else {
          const rect = el.getBoundingClientRect();
          shockX = rect.width / 2;
          shockY = rect.height / 2;
        }

        const shockSize = ${rippleSize};
        shockwave.style.width = shockSize + 'px';
        shockwave.style.height = shockSize + 'px';
        shockwave.style.left = (shockX - shockSize / 2) + 'px';
        shockwave.style.top = (shockY - shockSize / 2) + 'px';

        el.appendChild(shockwave);

        // Enhanced shockwave animation with multiple phases
        gsap.timeline()
          .to(shockwave, {
            duration: ${duration * 0.3},
            scale: 0.3,
            opacity: ${opacity},
            ease: "power2.out"
          })
          .to(shockwave, {
            duration: ${duration * 0.7},
            scale: 1,
            opacity: 0,
            ease: "power3.out",
            onComplete: function() {
              if (shockwave.parentNode) {
                shockwave.parentNode.removeChild(shockwave);
              }
            }
          });`;

      default:
        return `
        // Default material ripple
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.backgroundColor = '${color}';
        ripple.style.opacity = '${opacity}';
        ripple.style.pointerEvents = 'none';
        ripple.style.transform = 'scale(0)';
        ripple.style.zIndex = '1000';

        const rect = el.getBoundingClientRect();
        const rippleSize = ${rippleSize};
        ripple.style.width = rippleSize + 'px';
        ripple.style.height = rippleSize + 'px';
        ripple.style.left = (rect.width / 2 - rippleSize / 2) + 'px';
        ripple.style.top = (rect.height / 2 - rippleSize / 2) + 'px';

        el.appendChild(ripple);

        gsap.to(ripple, {
          duration: ${duration},
          scale: 1,
          opacity: 0,
          ease: "${easing}",
          onComplete: function() {
            if (ripple.parentNode) {
              ripple.parentNode.removeChild(ripple);
            }
          }
        });`;
    }
  };

  return `
(function () {
  const ids = ${JSON.stringify(objectIds)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Set up element for ripple effects
      const originalPosition = el.style.position;
      const originalOverflow = el.style.overflow;

      if (getComputedStyle(el).position === 'static') {
        el.style.position = 'relative';
      }
      el.style.overflow = 'hidden';

      function createRipple(event) {
        setTimeout(function() {
          // Check if multiple ripples are allowed
          if (!${allowMultiple}) {
            // Remove existing ripples
            const existingRipples = el.querySelectorAll('[data-ripple]');
            existingRipples.forEach(ripple => {
              if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
              }
            });
          }

          ${getRippleEffect()}

          // Mark ripple element for cleanup
          if (typeof ripple !== 'undefined') {
            ripple.setAttribute('data-ripple', 'true');
          }
          if (typeof wave !== 'undefined') {
            wave.setAttribute('data-ripple', 'true');
          }
          if (typeof pulse !== 'undefined') {
            pulse.setAttribute('data-ripple', 'true');
          }
          if (typeof shockwave !== 'undefined') {
            shockwave.setAttribute('data-ripple', 'true');
          }
        }, ${delay * 1000});
      }

      ${trigger === 'timeline' ? `
      // Timeline trigger - execute immediately with delay
      createRipple();` : `

      el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", createRipple);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim();
};

export const generateTypewriterCode = (config: TypewriterConfig): string => {
  const {
    objectIds,
    trigger,
    strings,
    typeSpeed,
    backSpeed,
    startDelay,
    backDelay,
    loop,
    loopCount,
    showCursor,
    cursorChar,
    smartBackspace,
    shuffle,
    fadeOut,
    fadeOutDelay
  } = config;

  const typedOptions = {
    strings: strings.length > 0 ? strings : ['Hello World!'],
    typeSpeed,
    backSpeed,
    startDelay,
    backDelay,
    loop,
    loopCount: loop ? loopCount : 1,
    showCursor,
    cursorChar,
    smartBackspace,
    shuffle,
    fadeOut,
    fadeOutDelay,
  };

  // Remove undefined values for cleaner code
  const cleanOptions = Object.fromEntries(
    Object.entries(typedOptions).filter(([_, value]) => value !== undefined && value !== '')
  );

  return `
// Load Typed.js library
var script = document.createElement("script");
script.src = "https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js";
script.onload = function() {
  (function () {
    const ids = ${JSON.stringify(objectIds)};
    const options = ${JSON.stringify(cleanOptions, null, 2)};

    ids.forEach(function (id) {
      const el = document.querySelector('[data-acc-text="' + id + '"]');
      if (el) {
        ${trigger === 'timeline' ? `
        // Timeline trigger - execute immediately with delay
        setTimeout(function() {
          // Clear existing content
          el.innerHTML = '';
          // Initialize Typed.js
          new Typed(el, options);
        }, ${startDelay});` : `

        el.addEventListener("${trigger === 'hover' ? 'mouseenter' : 'click'}", function () {
          // Clear existing content
          el.innerHTML = '';
          // Initialize Typed.js
          new Typed(el, options);
        });`}
      } else {
        console.error("Element with data-acc-text='" + id + "' not found.");
      }
    });
  })();
};
document.head.appendChild(script);`.trim();
};

export const generateFillCode = (config: FillConfig): string => {
  const { objectIds, trigger, origin, direction, duration, delay, easing, stagger, color, shape, borderRadius } = config;

  // Define clip-path patterns for different origins and shapes
  const getClipPath = (origin: string, isStart: boolean, shape: string) => {
    if (shape === 'circle') {
      const circlePatterns: Record<string, { start: string; end: string }> = {
        'bottom-right': {
          start: 'circle(0% at 100% 100%)',
          end: 'circle(150% at 100% 100%)'
        },
        'bottom-left': {
          start: 'circle(0% at 0% 100%)',
          end: 'circle(150% at 0% 100%)'
        },
        'top-right': {
          start: 'circle(0% at 100% 0%)',
          end: 'circle(150% at 100% 0%)'
        },
        'top-left': {
          start: 'circle(0% at 0% 0%)',
          end: 'circle(150% at 0% 0%)'
        },
        'center': {
          start: 'circle(0% at 50% 50%)',
          end: 'circle(150% at 50% 50%)'
        },
        'left': {
          start: 'circle(0% at 0% 50%)',
          end: 'circle(150% at 0% 50%)'
        },
        'right': {
          start: 'circle(0% at 100% 50%)',
          end: 'circle(150% at 100% 50%)'
        },
        'top': {
          start: 'circle(0% at 50% 0%)',
          end: 'circle(150% at 50% 0%)'
        },
        'bottom': {
          start: 'circle(0% at 50% 100%)',
          end: 'circle(150% at 50% 100%)'
        }
      };
      const pattern = circlePatterns[origin] || circlePatterns['center'];
      return isStart ? pattern.start : pattern.end;
    }
    
    if (shape === 'ellipse') {
      const ellipsePatterns: Record<string, { start: string; end: string }> = {
        'bottom-right': {
          start: 'ellipse(0% 0% at 100% 100%)',
          end: 'ellipse(150% 100% at 100% 100%)'
        },
        'bottom-left': {
          start: 'ellipse(0% 0% at 0% 100%)',
          end: 'ellipse(150% 100% at 0% 100%)'
        },
        'top-right': {
          start: 'ellipse(0% 0% at 100% 0%)',
          end: 'ellipse(150% 100% at 100% 0%)'
        },
        'top-left': {
          start: 'ellipse(0% 0% at 0% 0%)',
          end: 'ellipse(150% 100% at 0% 0%)'
        },
        'center': {
          start: 'ellipse(0% 0% at 50% 50%)',
          end: 'ellipse(150% 100% at 50% 50%)'
        },
        'left': {
          start: 'ellipse(0% 0% at 0% 50%)',
          end: 'ellipse(150% 100% at 0% 50%)'
        },
        'right': {
          start: 'ellipse(0% 0% at 100% 50%)',
          end: 'ellipse(150% 100% at 100% 50%)'
        },
        'top': {
          start: 'ellipse(0% 0% at 50% 0%)',
          end: 'ellipse(150% 100% at 50% 0%)'
        },
        'bottom': {
          start: 'ellipse(0% 0% at 50% 100%)',
          end: 'ellipse(150% 100% at 50% 100%)'
        }
      };
      const pattern = ellipsePatterns[origin] || ellipsePatterns['center'];
      return isStart ? pattern.start : pattern.end;
    }
    
    if (shape === 'rounded-rectangle') {
      const radius = borderRadius || 12;
      const roundedPatterns: Record<string, { start: string; end: string }> = {
        'bottom-right': {
          start: `inset(100% 0% 0% 100% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'bottom-left': {
          start: `inset(100% 100% 0% 0% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'top-right': {
          start: `inset(0% 0% 100% 100% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'top-left': {
          start: `inset(0% 100% 100% 0% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'center': {
          start: `inset(50% 50% 50% 50% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'left': {
          start: `inset(0% 100% 0% 0% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'right': {
          start: `inset(0% 0% 0% 100% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'top': {
          start: `inset(0% 0% 100% 0% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        },
        'bottom': {
          start: `inset(100% 0% 0% 0% round ${radius}px)`,
          end: `inset(0% 0% 0% 0% round ${radius}px)`
        }
      };
      const pattern = roundedPatterns[origin] || roundedPatterns['bottom-right'];
      return isStart ? pattern.start : pattern.end;
    }
    
    // Default to rectangle (original polygon patterns)
    const rectanglePatterns: Record<string, { start: string; end: string }> = {
      'bottom-right': {
        start: 'polygon(100% 100%, 100% 100%, 100% 100%, 100% 100%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'bottom-left': {
        start: 'polygon(0% 100%, 0% 100%, 0% 100%, 0% 100%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'top-right': {
        start: 'polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'top-left': {
        start: 'polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'center': {
        start: 'polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'left': {
        start: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'right': {
        start: 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'top': {
        start: 'polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      },
      'bottom': {
        start: 'polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)',
        end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      }
    };
    
    const pattern = rectanglePatterns[origin] || rectanglePatterns['bottom-right'];
    return isStart ? pattern.start : pattern.end;
  };

  const startClip = direction === 'reveal' ? getClipPath(origin, true, shape || 'rectangle') : getClipPath(origin, false, shape || 'rectangle');
  const endClip = direction === 'reveal' ? getClipPath(origin, false, shape || 'rectangle') : getClipPath(origin, true, shape || 'rectangle');

  const triggerFunction = (trigger: string) => {
    switch (trigger) {
      case 'timeline':
        return `
    // Auto-execute on timeline
    gsap.set(pseudoElements, { clipPath: "${startClip}" });
    gsap.to(pseudoElements, {
      clipPath: "${endClip}",
      duration: ${duration},
      delay: ${delay},
      ease: "${easing}",
      stagger: ${stagger}
    });`;
      case 'hover':
        return `
    elements.forEach(function(el, index) {
      const pseudoEl = pseudoElements[index];
      gsap.set(pseudoEl, { clipPath: "${startClip}" });
      
      el.addEventListener("mouseenter", function() {
        gsap.to(pseudoEl, {
          clipPath: "${endClip}",
          duration: ${duration},
          ease: "${easing}"
        });
      });
      
      el.addEventListener("mouseleave", function() {
        gsap.to(pseudoEl, {
          clipPath: "${startClip}",
          duration: ${duration},
          ease: "${easing}"
        });
      });
    });`;
      case 'click':
        return `
    elements.forEach(function(el, index) {
      const pseudoEl = pseudoElements[index];
      gsap.set(pseudoEl, { clipPath: "${startClip}" });
      
      el.addEventListener("click", function() {
        gsap.to(pseudoEl, {
          clipPath: "${endClip}",
          duration: ${duration},
          ease: "${easing}"
        });
      });
    });`;
      default:
        return '';
    }
  };

  return `
var script = document.createElement("script");
script.src = "https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js";
script.onload = function () {
  (function () {
    const ids = ${JSON.stringify(objectIds)};
    const fillColor = "${color}";
    
    const elements = ids.map(function(id) {
      const el = document.querySelector('[data-acc-text="' + id + '"]');
      if (el) {
        // Set up element for pseudo-element
        el.style.position = el.style.position || 'relative';
        el.style.overflow = 'hidden';
        return el;
      } else {
        console.error("Element with data-acc-text='" + id + "' not found.");
        return null;
      }
    }).filter(Boolean);
    
    if (elements.length === 0) {
      console.error("No valid elements found for fill effect.");
      return;
    }
    
    // Create pseudo-elements for each element
    const pseudoElements = elements.map(function(el) {
      const pseudoEl = document.createElement('div');
      pseudoEl.style.position = 'absolute';
      pseudoEl.style.top = '0';
      pseudoEl.style.left = '0';
      pseudoEl.style.right = '0';
      pseudoEl.style.bottom = '0';
      pseudoEl.style.backgroundColor = fillColor;
      pseudoEl.style.clipPath = 'polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)';
      pseudoEl.style.zIndex = '-1';
      pseudoEl.style.pointerEvents = 'none';
      
      // Insert pseudo-element as first child
      el.insertBefore(pseudoEl, el.firstChild);
      return pseudoEl;
    });
    
    ${triggerFunction(trigger)}
  })();
};
document.head.appendChild(script);`.trim();
};
