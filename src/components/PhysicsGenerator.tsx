import { useState, useEffect } from "react";
import {
  generatePhysicsCode,
  type PhysicsConfig,
} from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";
import TargetingMethodSelector from "./TargetingMethodSelector";

export default function PhysicsGenerator() {
  const [config, setConfig] = useState<PhysicsConfig>({
    objectIds: [],
    targetingMethod: "data-model-id",
    trigger: "click",
    effect: "drop",
    objectCount: 10,
    objectShape: "circle",
    objectSize: 20,
    gravity: 1,
    restitution: 0.8,
    friction: 0.1,
    duration: 5,
    delay: 0,
    colors: ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"],
    initialVelocity: {
      x: 50,
      y: -50,
    },
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generatePhysicsCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  const addColor = () => {
    setConfig((prev) => ({
      ...prev,
      colors: [...prev.colors, "#000000"],
    }));
  };

  const updateColor = (index: number, color: string) => {
    setConfig((prev) => ({
      ...prev,
      colors: prev.colors.map((c, i) => (i === index ? color : c)),
    }));
  };

  const removeColor = (index: number) => {
    setConfig((prev) => ({
      ...prev,
      colors: prev.colors.filter((_, i) => i !== index),
    }));
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Physics Effect Generator
      </h2>
      <p className="text-gray-600 mb-6">
        Create realistic physics-based animations using Matter.js. Objects will
        interact with gravity, bounce, and collide naturally.
      </p>

      <div className="space-y-6">
        {/* Object IDs Input */}
        <div>
          <TargetingMethodSelector

            value={config.targetingMethod}

            onChange={(method) =>

              setConfig((prev) => ({ ...prev, targetingMethod: method }))

            }

          />


          <TagInput
            label="Storyline Object IDs"
            placeholder="Enter object ID and press Enter"
            value={config.objectIds}
            onChange={handleObjectIdChange}
          />
        </div>

        {/* Trigger Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Trigger:
          </label>
          <div className="flex flex-wrap gap-3">
            {(["click", "hover", "timeline"] as const).map((triggerType) => (
              <label key={triggerType} className="flex items-center">
                <input
                  type="radio"
                  name="trigger"
                  value={triggerType}
                  checked={config.trigger === triggerType}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      trigger: e.target.value as typeof triggerType,
                    }))
                  }
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="text-sm text-gray-700 capitalize">
                  {triggerType}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Physics Effect Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Physics Effect:
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {(["drop", "bounce", "explode", "attract", "repel"] as const).map(
              (effectType) => (
                <label key={effectType} className="flex items-center">
                  <input
                    type="radio"
                    name="effect"
                    value={effectType}
                    checked={config.effect === effectType}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        effect: e.target.value as typeof effectType,
                      }))
                    }
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="text-sm text-gray-700 capitalize">
                    {effectType}
                  </span>
                </label>
              )
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Drop: Objects fall from above • Bounce: Objects bounce around •
            Explode: Objects burst outward • Attract: Objects pull toward center
            • Repel: Objects push away from center
          </p>
        </div>

        {/* Object Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Object Count: {config.objectCount}
            </label>
            <input
              type="range"
              min="1"
              max="50"
              value={config.objectCount}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  objectCount: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1</span>
              <span>50</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Object Shape:
            </label>
            <div className="flex flex-wrap gap-3">
              {(["circle", "rectangle", "polygon"] as const).map((shape) => (
                <label key={shape} className="flex items-center">
                  <input
                    type="radio"
                    name="objectShape"
                    value={shape}
                    checked={config.objectShape === shape}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        objectShape: e.target.value as typeof shape,
                      }))
                    }
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="text-sm text-gray-700 capitalize">
                    {shape}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Physics Properties */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Object Size: {config.objectSize}px
            </label>
            <input
              type="range"
              min="10"
              max="100"
              value={config.objectSize}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  objectSize: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>10px</span>
              <span>100px</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gravity: {config.gravity}
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={config.gravity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  gravity: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0 (Float)</span>
              <span>2 (Heavy)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bounciness: {config.restitution}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={config.restitution}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  restitution: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0 (No bounce)</span>
              <span>1 (Super bouncy)</span>
            </div>
          </div>
        </div>

        {/* Timing Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration: {config.duration}s
            </label>
            <input
              type="range"
              min="1"
              max="30"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1s</span>
              <span>30s</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay: {config.delay}s
            </label>
            <input
              type="range"
              min="0"
              max="10"
              step="0.1"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0s</span>
              <span>10s</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Friction: {config.friction}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={config.friction}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  friction: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0 (Slippery)</span>
              <span>1 (Sticky)</span>
            </div>
          </div>
        </div>

        {/* Colors Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Object Colors:
          </label>
          <div className="space-y-3">
            {config.colors.map((color, index) => (
              <div key={index} className="flex items-center gap-3">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => updateColor(index, e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={color}
                  onChange={(e) => updateColor(index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="#000000"
                />
                {config.colors.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeColor(index)}
                    className="px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              onClick={addColor}
              className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none"
            >
              + Add Color
            </button>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox
          effectType="physics"
          config={{
            ...config,
            physicsEffect: config.effect, // Map effect to physicsEffect for PreviewBox
          }}
        />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
