import { useState, useEffect } from "react";
import {
  generateMagneticCode,
  type MagneticConfig,
} from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function MagneticGenerator() {
  const [config, setConfig] = useState<MagneticConfig>({
    objectIds: [],
    strength: 0.3,
    distance: 10, // Changed to 10% viewport for better responsiveness
    speed: 0.3,
    returnSpeed: 0.6,
    easing: "ease-out",
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateMagneticCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Magnetic Hover Effect Generator
      </h2>

      <div className="space-y-4">
        {/* Object IDs Input */}
        <div>
          <TagInput
            label="Object IDs"
            value={config.objectIds}
            onChange={handleObjectIdChange}
            placeholder="Enter object IDs (e.g., button1, text2)..."
          />
          {/* <p className="text-sm text-gray-600 mt-1">
            Enter the data-acc-text values of elements you want to make magnetic
          </p> */}
        </div>

        {/* Configuration Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Magnetic Strength:
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={config.strength}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  strength: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Subtle (0.1)</span>
              <span className="font-medium">{config.strength}</span>
              <span>Strong (1.0)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Detection Distance (%):
            </label>
            <input
              type="range"
              min="5"
              max="30"
              step="1"
              value={config.distance}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  distance: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Close (5%)</span>
              <span className="font-medium">{config.distance}%</span>
              <span>Far (30%)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Response Speed:
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={config.speed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  speed: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Fast (0.1s)</span>
              <span className="font-medium">{config.speed}s</span>
              <span>Slow (1.0s)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Return Speed:
            </label>
            <input
              type="range"
              min="0.1"
              max="1.5"
              step="0.1"
              value={config.returnSpeed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  returnSpeed: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Fast (0.1s)</span>
              <span className="font-medium">{config.returnSpeed}s</span>
              <span>Slow (1.5s)</span>
            </div>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Animation Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as MagneticConfig["easing"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ease">Ease</option>
              <option value="ease-in">Ease In</option>
              <option value="ease-out">Ease Out</option>
              <option value="ease-in-out">Ease In Out</option>
              <option value="linear">Linear</option>
            </select>
          </div>
        </div>

        {/* Description */}
        {/* <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            How Magnetic Hover Works:
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>
              • Elements are attracted to the mouse cursor when it gets close
            </li>
            <li>• The closer the cursor, the stronger the magnetic pull</li>
            <li>
              • Elements smoothly return to their original position when the
              cursor moves away
            </li>
            <li>• Perfect for buttons, cards, and interactive elements</li>
          </ul>
        </div> */}
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="magnetic" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
