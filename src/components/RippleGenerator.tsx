import { useState, useEffect } from "react";
import { generateRippleCode, type RippleConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";
import TargetingMethodSelector from "./TargetingMethodSelector";

export default function RippleGenerator() {
  const [config, setConfig] = useState<RippleConfig>({
    objectIds: [],
    targetingMethod: "data-model-id",
    trigger: "click",
    effect: "material",
    size: "auto",
    color: "#3b82f6",
    opacity: 0.25,
    duration: 0.8,
    delay: 0,
    origin: "click",
    customOrigin: {
      x: 50,
      y: 50,
    },
    allowMultiple: true,
    easing: "power2.out",
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateRippleCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Ripple Effect Generator
      </h2>
      <p className="text-gray-600 mb-6">
        Create beautiful ripple effects inspired by Material Design. Perfect for
        buttons, cards, and interactive elements that need visual feedback.
      </p>

      <div className="space-y-6">
        {/* Object IDs Input */}
        <div>
          <TargetingMethodSelector
            value={config.targetingMethod}
            onChange={(method) =>
              setConfig((prev) => ({ ...prev, targetingMethod: method }))
            }
          />

          <TagInput
            label={
              config.targetingMethod === "data-model-id"
                ? "Object IDs"
                : "Accessibility Text Values"
            }
            placeholder={
              config.targetingMethod === "data-model-id"
                ? "e.g. 6mrnVQJ3w4F, 5vcGqLIG41f"
                : "e.g. button1, success-message"
            }
            value={config.objectIds}
            onChange={handleObjectIdChange}
          />
        </div>

        {/* Trigger Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Trigger:
          </label>
          <div className="flex flex-wrap gap-3">
            {(["click", "hover", "timeline"] as const).map((triggerType) => (
              <label key={triggerType} className="flex items-center">
                <input
                  type="radio"
                  name="trigger"
                  value={triggerType}
                  checked={config.trigger === triggerType}
                  onChange={(e) =>
                    setConfig((prev) => ({
                      ...prev,
                      trigger: e.target.value as typeof triggerType,
                    }))
                  }
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="text-sm text-gray-700 capitalize">
                  {triggerType}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Ripple Effect Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Ripple Effect:
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {(["material", "water", "pulse", "shockwave"] as const).map(
              (effectType) => (
                <label key={effectType} className="flex items-center">
                  <input
                    type="radio"
                    name="effect"
                    value={effectType}
                    checked={config.effect === effectType}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        effect: e.target.value as typeof effectType,
                      }))
                    }
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="text-sm text-gray-700 capitalize">
                    {effectType}
                  </span>
                </label>
              )
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Material: Classic Material Design • Water: Multiple expanding waves
            • Pulse: Breathing effect • Shockwave: Explosive ring with glow
          </p>
        </div>

        {/* Size and Appearance */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Ripple Size:
            </label>
            <div className="flex flex-wrap gap-3">
              {(["small", "medium", "large", "auto"] as const).map(
                (sizeType) => (
                  <label key={sizeType} className="flex items-center">
                    <input
                      type="radio"
                      name="size"
                      value={sizeType}
                      checked={config.size === sizeType}
                      onChange={(e) =>
                        setConfig((prev) => ({
                          ...prev,
                          size: e.target.value as typeof sizeType,
                        }))
                      }
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="text-sm text-gray-700 capitalize">
                      {sizeType}
                    </span>
                  </label>
                )
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Ripple Origin:
            </label>
            <div className="flex flex-wrap gap-3">
              {(["center", "click", "custom"] as const).map((originType) => (
                <label key={originType} className="flex items-center">
                  <input
                    type="radio"
                    name="origin"
                    value={originType}
                    checked={config.origin === originType}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        origin: e.target.value as typeof originType,
                      }))
                    }
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="text-sm text-gray-700 capitalize">
                    {originType}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Custom Origin Position (only show if custom is selected) */}
        {config.origin === "custom" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                X Position: {config.customOrigin.x}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={config.customOrigin.x}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    customOrigin: {
                      ...prev.customOrigin,
                      x: parseInt(e.target.value),
                    },
                  }))
                }
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Left (0%)</span>
                <span>Right (100%)</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Y Position: {config.customOrigin.y}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={config.customOrigin.y}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    customOrigin: {
                      ...prev.customOrigin,
                      y: parseInt(e.target.value),
                    },
                  }))
                }
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Top (0%)</span>
                <span>Bottom (100%)</span>
              </div>
            </div>
          </div>
        )}

        {/* Color and Opacity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ripple Color:
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={config.color}
                onChange={(e) =>
                  setConfig((prev) => ({ ...prev, color: e.target.value }))
                }
                className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={config.color}
                onChange={(e) =>
                  setConfig((prev) => ({ ...prev, color: e.target.value }))
                }
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="#2563eb"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Opacity: {config.opacity}
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={config.opacity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  opacity: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Subtle (0.1)</span>
              <span>Opaque (1.0)</span>
            </div>
          </div>
        </div>

        {/* Timing and Animation */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration: {config.duration}s
            </label>
            <input
              type="range"
              min="0.3"
              max="3"
              step="0.1"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Fast (0.3s)</span>
              <span>Slow (3s)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay: {config.delay}s
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>None (0s)</span>
              <span>Long (2s)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Animation Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as typeof config.easing,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="power2.out">Natural (Recommended)</option>
              <option value="power3.out">Smooth</option>
              <option value="back.out(1.7)">Bouncy</option>
              <option value="elastic.out(1, 0.3)">Elastic</option>
              <option value="ease-out">Standard</option>
              <option value="linear">Linear</option>
            </select>
          </div>
        </div>

        {/* Advanced Options */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.allowMultiple}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  allowMultiple: e.target.checked,
                }))
              }
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              Allow Multiple Ripples
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1 ml-6">
            When enabled, multiple ripples can appear simultaneously. When
            disabled, new ripples will replace existing ones.
          </p>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox
          effectType="ripple"
          config={{
            ...config,
            rippleEffect: config.effect, // Map effect to rippleEffect for PreviewBox
            rippleOrigin: config.origin, // Map origin to rippleOrigin for PreviewBox
          }}
        />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
