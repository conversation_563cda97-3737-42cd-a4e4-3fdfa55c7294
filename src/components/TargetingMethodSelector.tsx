import type { TargetingMethod } from "../utils/codeTemplates";

interface TargetingMethodSelectorProps {
  value: TargetingMethod;
  onChange: (method: TargetingMethod) => void;
  label?: string;
  showHelp?: boolean;
}

export default function TargetingMethodSelector({
  value,
  onChange,
  label = "Targeting Method",
  showHelp = true,
}: TargetingMethodSelectorProps) {
  return (
    <div className="space-y-3">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="targetingMethod"
              value="data-model-id"
              checked={value === "data-model-id"}
              onChange={(e) => onChange(e.target.value as TargetingMethod)}
              className="mr-2 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm">
              <strong>Object ID</strong> (Recommended)
            </span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="targetingMethod"
              value="data-acc-text"
              checked={value === "data-acc-text"}
              onChange={(e) => onChange(e.target.value as TargetingMethod)}
              className="mr-2 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm">
              <strong>Accessibility Text</strong> (Legacy)
            </span>
          </label>
        </div>
      </div>

      {showHelp && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="text-xs text-blue-800">
            <div className="font-medium mb-1">Targeting Method Guide:</div>
            <div className="space-y-1">
              <div>
                <strong>Object ID:</strong> Uses Storyline's internal object ID
                (data-model-id). Reliable and won't conflict with accessibility
                settings.
              </div>
              <div>
                <strong>Accessibility Text:</strong> Uses the accessibility text
                field (data-acc-text). May conflict if users add screen reader
                text to objects.
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
