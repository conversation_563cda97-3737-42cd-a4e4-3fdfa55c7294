import { useState, useEffect, useRef } from "react";
import {
  generatePathDrawCode,
  type PathDrawConfig,
} from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function PathDrawGenerator() {
  const [config, setConfig] = useState<PathDrawConfig>({
    objectIds: [],
    trigger: "timeline",
    library: "gsap",
    direction: "forward",
    duration: 5,
    delay: 0,
    easing: "ease",
    pathData:
      "M495.29 15.2104C478.109 16.0871 462.102 21.2475 450.305 34.2814C434.222 52.0498 426.297 77.2663 427.555 100.969C428.017 109.667 429.795 117.685 432.661 125.884C425.432 120.497 417.861 115.684 410.156 111.013C394.142 101.305 379.599 93.8453 360.259 96.6641C344.513 98.9591 332.671 107.521 323.296 120.144C312.694 134.419 307.191 152.517 309.963 170.249C312.993 189.631 325.22 205.222 340.843 216.402C357.531 228.344 377.638 234.422 397.797 237.085C380.367 244.749 365.277 257.823 358.318 275.894C349.62 298.482 351.872 326.025 361.837 347.849C364.142 352.856 366.942 357.621 370.195 362.072C353.508 363.696 337.221 368.203 325.154 380.545C321.772 365.346 315.349 351.111 301.732 342.434C290.556 335.314 276.563 334.272 263.868 337.203C250.241 340.349 238.245 348.561 230.946 360.539C221.761 375.751 219.033 394.002 223.367 411.235C225.036 417.999 227.708 424.316 230.557 430.651C213.205 423.578 193.473 415.257 174.97 423.133C163.675 427.941 154.22 438.343 149.779 449.699C145.251 461.278 144.757 474.55 149.94 485.992C156.908 501.377 171.179 511.014 186.574 516.725C198.054 520.984 209.038 521.941 221.164 521.322C210.173 536.492 203.17 551.621 206.114 570.738C208.537 586.471 215.93 601.442 228.964 610.936C240.989 619.696 253.778 619.403 267.931 620.637C273.772 621.146 279.937 622.256 285.767 621.243C296.849 619.318 306.515 611.166 312.634 602.047C317.799 594.348 321.357 585.098 323.54 576.121C332.884 592.623 349.394 609.108 368.158 614.281C375.975 616.436 383.748 615.495 390.748 611.439C399.697 606.254 406.776 597.357 410.674 587.877C425.228 601.917 437.268 618.06 449.199 634.307C498.669 701.67 522.963 763.688 535.576 846.362C537.001 855.478 537.971 864.66 538.482 873.872C539.03 883.882 539.083 894.05 540.205 904.008C537.098 898.746 532.82 893.1 530.575 887.5C528.478 882.271 528.287 875.961 526.986 870.451C524.231 859.234 521.136 848.103 517.705 837.075C513.422 822.912 508.968 808.481 503.158 794.866C491.353 767.196 465.442 726.975 440.888 709.953C431.492 703.439 420.337 698.838 409.307 695.965C377.246 687.616 335.482 693.258 306.948 710.257C306.021 710.802 305.111 711.374 304.218 711.974C317.498 713.857 331.268 715.112 343.455 721.134C349.902 724.32 355.862 728.532 361.782 732.593C396.483 756.394 424.839 787.538 453.573 817.937C479.882 845.769 506.103 873.708 529.178 904.349C532.417 908.65 537.504 914.814 538.629 920.153C539.868 926.033 536.921 941.957 536.257 948.957C534.623 966.2 532.591 983.695 532.346 1001.02C532.304 1003.99 532.166 1010 535.855 1010.9C537.075 1011.2 538.733 1010.68 539.661 1009.92C540.795 1009 541.382 1007.67 541.891 1006.34C543.668 1001.68 544.847 996.577 546.267 991.788C548.81 983.216 551.478 974.752 554.584 966.364C559.572 952.898 565.287 937.985 572.732 925.726C576.48 919.554 581.08 914.374 586.42 909.539C602.362 911.398 627.113 902.881 641.302 895.542C648.85 891.57 655.995 886.876 662.638 881.527C677.96 869.33 689.948 852.337 699.976 835.632C713.147 813.692 727.129 779.449 750.372 766.717C763.588 759.477 776.861 758.526 790.649 753.926C783.656 751.62 776.064 750.684 768.766 749.8C736.886 745.936 699.566 748.172 670.521 762.813C661.317 767.453 652.577 773.327 644.565 779.812C639.658 783.922 634.861 788.011 630.52 792.736C609.764 815.33 593.636 847.887 583.507 876.622C580.704 884.576 577.152 893.23 576.365 901.677C576.208 903.364 576.168 905.073 576.155 906.768C570.917 913.25 566.641 919.646 562.123 926.623C565.328 906.222 568.345 885.792 571.174 865.335C572.605 855.253 573.576 844.982 575.597 835.004C577.925 823.507 581.259 812.251 584.58 801.01C595.67 763.976 609.642 727.867 626.367 693.013C630.469 684.426 634.358 675.56 639.002 667.261C642.245 661.463 646.247 656.003 649.882 650.44C655.206 642.165 660.456 633.843 665.633 625.475C667.498 634.273 669.987 645.542 677.96 650.902C688.644 658.084 705.895 659.29 718.163 656.606C730.536 653.899 740.64 646.061 747.313 635.405C748.477 633.546 749.545 631.616 750.611 629.699C757.089 642.339 767.423 656.228 781.513 660.831C792.595 664.452 805.386 661.869 815.537 656.639C826.942 650.764 837.102 640.424 840.945 627.987C844.273 617.216 841.733 606.005 836.541 596.256C831.688 587.144 824.604 580.178 817.259 573.1C836.45 571.374 854.179 564.796 866.881 549.636C874.185 540.918 879.435 530.194 878.298 518.575C876.614 501.358 863.525 485.589 850.603 475.074C841.695 467.826 831.629 462.116 820.354 459.62C829.147 447.25 836.998 435.607 834.384 419.728C832.292 407.017 824.236 395.35 813.769 388.011C803.202 380.601 789.486 377.048 776.72 379.329C757.393 382.782 742.023 395.202 731.192 411.076C729.074 414.178 727.206 417.442 725.603 420.838C719.278 401.389 714.393 381.551 694.589 371.593C685.271 366.908 675.52 366.371 665.668 369.682C651.744 374.36 638.373 385.1 631.795 398.358C623.969 414.13 628.243 431.615 633.706 447.426C635.675 453.125 638.26 458.482 641.126 463.776C625.539 463.388 611.589 465.812 599.936 477.008C589.416 487.116 577.38 509.491 577.082 524.318C576.733 541.696 593.085 565.679 604.668 577.604C612.896 586.077 621.187 590.888 633.2 590.931C647.383 590.983 657.417 585.204 670.327 581.118C665.041 597.552 653.957 611.076 644.309 625.146C638.149 634.151 632.17 643.278 626.374 652.522C620.588 661.629 616.154 671.208 611.441 680.884C602.156 699.947 593.808 718.877 585.983 738.584C582.17 748.188 577.586 758.192 575.133 768.24C572.674 674.536 555.866 580.837 533.14 490.064C529.832 476.852 525.974 463.794 522.454 450.639C516.375 427.547 509.323 404.722 501.319 382.225C498.953 375.715 496.801 368.568 493.571 362.442C496.856 357.539 500.185 352.674 503.123 347.552C506.418 355.228 509.538 362.99 513.62 370.293C522.639 386.429 537.968 404.698 555.64 411.608C565.628 415.513 576.033 414.887 585.77 410.609C601.979 403.489 620.717 384.803 627.01 368.123C632.692 353.063 632.417 339.902 626.418 325.061C646.01 332.171 670.231 337.199 690.051 327.898C705.567 320.617 717.873 303.423 723.397 287.579C728.106 274.073 728.644 259.305 722.337 246.203C714.746 230.431 698.661 220.981 682.649 215.521C667.774 210.448 652.764 208.831 637.149 208.494C657.822 197.631 675.464 183.091 682.704 160.058C687.626 144.397 687.962 122.848 680.233 108.008C673.507 95.0943 660.982 87.6337 647.448 83.4166C631.964 78.5923 609.053 75.6432 593.167 78.8787C586.052 80.3276 579.243 83.1824 572.839 86.5556C569.546 88.3052 566.314 90.1679 563.149 92.1403C566.112 73.6631 566.23 56.6858 554.799 40.9018C544.764 27.0449 528.734 18.5769 512.009 16.0711C506.472 15.2908 500.877 15.0028 495.29 15.2104Z",
    allPaths: undefined,
    loop: false,
    stagger: 0.2,
  });
  const [generatedCode, setGeneratedCode] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === "image/svg+xml") {
      const reader = new FileReader();
      reader.onload = (e) => {
        const svgContent = e.target?.result as string;
        // Extract ALL path data from SVG
        const pathMatches = svgContent.match(/<path[^>]*d="([^"]*)"[^>]*>/g);
        if (pathMatches && pathMatches.length > 0) {
          // Extract and process paths to handle complex compound paths
          const allPaths: string[] = [];

          pathMatches.forEach((match) => {
            const dMatch = match.match(/d="([^"]*)"/);
            if (dMatch) {
              const pathData = dMatch[1];

              // Split compound paths by Z/z commands (path closures)
              // This handles cases where background rectangles and drawing paths are combined
              const subPaths = pathData
                .split(/[Zz]/)
                .filter((p) => p.trim().length > 0);

              subPaths.forEach((subPath) => {
                const trimmedPath = subPath.trim();
                if (trimmedPath.length === 0) return;

                // Add Z back to close the path if it was removed
                const completePath =
                  trimmedPath +
                  (trimmedPath.endsWith("Z") || trimmedPath.endsWith("z")
                    ? ""
                    : "Z");

                // Filter out simple rectangles that are likely backgrounds/borders
                // More precise pattern matching for simple rectangles
                const isSimpleRect =
                  /^M\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L?\s*\d*\s*\d*\s*$/i.test(
                    trimmedPath
                  );

                // Also check for very short paths that are likely not drawable content
                const isTooShort = trimmedPath.length < 20;

                // Check if path contains only basic move and line commands (likely a simple shape)
                const isOnlyMoveAndLine = /^[ML\d\s.,]+$/i.test(trimmedPath);

                // Keep paths that are complex enough to be drawable content
                if (!isSimpleRect && !isTooShort && !isOnlyMoveAndLine) {
                  allPaths.push(completePath);
                }
              });
            }
          });

          // If no complex paths found, try a more lenient approach
          if (allPaths.length === 0) {
            pathMatches.forEach((match) => {
              const dMatch = match.match(/d="([^"]*)"/);
              if (dMatch) {
                const pathData = dMatch[1];
                // Only filter out very obvious background rectangles
                const isObviousBackground =
                  /^M\s*0\s*0\s*L\s*\d+\s*0\s*L\s*\d+\s*\d+\s*L\s*0\s*\d+\s*L?\s*0\s*0\s*Z?$/i.test(
                    pathData.trim()
                  );

                if (!isObviousBackground && pathData.trim().length > 10) {
                  allPaths.push(pathData);
                }
              }
            });
          }

          if (allPaths.length > 0) {
            // Store all paths and use the first one as the main pathData for display
            setConfig((prev) => ({
              ...prev,
              pathData: allPaths[0],
              allPaths: allPaths,
            }));
          } else {
            alert(
              "No drawable paths found in the SVG file. The file may only contain background elements or very simple shapes."
            );
          }
        } else {
          alert(
            "No valid path data found in the SVG file. Please ensure the SVG contains a <path> element with a 'd' attribute."
          );
        }
      };
      reader.readAsText(file);
    } else {
      alert("Please select a valid SVG file.");
    }
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generatePathDrawCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  // Sample path options
  const samplePaths = {
    wave: "M10,150 C10,150 100,25 190,150 C280,275 370,150 370,150",
    heart:
      "M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z",
    star: "M12,2l3.09,6.26L22,9.27l-5,4.87 L18.18,21L12,17.77L5.82,21L7,14.14L2,9.27l6.91-1.01L12,2z",
    circle: "M50,10 A40,40 0 1,1 49.9,10",
    arrow: "M10,50 L50,10 L50,30 L90,30 L90,70 L50,70 L50,90 Z",
    flower:
      "M495.29 15.2104C478.109 16.0871 462.102 21.2475 450.305 34.2814C434.222 52.0498 426.297 77.2663 427.555 100.969C428.017 109.667 429.795 117.685 432.661 125.884C425.432 120.497 417.861 115.684 410.156 111.013C394.142 101.305 379.599 93.8453 360.259 96.6641C344.513 98.9591 332.671 107.521 323.296 120.144C312.694 134.419 307.191 152.517 309.963 170.249C312.993 189.631 325.22 205.222 340.843 216.402C357.531 228.344 377.638 234.422 397.797 237.085C380.367 244.749 365.277 257.823 358.318 275.894C349.62 298.482 351.872 326.025 361.837 347.849C364.142 352.856 366.942 357.621 370.195 362.072C353.508 363.696 337.221 368.203 325.154 380.545C321.772 365.346 315.349 351.111 301.732 342.434C290.556 335.314 276.563 334.272 263.868 337.203C250.241 340.349 238.245 348.561 230.946 360.539C221.761 375.751 219.033 394.002 223.367 411.235C225.036 417.999 227.708 424.316 230.557 430.651C213.205 423.578 193.473 415.257 174.97 423.133C163.675 427.941 154.22 438.343 149.779 449.699C145.251 461.278 144.757 474.55 149.94 485.992C156.908 501.377 171.179 511.014 186.574 516.725C198.054 520.984 209.038 521.941 221.164 521.322C210.173 536.492 203.17 551.621 206.114 570.738C208.537 586.471 215.93 601.442 228.964 610.936C240.989 619.696 253.778 619.403 267.931 620.637C273.772 621.146 279.937 622.256 285.767 621.243C296.849 619.318 306.515 611.166 312.634 602.047C317.799 594.348 321.357 585.098 323.54 576.121C332.884 592.623 349.394 609.108 368.158 614.281C375.975 616.436 383.748 615.495 390.748 611.439C399.697 606.254 406.776 597.357 410.674 587.877C425.228 601.917 437.268 618.06 449.199 634.307C498.669 701.67 522.963 763.688 535.576 846.362C537.001 855.478 537.971 864.66 538.482 873.872C539.03 883.882 539.083 894.05 540.205 904.008C537.098 898.746 532.82 893.1 530.575 887.5C528.478 882.271 528.287 875.961 526.986 870.451C524.231 859.234 521.136 848.103 517.705 837.075C513.422 822.912 508.968 808.481 503.158 794.866C491.353 767.196 465.442 726.975 440.888 709.953C431.492 703.439 420.337 698.838 409.307 695.965C377.246 687.616 335.482 693.258 306.948 710.257C306.021 710.802 305.111 711.374 304.218 711.974C317.498 713.857 331.268 715.112 343.455 721.134C349.902 724.32 355.862 728.532 361.782 732.593C396.483 756.394 424.839 787.538 453.573 817.937C479.882 845.769 506.103 873.708 529.178 904.349C532.417 908.65 537.504 914.814 538.629 920.153C539.868 926.033 536.921 941.957 536.257 948.957C534.623 966.2 532.591 983.695 532.346 1001.02C532.304 1003.99 532.166 1010 535.855 1010.9C537.075 1011.2 538.733 1010.68 539.661 1009.92C540.795 1009 541.382 1007.67 541.891 1006.34C543.668 1001.68 544.847 996.577 546.267 991.788C548.81 983.216 551.478 974.752 554.584 966.364C559.572 952.898 565.287 937.985 572.732 925.726C576.48 919.554 581.08 914.374 586.42 909.539C602.362 911.398 627.113 902.881 641.302 895.542C648.85 891.57 655.995 886.876 662.638 881.527C677.96 869.33 689.948 852.337 699.976 835.632C713.147 813.692 727.129 779.449 750.372 766.717C763.588 759.477 776.861 758.526 790.649 753.926C783.656 751.62 776.064 750.684 768.766 749.8C736.886 745.936 699.566 748.172 670.521 762.813C661.317 767.453 652.577 773.327 644.565 779.812C639.658 783.922 634.861 788.011 630.52 792.736C609.764 815.33 593.636 847.887 583.507 876.622C580.704 884.576 577.152 893.23 576.365 901.677C576.208 903.364 576.168 905.073 576.155 906.768C570.917 913.25 566.641 919.646 562.123 926.623C565.328 906.222 568.345 885.792 571.174 865.335C572.605 855.253 573.576 844.982 575.597 835.004C577.925 823.507 581.259 812.251 584.58 801.01C595.67 763.976 609.642 727.867 626.367 693.013C630.469 684.426 634.358 675.56 639.002 667.261C642.245 661.463 646.247 656.003 649.882 650.44C655.206 642.165 660.456 633.843 665.633 625.475C667.498 634.273 669.987 645.542 677.96 650.902C688.644 658.084 705.895 659.29 718.163 656.606C730.536 653.899 740.64 646.061 747.313 635.405C748.477 633.546 749.545 631.616 750.611 629.699C757.089 642.339 767.423 656.228 781.513 660.831C792.595 664.452 805.386 661.869 815.537 656.639C826.942 650.764 837.102 640.424 840.945 627.987C844.273 617.216 841.733 606.005 836.541 596.256C831.688 587.144 824.604 580.178 817.259 573.1C836.45 571.374 854.179 564.796 866.881 549.636C874.185 540.918 879.435 530.194 878.298 518.575C876.614 501.358 863.525 485.589 850.603 475.074C841.695 467.826 831.629 462.116 820.354 459.62C829.147 447.25 836.998 435.607 834.384 419.728C832.292 407.017 824.236 395.35 813.769 388.011C803.202 380.601 789.486 377.048 776.72 379.329C757.393 382.782 742.023 395.202 731.192 411.076C729.074 414.178 727.206 417.442 725.603 420.838C719.278 401.389 714.393 381.551 694.589 371.593C685.271 366.908 675.52 366.371 665.668 369.682C651.744 374.36 638.373 385.1 631.795 398.358C623.969 414.13 628.243 431.615 633.706 447.426C635.675 453.125 638.26 458.482 641.126 463.776C625.539 463.388 611.589 465.812 599.936 477.008C589.416 487.116 577.38 509.491 577.082 524.318C576.733 541.696 593.085 565.679 604.668 577.604C612.896 586.077 621.187 590.888 633.2 590.931C647.383 590.983 657.417 585.204 670.327 581.118C665.041 597.552 653.957 611.076 644.309 625.146C638.149 634.151 632.17 643.278 626.374 652.522C620.588 661.629 616.154 671.208 611.441 680.884C602.156 699.947 593.808 718.877 585.983 738.584C582.17 748.188 577.586 758.192 575.133 768.24C572.674 674.536 555.866 580.837 533.14 490.064C529.832 476.852 525.974 463.794 522.454 450.639C516.375 427.547 509.323 404.722 501.319 382.225C498.953 375.715 496.801 368.568 493.571 362.442C496.856 357.539 500.185 352.674 503.123 347.552C506.418 355.228 509.538 362.99 513.62 370.293C522.639 386.429 537.968 404.698 555.64 411.608C565.628 415.513 576.033 414.887 585.77 410.609C601.979 403.489 620.717 384.803 627.01 368.123C632.692 353.063 632.417 339.902 626.418 325.061C646.01 332.171 670.231 337.199 690.051 327.898C705.567 320.617 717.873 303.423 723.397 287.579C728.106 274.073 728.644 259.305 722.337 246.203C714.746 230.431 698.661 220.981 682.649 215.521C667.774 210.448 652.764 208.831 637.149 208.494C657.822 197.631 675.464 183.091 682.704 160.058C687.626 144.397 687.962 122.848 680.233 108.008C673.507 95.0943 660.982 87.6337 647.448 83.4166C631.964 78.5923 609.053 75.6432 593.167 78.8787C586.052 80.3276 579.243 83.1824 572.839 86.5556C569.546 88.3052 566.314 90.1679 563.149 92.1403C566.112 73.6631 566.23 56.6858 554.799 40.9018C544.764 27.0449 528.734 18.5769 512.009 16.0711C506.472 15.2908 500.877 15.0028 495.29 15.2104Z",
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        SVG Path Drawing Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. content1, text2, image3"
          label="Object IDs"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="timeline">Timeline Start</option>
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Animation Library:
            </label>
            <select
              value={config.library}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  library: e.target.value as "gsap" | "anime",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="gsap">GSAP</option>
              <option value="anime">Anime.js</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direction:
            </label>
            <select
              value={config.direction}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  direction: e.target.value as "forward" | "reverse",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="forward">Draw Forward</option>
              <option value="reverse">Draw Reverse</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0.1"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 2,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as
                    | "ease"
                    | "ease-in"
                    | "ease-out"
                    | "ease-in-out"
                    | "linear",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ease">Ease</option>
              <option value="ease-in">Ease In</option>
              <option value="ease-out">Ease Out</option>
              <option value="ease-in-out">Ease In-Out</option>
              <option value="linear">Linear</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="loop"
              checked={config.loop}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, loop: e.target.checked }))
              }
              className="mr-2"
            />
            <label htmlFor="loop" className="text-sm font-medium text-gray-700">
              Loop Animation
            </label>
          </div>
        </div>

        {/* Advanced Configuration */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stagger Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              value={config.stagger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  stagger: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Delay between multiple paths (for multiple path SVGs)
            </p>
          </div>
        </div>

        {/* Path Configuration Section */}
        <div className="border-t pt-6 mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Path Configuration
          </h3>

          {/* Sample Paths */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sample Paths:
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.entries(samplePaths).map(([name, path]) => (
                <button
                  key={name}
                  onClick={() =>
                    setConfig((prev) => ({ ...prev, pathData: path }))
                  }
                  className={`px-3 py-2 text-sm rounded-md transition-colors capitalize ${
                    config.pathData === path
                      ? "bg-blue-100 border-2 border-blue-500 text-blue-700"
                      : "bg-gray-100 hover:bg-gray-200 border-2 border-transparent"
                  }`}
                >
                  {name}
                </button>
              ))}
            </div>
          </div>

          {/* File Upload */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload SVG File:
            </label>
            <input
              ref={fileInputRef}
              type="file"
              accept=".svg"
              onChange={handleFileUpload}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Upload an SVG file to extract path data automatically
              {config.allPaths && config.allPaths.length > 1 && (
                <span className="text-green-600 font-medium">
                  {" "}
                  • {config.allPaths.length} paths found
                </span>
              )}
            </p>
          </div>

          {/* Manual Path Data */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              SVG Path Data (d attribute):
            </label>
            <textarea
              value={config.pathData}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, pathData: e.target.value }))
              }
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
              placeholder="M10,150 C10,150 100,25 190,150 C280,275 370,150 370,150"
            />
            <p className="text-xs text-gray-500 mt-1">
              Enter SVG path data or use the sample paths above
            </p>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox
          effectType="pathdraw"
          config={{ ...config, pathDirection: config.direction }}
          key={`${config.pathData}-${config.allPaths?.length || 0}-${
            config.duration
          }-${config.stagger}`} // Force re-render when animation data changes
        />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
