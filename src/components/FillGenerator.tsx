import { useState, useEffect } from "react";
import { generateFillCode, type FillConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function FillGenerator() {
  const [config, setConfig] = useState<FillConfig>({
    objectIds: [],
    trigger: "timeline",
    origin: "bottom-right",
    direction: "reveal",
    duration: 1,
    delay: 0,
    easing: "power2.out",
    stagger: 0.1,
    color: "#3b82f6",
    shape: "rectangle",
    borderRadius: 12,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateFillCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Fill Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. content1, text2, image3"
          label="Object IDs"
        />

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="timeline">Timeline Start</option>
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Origin Point:
            </label>
            <select
              value={config.origin}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  origin: e.target.value as FillConfig["origin"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="bottom-right">Bottom Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="top-right">Top Right</option>
              <option value="top-left">Top Left</option>
              <option value="center">Center</option>
              <option value="left">Left</option>
              <option value="right">Right</option>
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direction:
            </label>
            <select
              value={config.direction}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  direction: e.target.value as "reveal" | "conceal",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="reveal">Reveal</option>
              <option value="conceal">Conceal</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shape:
            </label>
            <select
              value={config.shape}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  shape: e.target.value as FillConfig["shape"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="rectangle">Rectangle</option>
              <option value="circle">Circle</option>
              <option value="rounded-rectangle">Rounded Rectangle</option>
              <option value="ellipse">Ellipse</option>
            </select>
          </div>
        </div>

        {config.shape === "rounded-rectangle" && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Border Radius (px):
            </label>
            <input
              type="number"
              min="0"
              max="50"
              step="1"
              value={config.borderRadius}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  borderRadius: parseInt(e.target.value) || 12,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Match this value to your object's corner radius in Storyline
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0.1"
              max="10"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="10"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as FillConfig["easing"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="power2.out">Power2 Out</option>
              <option value="power3.out">Power3 Out</option>
              <option value="back.out(1.7)">Back Out</option>
              <option value="elastic.out(1, 0.3)">Elastic Out</option>
              <option value="ease-out">Ease Out</option>
              <option value="linear">Linear</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stagger (seconds):
            </label>
            <input
              type="number"
              step="0.05"
              min="0"
              max="2"
              value={config.stagger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  stagger: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Time between each element (for multiple objects)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fill Color:
            </label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                value={config.color}
                onChange={(e) =>
                  setConfig((prev) => ({ ...prev, color: e.target.value }))
                }
                className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={config.color}
                onChange={(e) =>
                  setConfig((prev) => ({ ...prev, color: e.target.value }))
                }
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="#3b82f6"
              />
            </div>
          </div>
        </div>

        <div className="bg-blue-50 p-4 rounded-md">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            Effect Preview
          </h3>
          <p className="text-sm text-blue-700">
            <strong>Origin:</strong> {config.origin.replace("-", " ")} |{" "}
            <strong>Direction:</strong> {config.direction} |{" "}
            <strong>Duration:</strong> {config.duration}s |{" "}
            <strong>Trigger:</strong> {config.trigger} |{" "}
            <strong>Shape:</strong> {config.shape.replace("-", " ")}
          </p>
          <div className="flex items-center gap-2 mt-2">
            <strong className="text-sm text-blue-900">Fill Color:</strong>
            <div
              className="w-4 h-4 rounded border border-gray-300"
              style={{ backgroundColor: config.color }}
            />
            <span className="text-sm text-blue-700">{config.color}</span>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="fill" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
