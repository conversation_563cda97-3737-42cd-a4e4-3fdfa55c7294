import { useState, useEffect } from "react";
import {
  generateConfettiCode,
  type ConfettiConfig,
} from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function ConfettiGenerator() {
  const [config, setConfig] = useState<ConfettiConfig>({
    objectIds: [],
    trigger: "click",
    particleCount: 50,
    colors: ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"],
    shapes: ["square", "circle"],
    duration: 3,
    delay: 0,
    origin: {
      x: 0.5,
      y: 0.5,
    },
    spread: 45,
    angle: 90,
    gravity: 1,
    startVelocity: 45,
    scalar: 1,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateConfettiCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  const handleColorChange = (index: number, color: string) => {
    const newColors = [...config.colors];
    newColors[index] = color;
    setConfig((prev) => ({ ...prev, colors: newColors }));
  };

  const addColor = () => {
    setConfig((prev) => ({
      ...prev,
      colors: [...prev.colors, "#ff0000"],
    }));
  };

  const removeColor = (index: number) => {
    setConfig((prev) => ({
      ...prev,
      colors: prev.colors.filter((_, i) => i !== index),
    }));
  };

  const toggleShape = (
    shape:
      | "square"
      | "circle"
      | "star"
      | "triangle"
      | "heart"
      | "plus"
      | "ribbon"
      | "squiggle"
      | "streamer"
  ) => {
    setConfig((prev) => {
      const shapes = prev.shapes.includes(shape)
        ? prev.shapes.filter((s) => s !== shape)
        : [...prev.shapes, shape];
      return { ...prev, shapes };
    });
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Confetti Effect Generator
      </h2>

      <div className="space-y-6">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. button1, success-message, celebration"
          label="Object IDs"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
              <option value="timeline">Timeline Start</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Particle Count:
            </label>
            <input
              type="number"
              min="1"
              max="500"
              value={config.particleCount}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  particleCount: parseInt(e.target.value) || 50,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.5"
              min="0.5"
              max="10"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 3,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spread (degrees):
            </label>
            <input
              type="number"
              min="0"
              max="360"
              value={config.spread}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  spread: parseInt(e.target.value) || 45,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Launch Angle (degrees):
            </label>
            <input
              type="number"
              min="0"
              max="360"
              value={config.angle}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  angle: parseInt(e.target.value) || 90,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gravity:
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="2"
              value={config.gravity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  gravity: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Velocity:
            </label>
            <input
              type="number"
              min="1"
              max="100"
              value={config.startVelocity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  startVelocity: parseInt(e.target.value) || 45,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Origin X (0-1):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="1"
              value={config.origin.x}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  origin: {
                    ...prev.origin,
                    x: parseFloat(e.target.value) || 0.5,
                  },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Origin Y (0-1):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="1"
              value={config.origin.y}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  origin: {
                    ...prev.origin,
                    y: parseFloat(e.target.value) || 0.5,
                  },
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Particle Size:
          </label>
          <input
            type="number"
            step="0.1"
            min="0.1"
            max="3"
            value={config.scalar}
            onChange={(e) =>
              setConfig((prev) => ({
                ...prev,
                scalar: parseFloat(e.target.value) || 1,
              }))
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Shapes Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Particle Shapes:
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            {(
              [
                "square",
                "circle",
                "star",
                "triangle",
                "heart",
                "plus",
                "ribbon",
                "squiggle",
                "streamer",
              ] as const
            ).map((shape) => (
              <label key={shape} className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.shapes.includes(shape)}
                  onChange={() => toggleShape(shape)}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700 capitalize">
                  {shape}
                </span>
              </label>
            ))}
          </div>
          <p className="text-xs text-gray-500">
            Multiple shapes will be randomly mixed in the confetti effect.
            Includes basic shapes (square, circle, star) and custom shapes
            (triangle, heart, plus, ribbon, squiggle, streamer).
          </p>
        </div>

        {/* Colors Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Confetti Colors:
          </label>
          <div className="space-y-2">
            {config.colors.map((color, index) => (
              <div key={index} className="flex items-center gap-2">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => handleColorChange(index, e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={color}
                  onChange={(e) => handleColorChange(index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="#ff0000"
                />
                {config.colors.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeColor(index)}
                    className="px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              onClick={addColor}
              className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none"
            >
              + Add Color
            </button>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="confetti" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
