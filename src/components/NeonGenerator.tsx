import { useState, useEffect } from "react";
import { generateNeonCode, type NeonConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function NeonGenerator() {
  const [config, setConfig] = useState<NeonConfig>({
    objectIds: [],
    trigger: "click",
    effect: "glow",
    duration: 2,
    delay: 0,
    color: "#00ffff",
    intensity: 2,
    blurRadius: 1, // Changed to 1% for better responsiveness
    spreadRadius: 2, // Changed to 2% for better responsiveness
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateNeonCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Neon Glow Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. card1, card2, buttonMain"
          label="Object IDs"
        />

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">Click</option>
              <option value="hover">Hover</option>
              <option value="timeline">Timeline</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Effect Type:
            </label>
            <select
              value={config.effect}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  effect: e.target.value as
                    | "glow"
                    | "pulse"
                    | "flicker"
                    | "rainbow",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="glow">Static Glow</option>
              <option value="pulse">Pulse</option>
              <option value="flicker">Flicker</option>
              <option value="rainbow">Rainbow</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 2,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0.1"
              max="10"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="10"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Neon Color:
            </label>
            <input
              type="color"
              value={config.color}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  color: e.target.value,
                }))
              }
              className="w-full h-10 px-1 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Intensity:
            </label>
            <input
              type="number"
              value={config.intensity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  intensity: parseFloat(e.target.value) || 2,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0.5"
              max="5"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Blur Radius (%):
            </label>
            <input
              type="number"
              value={config.blurRadius}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  blurRadius: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0.1"
              max="5"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spread Radius (%):
            </label>
            <input
              type="number"
              value={config.spreadRadius}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  spreadRadius: parseFloat(e.target.value) || 2,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="10"
              step="0.1"
            />
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="neon" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
