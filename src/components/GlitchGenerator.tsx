import { useState, useEffect } from "react";
import { generateGlitchCode, type GlitchConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function GlitchGenerator() {
  const [config, setConfig] = useState<GlitchConfig>({
    objectIds: [],
    trigger: "click",
    effect: "digital",
    duration: 1,
    delay: 0,
    intensity: 1, // Changed to 1% for better responsiveness
    speed: 5,
    iterations: 3,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateGlitchCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Glitch Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. card1, card2, buttonMain"
          label="Object IDs"
        />

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">Click</option>
              <option value="hover">Hover</option>
              <option value="timeline">Timeline</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Effect Type:
            </label>
            <select
              value={config.effect}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  effect: e.target.value as
                    | "digital"
                    | "rgb"
                    | "shake"
                    | "corrupt",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="digital">Digital</option>
              <option value="rgb">RGB Split</option>
              <option value="shake">Shake</option>
              <option value="corrupt">Corrupt</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0.1"
              max="5"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="10"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Intensity:
            </label>
            <input
              type="number"
              value={config.intensity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  intensity: parseFloat(e.target.value) || 10,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="50"
              step="1"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Speed:
            </label>
            <input
              type="number"
              value={config.speed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  speed: parseFloat(e.target.value) || 5,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="20"
              step="1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Iterations:
            </label>
            <input
              type="number"
              value={config.iterations}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  iterations: parseInt(e.target.value) || 3,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="10"
              step="1"
            />
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="glitch" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
