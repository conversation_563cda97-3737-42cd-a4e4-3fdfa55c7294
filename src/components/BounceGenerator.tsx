import { useState, useEffect } from "react";
import { generateBounceCode, type BounceConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function BounceGenerator() {
  const [config, setConfig] = useState<BounceConfig>({
    objectIds: [],
    trigger: "click",
    height: 5, // Changed to 5% viewport height for better responsiveness
    bounces: 3,
    duration: 1,
    delay: 0,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateBounceCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Bounce Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. button1, icon2, element3"
          label="Object IDs"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
              <option value="timeline">Timeline Start</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bounce Height (%):
            </label>
            <input
              type="number"
              value={config.height}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  height: parseInt(e.target.value) || 5,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of Bounces:
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={config.bounces}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  bounces: parseInt(e.target.value) || 3,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="bounce" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
