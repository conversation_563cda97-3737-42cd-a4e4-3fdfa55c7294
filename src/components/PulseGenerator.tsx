import { useState, useEffect } from "react";
import { generatePulseCode, type PulseConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";
import TargetingMethodSelector from "./TargetingMethodSelector";

export default function PulseGenerator() {
  const [config, setPulseConfig] = useState<PulseConfig>({
    objectIds: [],
    trigger: "continuous",
    scale: 1.1,
    duration: 1,
    iterations: 3,
    transformOrigin: "center",
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setPulseConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generatePulseCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Pulse Effect Generator
      </h2>

      <div className="space-y-4">
        <TargetingMethodSelector

          value={config.targetingMethod}

          onChange={(method) =>

            setConfig((prev) => ({ ...prev, targetingMethod: method }))

          }

        />


        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. alert1, notification2, cta3"
          label={config.targetingMethod === "data-model-id" ? "Object IDs" : "Accessibility Text Values"}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setPulseConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as
                    | "click"
                    | "hover"
                    | "timeline"
                    | "continuous",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="continuous">Continuous</option>
              <option value="timeline">Timeline Start</option>
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Scale Factor:
            </label>
            <input
              type="number"
              step="0.1"
              value={config.scale}
              onChange={(e) =>
                setPulseConfig((prev) => ({
                  ...prev,
                  scale: parseFloat(e.target.value) || 1.1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              value={config.duration}
              onChange={(e) =>
                setPulseConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {config.trigger !== "continuous" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Iterations:
              </label>
              <input
                type="number"
                value={config.iterations}
                onChange={(e) =>
                  setPulseConfig((prev) => ({
                    ...prev,
                    iterations: parseInt(e.target.value) || 3,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transform Origin:
            </label>
            <select
              value={config.transformOrigin}
              onChange={(e) =>
                setPulseConfig((prev) => ({
                  ...prev,
                  transformOrigin: e.target.value as
                    | "center"
                    | "top"
                    | "bottom"
                    | "left"
                    | "right",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="center">Center</option>
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
              <option value="left">Left</option>
              <option value="right">Right</option>
            </select>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="pulse" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
