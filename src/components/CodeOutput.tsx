import { useState } from "react";

interface CodeOutputProps {
  code: string;
  title?: string;
}

export default function CodeOutput({
  code,
  title = "Copy this JavaScript:",
}: CodeOutputProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <div className="mt-6">
      <div className="flex items-center gap-3 mb-3">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <button
          onClick={copyToClipboard}
          className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            copied
              ? "bg-green-100 text-green-800 border border-green-300"
              : "bg-blue-500 text-white hover:bg-blue-700"
          }`}
        >
          {copied ? "Copied!" : "Copy Code"}
        </button>
      </div>

      <div className="relative">
        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm leading-relaxed">
          <code>{code}</code>
        </pre>
      </div>

      <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <p className="text-sm text-blue-800">
          <span className="font-medium">💡 Note:</span> Paste this code into a
          JavaScript trigger in Articulate Storyline. Make sure your objects
          have the correct accessibility alt-text IDs.
        </p>
      </div>
    </div>
  );
}
