import { useState, useEffect } from "react";
import { generateTypewriterCode, type TypewriterConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function TypewriterGenerator() {
  const [config, setConfig] = useState<TypewriterConfig>({
    objectIds: [],
    trigger: "timeline",
    strings: ["Hello World!", "This is a typewriter effect."],
    typeSpeed: 50,
    backSpeed: 30,
    startDelay: 0,
    backDelay: 700,
    loop: false,
    loopCount: 1,
    showCursor: true,
    cursorChar: "|",
    smartBackspace: true,
    shuffle: false,
    fadeOut: false,
    fadeOutDelay: 500,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  const handleStringChange = (index: number, value: string) => {
    const newStrings = [...config.strings];
    newStrings[index] = value;
    setConfig((prev) => ({ ...prev, strings: newStrings }));
  };

  const addString = () => {
    setConfig((prev) => ({
      ...prev,
      strings: [...prev.strings, "New string..."],
    }));
  };

  const removeString = (index: number) => {
    if (config.strings.length > 1) {
      const newStrings = config.strings.filter((_, i) => i !== index);
      setConfig((prev) => ({ ...prev, strings: newStrings }));
    }
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateTypewriterCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        Typewriter Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. text1, text2, heading3"
          label="Object IDs"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="timeline">Timeline</option>
              <option value="click">Click</option>
              <option value="hover">Hover</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type Speed (ms):
            </label>
            <input
              type="number"
              value={config.typeSpeed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  typeSpeed: parseInt(e.target.value) || 50,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="1000"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Back Speed (ms):
            </label>
            <input
              type="number"
              value={config.backSpeed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  backSpeed: parseInt(e.target.value) || 30,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="1"
              max="1000"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Delay (ms):
            </label>
            <input
              type="number"
              value={config.startDelay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  startDelay: parseInt(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Back Delay (ms):
            </label>
            <input
              type="number"
              value={config.backDelay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  backDelay: parseInt(e.target.value) || 700,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cursor Character:
            </label>
            <input
              type="text"
              value={config.cursorChar}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  cursorChar: e.target.value || "|",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              maxLength={3}
            />
          </div>
        </div>

        {/* Strings Configuration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Text Strings:
          </label>
          <div className="space-y-2">
            {config.strings.map((string, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="text"
                  value={string}
                  onChange={(e) => handleStringChange(index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={`String ${index + 1}...`}
                />
                {config.strings.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeString(index)}
                    className="px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              onClick={addString}
              className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none"
            >
              + Add String
            </button>
          </div>
        </div>

        {/* Boolean Options */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.loop}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, loop: e.target.checked }))
              }
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Loop</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.showCursor}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, showCursor: e.target.checked }))
              }
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Show Cursor</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.smartBackspace}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, smartBackspace: e.target.checked }))
              }
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Smart Backspace</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.shuffle}
              onChange={(e) =>
                setConfig((prev) => ({ ...prev, shuffle: e.target.checked }))
              }
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Shuffle</span>
          </label>
        </div>

        {config.loop && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Loop Count (0 = infinite):
            </label>
            <input
              type="number"
              value={config.loopCount}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  loopCount: parseInt(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
            />
          </div>
        )}
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="typewriter" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
