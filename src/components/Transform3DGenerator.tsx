import { useState, useEffect } from "react";
import {
  generateTransform3DCode,
  type Transform3DConfig,
} from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function Transform3DGenerator() {
  const [config, setConfig] = useState<Transform3DConfig>({
    objectIds: [],
    trigger: "click",
    effect: "cardFlip",
    duration: 1,
    delay: 0,
    easing: "ease",
    rotateX: 0,
    rotateY: 180,
    rotateZ: 0,
    translateZ: 0,
    perspective: 100, // Changed to 100% for better responsiveness
    transformOrigin: "center",
    flipDirection: "horizontal",
    rotationSpeed: 2,
    continuous: false,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateTransform3DCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        3D Transform Effect Generator
      </h2>

      <div className="space-y-6">
        {/* Object IDs Input */}
        <div>
          <TagInput
            label="Object IDs"
            value={config.objectIds}
            onChange={handleObjectIdChange}
            placeholder="Enter object IDs (e.g., card1, button2)..."
          />
          <p className="text-sm text-gray-600 mt-1">
            Enter the data-acc-text values of elements you want to apply 3D
            transforms to
          </p>
        </div>

        {/* Basic Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as Transform3DConfig["trigger"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="timeline">Timeline Start</option>
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              3D Effect Type:
            </label>
            <select
              value={config.effect}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  effect: e.target.value as Transform3DConfig["effect"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="cardFlip">Card Flip</option>
              <option value="rotate3D">3D Rotation</option>
              <option value="perspective">Perspective Tilt</option>
              <option value="depth">Depth Movement</option>
              <option value="cube">Cube Rotation</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transform Origin:
            </label>
            <select
              value={config.transformOrigin}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  transformOrigin: e.target
                    .value as Transform3DConfig["transformOrigin"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="center">Center</option>
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
              <option value="left">Left</option>
              <option value="right">Right</option>
              <option value="top-left">Top Left</option>
              <option value="top-right">Top Right</option>
              <option value="bottom-left">Bottom Left</option>
              <option value="bottom-right">Bottom Right</option>
            </select>
          </div>
        </div>

        {/* Effect-specific controls */}
        {config.effect === "cardFlip" && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-900 mb-3">
              Card Flip Settings
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Flip Direction:
              </label>
              <select
                value={config.flipDirection}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    flipDirection: e.target.value as "horizontal" | "vertical",
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="horizontal">Horizontal (Y-axis)</option>
                <option value="vertical">Vertical (X-axis)</option>
              </select>
            </div>
          </div>
        )}

        {config.effect === "rotate3D" && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-green-900 mb-3">
              3D Rotation Settings
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.continuous}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        continuous: e.target.checked,
                      }))
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Continuous Rotation
                  </span>
                </label>
              </div>
              {config.continuous && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rotation Speed (seconds per cycle):
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    value={config.rotationSpeed}
                    onChange={(e) =>
                      setConfig((prev) => ({
                        ...prev,
                        rotationSpeed: parseFloat(e.target.value) || 2,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Rotation Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rotate X (degrees):
            </label>
            <input
              type="range"
              min="-360"
              max="360"
              step="15"
              value={config.rotateX}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  rotateX: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>-360°</span>
              <span className="font-medium">{config.rotateX}°</span>
              <span>360°</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rotate Y (degrees):
            </label>
            <input
              type="range"
              min="-360"
              max="360"
              step="15"
              value={config.rotateY}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  rotateY: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>-360°</span>
              <span className="font-medium">{config.rotateY}°</span>
              <span>360°</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Rotate Z (degrees):
            </label>
            <input
              type="range"
              min="-360"
              max="360"
              step="15"
              value={config.rotateZ}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  rotateZ: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>-360°</span>
              <span className="font-medium">{config.rotateZ}°</span>
              <span>360°</span>
            </div>
          </div>
        </div>

        {/* Advanced 3D Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Perspective (px):
            </label>
            <input
              type="range"
              min="100"
              max="2000"
              step="50"
              value={config.perspective}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  perspective: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Close (100px)</span>
              <span className="font-medium">{config.perspective}px</span>
              <span>Far (2000px)</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Translate Z (%):
            </label>
            <input
              type="range"
              min="-20"
              max="20"
              step="1"
              value={config.translateZ}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  translateZ: parseInt(e.target.value),
                }))
              }
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Back (-20%)</span>
              <span className="font-medium">{config.translateZ}%</span>
              <span>Forward (20%)</span>
            </div>
          </div>
        </div>

        {/* Animation Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0.1"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as Transform3DConfig["easing"],
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ease">Ease</option>
              <option value="ease-in">Ease In</option>
              <option value="ease-out">Ease Out</option>
              <option value="ease-in-out">Ease In Out</option>
              <option value="linear">Linear</option>
            </select>
          </div>
        </div>

        {/* Description */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-purple-900 mb-2">
            3D Transform Effects:
          </h3>
          <ul className="text-sm text-purple-800 space-y-1">
            <li>
              • <strong>Card Flip:</strong> Classic flip animation for cards and
              panels
            </li>
            <li>
              • <strong>3D Rotation:</strong> Rotate around X, Y, and Z axes
              simultaneously
            </li>
            <li>
              • <strong>Perspective Tilt:</strong> Subtle 3D tilt with depth
              perception
            </li>
            <li>
              • <strong>Depth Movement:</strong> Move elements forward/backward
              in 3D space
            </li>
            <li>
              • <strong>Cube Rotation:</strong> Sequential rotation on all three
              axes
            </li>
          </ul>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="transform3d" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
