import { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";

// Type declaration for anime.js
declare global {
  interface Window {
    anime?: (config: {
      targets: Element | Element[];
      strokeDashoffset: number;
      duration: number;
      delay: number;
      easing: string;
      loop?: boolean;
      complete?: () => void;
    }) => void;
    confetti?: (options: {
      particleCount?: number;
      spread?: number;
      angle?: number;
      gravity?: number;
      startVelocity?: number;
      scalar?: number;
      colors?: string[];
      shapes?: (string | unknown)[];
      origin?: { x: number; y: number };
      ticks?: number;
    }) => Promise<void> | null;
    Typed?: any;
  }
}

// VanillaTilt interface
interface VanillaTiltInstance {
  destroy(): void;
}

interface VanillaTiltStatic {
  init(
    element: HTMLElement,
    options: {
      max: number;
      speed: number;
      glare: boolean;
      "max-glare": number;
      scale: number;
    }
  ): void;
}

// Declare VanillaTilt for TypeScript
declare global {
  interface Window {
    VanillaTilt: VanillaTiltStatic;
  }
}

// Extend HTMLElement to include vanillaTilt property
interface HTMLElementWithTilt extends HTMLElement {
  vanillaTilt?: VanillaTiltInstance;
}

// Extend HTMLElement to include magnetic cleanup function
interface HTMLElementWithMagnetic extends HTMLElement {
  _magneticCleanup?: () => void;
}

// Combined type that includes all possible properties from all effect configs
type EffectConfig = {
  // Common properties
  objectIds?: string[];
  trigger?: "click" | "hover" | "timeline" | "continuous";
  duration?: number;
  delay?: number;
  easing?: "ease" | "ease-in" | "ease-out" | "ease-in-out" | "linear";

  // Fade specific
  effect?:
    | "fadeIn"
    | "fadeOut"
    | "fadeToggle"
    | "zoomIn"
    | "zoomOut"
    | "zoomToggle"
    | "skew"
    | "perspective"
    | "matrix"
    | "shape"
    | "digital"
    | "rgb"
    | "shake"
    | "corrupt"
    | "glow"
    | "pulse"
    | "flicker"
    | "rainbow"
    | "cardFlip"
    | "rotate3D"
    | "depth"
    | "cube";
  initialOpacity?: number;

  // Slide specific
  direction?:
    | "left"
    | "right"
    | "up"
    | "down"
    | "horizontal"
    | "vertical"
    | "both"
    | "forward"
    | "reverse"
    | "reveal"
    | "conceal";
  distance?: number;

  // Bounce specific
  height?: number;
  bounces?: number;

  // Zoom specific
  scale?: number;
  transformOrigin?:
    | "center"
    | "top"
    | "bottom"
    | "left"
    | "right"
    | "top-left"
    | "top-right"
    | "bottom-left"
    | "bottom-right";

  // Pulse specific
  iterations?: number;

  // Shake specific
  intensity?: number;
  frequency?: number;

  // Rotate specific
  axis?: "X" | "Y" | "Z";
  degrees?: number;

  // Tilt specific
  maxTilt?: number;
  speed?: number;
  glare?: boolean;

  // Morph specific
  skewX?: number;
  skewY?: number;
  perspective?: number;
  rotateX?: number;
  rotateY?: number;
  scaleX?: number;
  scaleY?: number;
  translateX?: number;
  translateY?: number;
  shapeType?: "diamond" | "parallelogram" | "trapezoid" | "custom";

  // Neon specific
  color?: string;
  blurRadius?: number;
  spreadRadius?: number;

  // Magnetic specific
  strength?: number;
  returnSpeed?: number;

  // 3D Transform specific
  rotateZ?: number;
  translateZ?: number;
  flipDirection?: "horizontal" | "vertical";
  rotationSpeed?: number;
  continuous?: boolean;

  // Path drawing specific
  library?: "gsap" | "anime";
  pathData?: string;
  allPaths?: string[];
  loop?: boolean;
  pathDirection?: "forward" | "reverse";
  stagger?: number;

  // Confetti specific
  particleCount?: number;
  colors?: string[];
  shapes?: (
    | "square"
    | "circle"
    | "star"
    | "triangle"
    | "heart"
    | "plus"
    | "ribbon"
    | "squiggle"
    | "streamer"
  )[];
  origin?: {
    x: number;
    y: number;
  };
  spread?: number;
  angle?: number;
  gravity?: number;
  startVelocity?: number;
  scalar?: number;

  // Physics specific
  physicsEffect?: "drop" | "bounce" | "explode" | "attract" | "repel";
  objectCount?: number;
  objectShape?: "circle" | "rectangle" | "polygon";
  objectSize?: number;
  restitution?: number;
  friction?: number;
  initialVelocity?: {
    x: number;
    y: number;
  };

  // Ripple specific
  rippleEffect?: "material" | "water" | "pulse" | "shockwave";
  size?: "small" | "medium" | "large" | "auto";
  opacity?: number;
  rippleOrigin?: "center" | "click" | "custom";
  customOrigin?: {
    x: number;
    y: number;
  };
  allowMultiple?: boolean;

  // Typewriter specific
  strings?: string[];
  typeSpeed?: number;
  backSpeed?: number;
  startDelay?: number;
  backDelay?: number;
  loopCount?: number;
  showCursor?: boolean;
  cursorChar?: string;
  smartBackspace?: boolean;
  shuffle?: boolean;
  fadeOut?: boolean;
  fadeOutDelay?: number;

  // Fill specific
  origin?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center' | 'left' | 'right' | 'top' | 'bottom';
  stagger?: number;
  color?: string;
  borderRadius?: number;
  shape?: 'rectangle' | 'circle' | 'rounded-rectangle' | 'ellipse';
};

type PreviewBoxProps = {
  effectType: string;
  config: EffectConfig;
  onTrigger?: () => void;
};

export default function PreviewBox({
  effectType,
  config,
  onTrigger,
}: PreviewBoxProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const elementRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Reset element to initial state
  const resetElement = () => {
    if (!elementRef.current) return;

    // Destroy any existing tilt instance
    const element = elementRef.current as HTMLElementWithTilt;
    if (element.vanillaTilt) {
      element.vanillaTilt.destroy();
    }

    gsap.set(elementRef.current, {
      x: 0,
      y: 0,
      scale: 1,
      rotation: 0,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      opacity: 1,
      transformOrigin: "center center",
      boxShadow: "none",
    });

    // Reset CSS properties that might have been modified by VanillaTilt
    elementRef.current.style.transformStyle = "";
    elementRef.current.style.willChange = "";
  };

  // Execute the preview animation based on effect type
  const executePreview = () => {
    if (!elementRef.current) return;

    resetElement();

    switch (effectType) {
      case "fade":
        previewFadeEffect();
        break;
      case "slide":
        previewSlideEffect();
        break;
      case "bounce":
        previewBounceEffect();
        break;
      case "zoom":
        previewZoomEffect();
        break;
      case "pulse":
        previewPulseEffect();
        break;
      case "shake":
        previewShakeEffect();
        break;
      case "rotate":
        previewRotateEffect();
        break;
      case "tilt":
        previewTiltEffect();
        break;
      case "morph":
        previewMorphEffect();
        break;
      case "glitch":
        previewGlitchEffect();
        break;
      case "neon":
        previewNeonEffect();
        break;
      case "magnetic":
        previewMagneticEffect();
        break;
      case "transform3d":
        previewTransform3DEffect();
        break;
      case "pathdraw":
        previewPathDrawEffect();
        break;
      case "confetti":
        previewConfettiEffect();
        break;
      case "physics":
        previewPhysicsEffect();
        break;
      case "ripple":
        previewRippleEffect();
        break;
      case "typewriter":
        previewTypewriterEffect();
        break;
      case "fill":
        previewFillEffect();
        break;
      default:
        break;
    }

    if (onTrigger) onTrigger();
  };

  const previewFadeEffect = () => {
    if (!elementRef.current) return;

    const { effect, duration, easing, initialOpacity, delay } = config;

    if (effect === "fadeIn") {
      gsap.set(elementRef.current, { opacity: (initialOpacity || 0) / 100 });
      gsap.to(elementRef.current, {
        duration: duration || 1,
        opacity: 1,
        ease: easing || "ease",
        delay: delay || 0,
      });
    } else if (effect === "fadeOut") {
      gsap.to(elementRef.current, {
        duration: duration || 1,
        opacity: 0,
        ease: easing || "ease",
        delay: delay || 0,
      });
    } else if (effect === "fadeToggle") {
      const currentOpacity = gsap.getProperty(
        elementRef.current,
        "opacity"
      ) as number;
      gsap.to(elementRef.current, {
        duration: duration || 1,
        opacity: currentOpacity > 0.5 ? 0 : 1,
        ease: easing || "ease",
        delay: delay || 0,
      });
    }
  };

  const previewSlideEffect = () => {
    if (!elementRef.current) return;

    const { direction, distance, duration, easing, delay } = config;

    // Set initial position based on direction (convert percentage to pixels for preview)
    const getInitialPosition = () => {
      const pixelDistance = (distance || 10) * 10; // Convert % to reasonable pixel value for preview
      switch (direction) {
        case "left":
          return { x: -pixelDistance, y: 0 };
        case "right":
          return { x: pixelDistance, y: 0 };
        case "up":
          return { x: 0, y: -pixelDistance };
        case "down":
          return { x: 0, y: pixelDistance };
        default:
          return { x: 0, y: 0 };
      }
    };

    const initialPos = getInitialPosition();
    gsap.set(elementRef.current, { ...initialPos, opacity: 0 });
    gsap.to(elementRef.current, {
      duration: duration || 1,
      x: 0,
      y: 0,
      opacity: 1,
      ease: easing || "ease",
      delay: delay || 0,
    });
  };

  const previewBounceEffect = () => {
    if (!elementRef.current) return;

    const { height, bounces, duration } = config;
    const timeline = gsap.timeline();

    for (let i = 0; i < (bounces || 3); i++) {
      const bounceHeight = (height || 5) * 10 * (1 - i / (bounces || 3)); // Convert % to pixels for preview
      timeline
        .to(elementRef.current, {
          duration: (duration || 1) / ((bounces || 3) * 2),
          y: -bounceHeight,
          ease: "power2.out",
        })
        .to(elementRef.current, {
          duration: (duration || 1) / ((bounces || 3) * 2),
          y: 0,
          ease: "power2.in",
        });
    }
  };

  const previewZoomEffect = () => {
    if (!elementRef.current) return;

    const { effect, scale, duration, easing, transformOrigin, delay } = config;

    gsap.set(elementRef.current, {
      transformOrigin: `${transformOrigin || "center"} ${
        transformOrigin || "center"
      }`,
    });

    if (effect === "zoomIn") {
      gsap.set(elementRef.current, { scale: 0 });
      gsap.to(elementRef.current, {
        duration: duration || 1,
        scale: scale || 1.5,
        ease: easing || "ease",
        delay: delay || 0,
      });
    } else if (effect === "zoomOut") {
      gsap.to(elementRef.current, {
        duration: duration || 1,
        scale: 0,
        ease: easing || "ease",
        delay: delay || 0,
      });
    } else if (effect === "zoomToggle") {
      const currentScale = gsap.getProperty(
        elementRef.current,
        "scaleX"
      ) as number;
      gsap.to(elementRef.current, {
        duration: duration || 1,
        scale: currentScale > 1 ? 1 : scale || 1.5,
        ease: easing || "ease",
        delay: delay || 0,
      });
    }
  };

  const previewPulseEffect = () => {
    if (!elementRef.current) return;

    const { scale, duration, iterations, transformOrigin, trigger } = config;

    gsap.set(elementRef.current, {
      transformOrigin: `${transformOrigin || "center"} ${
        transformOrigin || "center"
      }`,
    });

    if (trigger === "continuous") {
      // Continuous pulse effect - infinite repeat
      gsap.to(elementRef.current, {
        duration: duration || 1,
        scale: scale || 1.2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
      });
    } else {
      // Finite pulse effect
      gsap.to(elementRef.current, {
        duration: (duration || 1) / 2,
        scale: scale || 1.2,
        repeat: (iterations || 2) * 2 - 1,
        yoyo: true,
        ease: "power2.inOut",
      });
    }
  };

  const previewShakeEffect = () => {
    if (!elementRef.current) return;

    const { intensity, duration, direction, frequency } = config;
    const timeline = gsap.timeline();
    const shakeCount = Math.ceil((duration || 1) * (frequency || 10));

    for (let i = 0; i < shakeCount; i++) {
      const pixelIntensity = (intensity || 1) * 10; // Convert % to pixels for preview
      const xOffset =
        direction === "vertical"
          ? 0
          : (Math.random() - 0.5) * pixelIntensity * 2;
      const yOffset =
        direction === "horizontal"
          ? 0
          : (Math.random() - 0.5) * pixelIntensity * 2;

      timeline.to(elementRef.current, {
        duration: (duration || 1) / (frequency || 10),
        x: xOffset,
        y: yOffset,
        ease: "power2.inOut",
      });
    }

    timeline.to(elementRef.current, {
      duration: (duration || 1) / (frequency || 10),
      x: 0,
      y: 0,
      ease: "power2.out",
    });
  };

  const previewRotateEffect = () => {
    if (!elementRef.current) return;

    const { axis, degrees, duration, easing } = config;

    gsap.to(elementRef.current, {
      duration: duration || 1,
      [`rotation${axis || "Z"}`]: degrees || 90,
      transformOrigin: "center center",
      ease: easing || "ease",
    });
  };

  const previewTiltEffect = () => {
    if (!elementRef.current) return;

    const { maxTilt, scale, speed, glare } = config;

    // Load VanillaTilt library if not already loaded
    if (!window.VanillaTilt) {
      const script = document.createElement("script");
      script.src =
        "https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js";
      script.onload = () => {
        initializeTilt();
      };
      document.head.appendChild(script);
    } else {
      initializeTilt();
    }

    function initializeTilt() {
      if (!elementRef.current) return;

      // Destroy any existing tilt instance
      const element = elementRef.current as HTMLElementWithTilt;
      if (element.vanillaTilt) {
        element.vanillaTilt.destroy();
      }

      // Apply essential CSS properties for proper tilt functionality
      elementRef.current.style.transformStyle = "preserve-3d";
      elementRef.current.style.willChange = "transform";
      elementRef.current.style.transformOrigin = "center center";

      // Initialize VanillaTilt with the actual config values
      window.VanillaTilt.init(elementRef.current, {
        max: maxTilt || 15,
        speed: speed || 400,
        glare: glare || false,
        "max-glare": glare ? 0.2 : 0,
        scale: scale || 1.02,
      });

      // Trigger a demo tilt after a short delay
      setTimeout(() => {
        if (elementRef.current) {
          // Simulate mouse movement to trigger tilt
          const rect = elementRef.current.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;

          const mouseEvent = new MouseEvent("mousemove", {
            clientX: centerX + 20,
            clientY: centerY - 20,
            bubbles: true,
          });
          elementRef.current.dispatchEvent(mouseEvent);

          // Return to center after demo
          setTimeout(() => {
            if (elementRef.current) {
              const centerEvent = new MouseEvent("mouseleave", {
                bubbles: true,
              });
              elementRef.current.dispatchEvent(centerEvent);
            }
          }, 1000);
        }
      }, 100);
    }
  };

  const previewMorphEffect = () => {
    if (!elementRef.current) return;

    const {
      effect,
      skewX,
      skewY,
      perspective,
      rotateX,
      rotateY,
      scaleX,
      scaleY,
      translateX,
      translateY,
      shapeType,
      intensity,
      duration,
      easing,
    } = config;

    // Generate transform string based on effect type
    let transformString = "";

    switch (effect) {
      case "skew":
        transformString = `skew(${skewX || 15}deg, ${skewY || 5}deg)`;
        break;

      case "perspective":
        transformString = `perspective(${
          (perspective || 100) * 10
        }px) rotateX(${rotateX || 15}deg) rotateY(${rotateY || 15}deg)`;
        break;

      case "matrix":
        transformString = `matrix(${scaleX || 1.2}, 0, 0, ${scaleY || 1.2}, ${
          translateX || 0
        }, ${translateY || 0})`;
        break;

      case "shape":
        switch (shapeType) {
          case "diamond":
            transformString = `rotate(45deg) scale(${
              1 + (intensity || 5) * 0.1
            })`;
            break;
          case "parallelogram":
            transformString = `skew(${(intensity || 5) * 2}deg, 0deg) scale(${
              1 + (intensity || 5) * 0.05
            })`;
            break;
          case "trapezoid":
            transformString = `perspective(${
              500 + (intensity || 5) * 100
            }px) rotateX(${(intensity || 5) * 3}deg)`;
            break;
          default:
            transformString = `skew(${intensity || 5}deg, ${
              (intensity || 5) * 0.5
            }deg) scale(${1 + (intensity || 5) * 0.1})`;
        }
        break;

      default:
        transformString = `skew(${skewX || 15}deg, ${skewY || 5}deg)`;
    }

    // Set transform origin for better morphing
    gsap.set(elementRef.current, {
      transformOrigin: "center center",
      transformStyle: "preserve-3d",
    });

    gsap.to(elementRef.current, {
      duration: duration || 1,
      transform: transformString,
      ease: easing || "ease",
    });
  };

  const previewGlitchEffect = () => {
    if (!elementRef.current) return;

    const { effect, intensity, speed, duration } = config;

    switch (effect) {
      case "digital": {
        // Digital glitch with rapid position changes
        const timeline = gsap.timeline();
        const pixelIntensity = (intensity || 1) * 10; // Convert % to pixels for preview
        for (let i = 0; i < (speed || 5); i++) {
          timeline
            .to(elementRef.current, {
              duration: (duration || 1) / (speed || 5) / 4,
              x: (Math.random() - 0.5) * pixelIntensity,
              y: (Math.random() - 0.5) * pixelIntensity * 0.5,
              scaleX: 1 + (Math.random() - 0.5) * pixelIntensity * 0.01,
              scaleY: 1 + (Math.random() - 0.5) * pixelIntensity * 0.01,
              ease: "none",
            })
            .to(elementRef.current, {
              duration: (duration || 1) / (speed || 5) / 4,
              x: 0,
              y: 0,
              scaleX: 1,
              scaleY: 1,
              ease: "power2.out",
            });
        }
        break;
      }

      case "shake": {
        // Intense shake glitch
        const shakeTimeline = gsap.timeline();
        const pixelIntensity = (intensity || 1) * 10; // Convert % to pixels for preview
        for (let i = 0; i < (speed || 5) * 2; i++) {
          shakeTimeline.to(elementRef.current, {
            duration: (duration || 1) / ((speed || 5) * 4),
            x: (Math.random() - 0.5) * pixelIntensity * 2,
            y: (Math.random() - 0.5) * pixelIntensity * 2,
            rotation: (Math.random() - 0.5) * pixelIntensity * 0.5,
            ease: "none",
          });
        }
        shakeTimeline.to(elementRef.current, {
          duration: (duration || 1) / (speed || 5),
          x: 0,
          y: 0,
          rotation: 0,
          ease: "power2.out",
        });
        break;
      }

      case "corrupt": {
        // Data corruption effect
        const pixelIntensity = (intensity || 1) * 10; // Convert % to pixels for preview
        gsap
          .timeline()
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            scaleX: 1 + pixelIntensity * 0.1,
            scaleY: 1 - pixelIntensity * 0.05,
            skewX: pixelIntensity * 2,
            ease: "power2.inOut",
          })
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            scaleX: 1 - pixelIntensity * 0.05,
            scaleY: 1 + pixelIntensity * 0.1,
            skewX: -pixelIntensity,
            skewY: pixelIntensity,
            ease: "power2.inOut",
          })
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            scaleX: 1,
            scaleY: 1,
            skewX: 0,
            skewY: 0,
            ease: "power2.out",
          });
        break;
      }

      default: {
        // Default digital glitch
        const pixelIntensity = (intensity || 1) * 10; // Convert % to pixels for preview
        gsap
          .timeline()
          .to(elementRef.current, {
            duration: duration || 1,
            x: (Math.random() - 0.5) * pixelIntensity,
            y: (Math.random() - 0.5) * pixelIntensity,
            ease: "power2.inOut",
          })
          .to(elementRef.current, {
            duration: (duration || 1) / 2,
            x: 0,
            y: 0,
            ease: "power2.out",
          });
      }
    }
  };

  const previewNeonEffect = () => {
    if (!elementRef.current) return;

    const { effect, color, intensity, blurRadius, spreadRadius, duration } =
      config;

    // Convert percentage values to pixels for preview
    const pixelBlurRadius = (blurRadius || 1) * 5;
    const pixelSpreadRadius = (spreadRadius || 2) * 5;

    const baseGlow = `0 0 ${pixelBlurRadius}px ${color || "#00ffff"}, 0 0 ${
      pixelBlurRadius * 2
    }px ${color || "#00ffff"}, 0 0 ${pixelBlurRadius * 3}px ${
      color || "#00ffff"
    }`;
    const intenseGlow = `0 0 ${pixelBlurRadius * (intensity || 2)}px ${
      color || "#00ffff"
    }, 0 0 ${pixelBlurRadius * (intensity || 2) * 2}px ${
      color || "#00ffff"
    }, 0 0 ${pixelBlurRadius * (intensity || 2) * 3}px ${
      color || "#00ffff"
    }, 0 0 ${pixelSpreadRadius}px ${color || "#00ffff"}`;

    switch (effect) {
      case "glow":
        // Static neon glow
        gsap.set(elementRef.current, {
          textShadow: intenseGlow,
          color: color || "#00ffff",
        });
        break;

      case "pulse":
        // Pulsing neon effect
        gsap
          .timeline({ repeat: 2, yoyo: true })
          .to(elementRef.current, {
            duration: (duration || 2) / 2,
            textShadow: intenseGlow,
            color: color || "#00ffff",
            ease: "power2.inOut",
          })
          .to(elementRef.current, {
            duration: (duration || 2) / 2,
            textShadow: baseGlow,
            ease: "power2.inOut",
          });
        break;

      case "flicker": {
        // Flickering neon effect
        const flickerTimeline = gsap.timeline();
        const flickerPattern = [1, 0.8, 1, 0.3, 1, 0.9, 0.1, 1, 0.7, 1];

        flickerPattern.forEach((opacity) => {
          flickerTimeline.to(elementRef.current, {
            duration: (duration || 2) / 10,
            textShadow: opacity > 0.5 ? intenseGlow : baseGlow,
            color:
              opacity > 0.5 ? color || "#00ffff" : `rgba(0, 255, 255, 0.5)`,
            ease: "none",
          });
        });
        break;
      }

      case "rainbow": {
        // Rainbow neon effect
        const colors = [
          "#ff0000",
          "#ff7f00",
          "#ffff00",
          "#00ff00",
          "#0000ff",
          "#4b0082",
          "#9400d3",
        ];
        const rainbowTimeline = gsap.timeline();

        colors.forEach((rainbowColor) => {
          const rainbowGlow = `0 0 ${blurRadius || 10}px ${rainbowColor}, 0 0 ${
            (blurRadius || 10) * 2
          }px ${rainbowColor}, 0 0 ${(blurRadius || 10) * 3}px ${rainbowColor}`;
          rainbowTimeline.to(elementRef.current, {
            duration: (duration || 2) / colors.length,
            textShadow: rainbowGlow,
            color: rainbowColor,
            ease: "power2.inOut",
          });
        });
        break;
      }

      default:
        // Default glow
        gsap.set(elementRef.current, {
          textShadow: intenseGlow,
          color: color || "#00ffff",
        });
    }
  };

  const previewMagneticEffect = () => {
    if (!elementRef.current || !previewRef.current) return;

    const { strength, distance, speed, returnSpeed, easing } = config;

    // Set up element for smooth transforms
    elementRef.current.style.willChange = "transform";
    elementRef.current.style.transformOrigin = "center center";

    let isHovering = false;
    let animationFrame: number;

    function updatePosition(mouseX: number, mouseY: number) {
      if (!isHovering || !elementRef.current || !previewRef.current) return;

      const previewRect = previewRef.current.getBoundingClientRect();
      const elementRect = elementRef.current.getBoundingClientRect();

      // Calculate relative mouse position within the preview box
      const relativeX = mouseX - previewRect.left;
      const relativeY = mouseY - previewRect.top;

      // Calculate element center relative to preview box
      const elementCenterX =
        elementRect.left - previewRect.left + elementRect.width / 2;
      const elementCenterY =
        elementRect.top - previewRect.top + elementRect.height / 2;

      // Calculate distance from mouse to element center
      const deltaX = relativeX - elementCenterX;
      const deltaY = relativeY - elementCenterY;
      const dist = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // Only apply magnetic effect if within range (convert % to pixels for preview)
      const maxDistance = (distance || 10) * 10; // Convert % to pixels for preview
      if (dist < maxDistance) {
        // Calculate magnetic force (stronger when closer)
        const force = Math.max(0, 1 - dist / maxDistance);
        const moveX = deltaX * force * (strength || 0.3);
        const moveY = deltaY * force * (strength || 0.3);

        gsap.to(elementRef.current, {
          duration: speed || 0.3,
          x: moveX,
          y: moveY,
          ease: easing || "ease-out",
          overwrite: true,
        });
      }
    }

    function handleMouseMove(e: MouseEvent) {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }

      animationFrame = requestAnimationFrame(() => {
        updatePosition(e.clientX, e.clientY);
      });
    }

    function handleMouseEnter() {
      isHovering = true;
      document.addEventListener("mousemove", handleMouseMove);
    }

    function handleMouseLeave() {
      isHovering = false;
      document.removeEventListener("mousemove", handleMouseMove);

      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }

      // Return to original position
      if (elementRef.current) {
        gsap.to(elementRef.current, {
          duration: returnSpeed || 0.6,
          x: 0,
          y: 0,
          ease: easing || "ease-out",
        });
      }
    }

    // Add event listeners to the preview container
    previewRef.current.addEventListener("mouseenter", handleMouseEnter);
    previewRef.current.addEventListener("mouseleave", handleMouseLeave);

    // Cleanup function to remove listeners
    const cleanup = () => {
      if (previewRef.current) {
        previewRef.current.removeEventListener("mouseenter", handleMouseEnter);
        previewRef.current.removeEventListener("mouseleave", handleMouseLeave);
      }
      document.removeEventListener("mousemove", handleMouseMove);
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };

    // Store cleanup function for later use
    (previewRef.current as HTMLElementWithMagnetic)._magneticCleanup = cleanup;
  };

  const previewTransform3DEffect = () => {
    if (!elementRef.current) return;

    const {
      effect,
      duration,
      easing,
      rotateX,
      rotateY,
      rotateZ,
      translateZ,
      perspective,
      transformOrigin,
      flipDirection,
      rotationSpeed,
      continuous,
    } = config;

    // Set up 3D transform properties (convert % to pixels for preview)
    elementRef.current.style.transformStyle = "preserve-3d";
    elementRef.current.style.perspective = `${(perspective || 100) * 10}px`;
    elementRef.current.style.willChange = "transform";
    elementRef.current.style.backfaceVisibility = "hidden";

    gsap.set(elementRef.current, {
      transformOrigin: `${transformOrigin || "center"} ${
        transformOrigin || "center"
      }`,
    });

    switch (effect) {
      case "cardFlip":
        if (flipDirection === "horizontal") {
          gsap.to(elementRef.current, {
            duration: duration || 1,
            rotateY: 180,
            ease: easing || "ease",
          });
        } else {
          gsap.to(elementRef.current, {
            duration: duration || 1,
            rotateX: 180,
            ease: easing || "ease",
          });
        }
        break;

      case "rotate3D":
        if (continuous) {
          gsap.to(elementRef.current, {
            duration: rotationSpeed || 2,
            rotateX: rotateX || 0,
            rotateY: rotateY || 360,
            rotateZ: rotateZ || 0,
            repeat: -1,
            ease: "none",
          });
        } else {
          gsap.to(elementRef.current, {
            duration: duration || 1,
            rotateX: rotateX || 0,
            rotateY: rotateY || 180,
            rotateZ: rotateZ || 0,
            ease: easing || "ease",
          });
        }
        break;

      case "perspective":
        gsap.to(elementRef.current, {
          duration: duration || 1,
          rotateX: rotateX || 15,
          rotateY: rotateY || 15,
          ease: easing || "ease",
        });
        break;

      case "depth":
        gsap.to(elementRef.current, {
          duration: duration || 1,
          translateZ: (translateZ || 5) * 10, // Convert % to pixels for preview
          rotateX: (rotateX || 0) * 0.3,
          rotateY: (rotateY || 0) * 0.3,
          ease: easing || "ease",
        });
        break;

      case "cube": {
        const timeline = gsap.timeline();
        timeline
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            rotateY: 90,
            ease: easing || "ease",
          })
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            rotateX: 90,
            ease: easing || "ease",
          })
          .to(elementRef.current, {
            duration: (duration || 1) / 3,
            rotateZ: 90,
            ease: easing || "ease",
          });
        break;
      }

      default:
        gsap.to(elementRef.current, {
          duration: duration || 1,
          rotateX: rotateX || 0,
          rotateY: rotateY || 180,
          rotateZ: rotateZ || 0,
          ease: easing || "ease",
        });
    }
  };

  const previewPathDrawEffect = () => {
    if (!elementRef.current) return;

    const {
      pathDirection,
      duration,
      delay,
      easing,
      pathData,
      allPaths,
      library,
      loop,
      stagger,
    } = config;

    // Clear any existing SVG content first (important for proper reset)
    const existingSvgs = elementRef.current.querySelectorAll("svg");
    existingSvgs.forEach((svg) => svg.remove());

    // Now check for existing paths (should be none after clearing)
    let existingPaths = elementRef.current.querySelectorAll("path");

    if (existingPaths.length === 0 && (pathData || allPaths)) {
      // Create demo SVG for preview (matches what will happen in Storyline)
      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", "100%");
      svg.setAttribute("height", "100%");

      // Use allPaths if available, otherwise use single pathData
      const pathsToCreate =
        allPaths && allPaths.length > 0 ? allPaths : [pathData || ""];

      // Try to determine appropriate viewBox from path data
      let viewBox = "0 0 800 600"; // Larger default for better visibility
      const firstPath = pathsToCreate[0];
      if (firstPath) {
        const numbers = firstPath.match(/[\d.]+/g);
        if (numbers && numbers.length > 0) {
          const coords = numbers.map((n) => parseFloat(n));
          const minCoord = Math.min(...coords);
          const maxCoord = Math.max(...coords);

          // Calculate bounds with padding
          const padding = Math.max(50, (maxCoord - minCoord) * 0.1);
          const minX = Math.max(0, minCoord - padding);
          const maxX = maxCoord + padding;
          const minY = Math.max(0, minCoord - padding);
          const maxY = maxCoord + padding;

          viewBox = `${minX} ${minY} ${maxX - minX} ${maxY - minY}`;
        }
      }

      svg.setAttribute("viewBox", viewBox);
      svg.setAttribute("preserveAspectRatio", "xMidYMid meet");
      svg.style.position = "absolute";
      svg.style.top = "0";
      svg.style.left = "0";
      svg.style.pointerEvents = "none";

      // Create path elements for all paths
      pathsToCreate.forEach((pathD) => {
        if (pathD && pathD.trim()) {
          const path = document.createElementNS(
            "http://www.w3.org/2000/svg",
            "path"
          );
          path.setAttribute("d", pathD);
          // Use default stroke properties for preview (real SVG will have its own)
          path.setAttribute("stroke", "#3b82f6");
          path.setAttribute("stroke-width", "3");
          path.setAttribute("fill", "none");
          path.setAttribute("stroke-linecap", "round");
          path.setAttribute("stroke-linejoin", "round");
          path.style.filter = "drop-shadow(0 1px 2px rgba(0,0,0,0.1))";

          svg.appendChild(path);
        }
      });

      elementRef.current.appendChild(svg);

      // Update the paths list to include our new paths
      existingPaths = elementRef.current.querySelectorAll("path");
    }

    // Now animate all existing paths (matches generated code exactly)
    if (existingPaths.length === 0) {
      console.error("No SVG paths found for animation");
      return;
    }

    if (library === "anime") {
      // Use Anime.js for preview (if available)
      if (typeof window.anime !== "undefined") {
        // Set initial state for all paths
        existingPaths.forEach((path) => {
          const pathLength = path.getTotalLength();
          path.style.strokeDasharray = pathLength.toString();
          path.style.strokeDashoffset = (
            pathDirection === "forward" ? pathLength : 0
          ).toString();
        });

        // Animate all paths with Anime.js (with stagger)
        existingPaths.forEach((path, index) => {
          const pathLength = path.getTotalLength();
          window.anime!({
            targets: path,
            strokeDashoffset: pathDirection === "forward" ? 0 : pathLength,
            duration: (duration || 2) * 1000,
            delay: (delay || 0) * 1000 + index * (stagger || 0) * 1000,
            easing:
              easing === "ease"
                ? "easeInOutQuad"
                : easing === "ease-in"
                ? "easeInQuad"
                : easing === "ease-out"
                ? "easeOutQuad"
                : easing === "ease-in-out"
                ? "easeInOutQuad"
                : "linear",
            loop: loop || false,
          });
        });
      } else {
        // Fallback to GSAP if Anime.js not loaded
        console.warn("Anime.js not loaded, falling back to GSAP for preview");
        animateWithGSAP();
      }
    } else {
      // Use GSAP for preview
      animateWithGSAP();
    }

    function animateWithGSAP() {
      // Set initial state and animate all paths (with stagger)
      existingPaths.forEach((path, index) => {
        const pathLength = path.getTotalLength();

        // Set initial state for drawing animation
        gsap.set(path, {
          strokeDasharray: pathLength,
          strokeDashoffset: pathDirection === "forward" ? pathLength : 0,
        });

        // Animate the path drawing with stagger
        gsap.to(path, {
          strokeDashoffset: pathDirection === "forward" ? 0 : pathLength,
          duration: duration || 2,
          delay: (delay || 0) + index * (stagger || 0),
          ease: easing || "ease",
          repeat: loop ? -1 : 0,
        });
      });
    }
  };

  const previewConfettiEffect = () => {
    if (!previewRef.current) return;

    const {
      particleCount,
      colors,
      shapes,
      spread,
      angle,
      gravity,
      startVelocity,
      scalar,
      origin,
      delay,
    } = config;

    // Load confetti.js library if not already loaded
    if (!window.confetti) {
      const script = document.createElement("script");
      script.src =
        "https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js";
      script.onload = () => {
        launchConfetti();
      };
      document.head.appendChild(script);
    } else {
      launchConfetti();
    }

    function launchConfetti() {
      setTimeout(() => {
        // Get preview box position for confetti origin
        const rect = previewRef.current!.getBoundingClientRect();
        const originX =
          (rect.left + rect.width * (origin?.x || 0.5)) / window.innerWidth;
        const originY =
          (rect.top + rect.height * (origin?.y || 0.5)) / window.innerHeight;

        // Create custom shapes if needed
        const customShapes: Record<string, unknown> = {};
        const customShapePaths = {
          triangle: "M0 10 L5 0 L10 10z",
          heart:
            "M5 0 C2.5 -2.5 0 0 0 2.5 C0 5 2.5 7.5 5 10 C7.5 7.5 10 5 10 2.5 C10 0 7.5 -2.5 5 0z",
          plus: "M4 0 L6 0 L6 4 L10 4 L10 6 L6 6 L6 10 L4 10 L4 6 L0 6 L0 4 L4 4z",
          ribbon:
            "M0 3 Q3 0 6 3 Q9 6 12 3 Q15 0 18 3 L18 7 Q15 10 12 7 Q9 4 6 7 Q3 10 0 7 Z",
          squiggle: "M0 8 Q2 2 4 8 Q6 14 8 8 Q10 2 12 8 Q14 14 16 8 Q18 2 20 8",
          streamer:
            "M0 2 Q5 0 10 2 Q15 4 20 2 Q25 0 30 2 L30 4 Q25 6 20 4 Q15 2 10 4 Q5 6 0 4 Z",
        };

        if (shapes && shapes.length > 0) {
          shapes.forEach((shape) => {
            if (customShapePaths[shape as keyof typeof customShapePaths]) {
              customShapes[shape] = (
                window as unknown as {
                  confetti?: {
                    shapeFromPath: (options: { path: string }) => unknown;
                  };
                }
              ).confetti?.shapeFromPath({
                path: customShapePaths[shape as keyof typeof customShapePaths],
              });
            }
          });
        }

        // Combine built-in and custom shapes
        const builtInShapes =
          shapes?.filter((s) => ["square", "circle", "star"].includes(s)) || [];
        const customShapeObjects =
          shapes
            ?.filter((s) => !["square", "circle", "star"].includes(s))
            .map((s) => customShapes[s])
            .filter(Boolean) || [];

        const allShapes = [...builtInShapes, ...customShapeObjects];

        const confettiOptions = {
          particleCount: particleCount || 50,
          spread: spread || 45,
          angle: angle || 90,
          gravity: gravity || 1,
          startVelocity: startVelocity || 45,
          scalar: scalar || 1,
          origin: { x: originX, y: originY },
          colors: colors && colors.length > 0 ? colors : undefined,
          shapes: allShapes.length > 0 ? allShapes : undefined,
        };

        window.confetti?.(confettiOptions);
      }, (delay || 0) * 1000);
    }
  };

  const previewPhysicsEffect = () => {
    if (!previewRef.current) return;

    const {
      physicsEffect,
      objectCount,
      objectShape,
      objectSize,
      colors,
      delay,
      duration,
    } = config;

    function createPhysicsPreview() {
      setTimeout(() => {
        // Create a simple visual representation of physics objects
        const previewBox = previewRef.current!;
        const rect = previewBox.getBoundingClientRect();

        // Clear any existing physics objects
        const existingObjects = previewBox.querySelectorAll(".physics-object");
        existingObjects.forEach((obj) => obj.remove());

        // Create physics objects for preview
        for (let i = 0; i < Math.min(objectCount || 10, 15); i++) {
          const physicsObject = document.createElement("div");
          physicsObject.className = "physics-object";

          const size = Math.max(5, Math.min(objectSize || 20, 30)); // Limit size for preview
          const color =
            colors && colors.length > 0
              ? colors[Math.floor(Math.random() * colors.length)]
              : "#3b82f6";

          // Style the physics object
          physicsObject.style.position = "absolute";
          physicsObject.style.width = size + "px";
          physicsObject.style.height = size + "px";
          physicsObject.style.backgroundColor = color;
          physicsObject.style.pointerEvents = "none";
          physicsObject.style.zIndex = "10";

          // Shape styling
          if (objectShape === "circle") {
            physicsObject.style.borderRadius = "50%";
          } else if (objectShape === "polygon") {
            physicsObject.style.clipPath =
              "polygon(50% 0%, 0% 100%, 100% 100%)";
          }
          // rectangle is default (no additional styling needed)

          // Position based on effect type
          let startX, startY;
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;

          switch (physicsEffect) {
            case "drop":
              startX = Math.random() * (rect.width - size);
              startY = -size;
              break;
            case "bounce":
              startX = Math.random() * (rect.width - size);
              startY = centerY;
              break;
            case "explode":
              startX = centerX - size / 2;
              startY = centerY - size / 2;
              break;
            case "attract": {
              // Start from edges for more dramatic attract effect
              const edge = Math.floor(Math.random() * 4);
              switch (edge) {
                case 0: // top
                  startX = Math.random() * (rect.width - size);
                  startY = 0;
                  break;
                case 1: // right
                  startX = rect.width - size;
                  startY = Math.random() * (rect.height - size);
                  break;
                case 2: // bottom
                  startX = Math.random() * (rect.width - size);
                  startY = rect.height - size;
                  break;
                case 3: // left
                  startX = 0;
                  startY = Math.random() * (rect.height - size);
                  break;
                default:
                  startX = Math.random() * (rect.width - size);
                  startY = Math.random() * (rect.height - size);
              }
              break;
            }
            case "repel": {
              // Start from center for more dramatic repel effect
              startX = centerX - size / 2 + (Math.random() - 0.5) * 20;
              startY = centerY - size / 2 + (Math.random() - 0.5) * 20;
              break;
            }
            default:
              startX = Math.random() * (rect.width - size);
              startY = -size;
          }

          physicsObject.style.left = startX + "px";
          physicsObject.style.top = startY + "px";

          previewBox.appendChild(physicsObject);

          // Animate based on effect type
          const animationDuration = Math.min(duration || 5, 3); // Limit duration for preview

          switch (physicsEffect) {
            case "drop":
              gsap.to(physicsObject, {
                duration: animationDuration,
                y: rect.height + size,
                ease: "power2.in",
                delay: i * 0.1,
              });
              break;
            case "bounce": {
              // Create bouncing animation
              const timeline = gsap.timeline({ delay: i * 0.1 });
              timeline
                .to(physicsObject, {
                  duration: animationDuration / 4,
                  y: -50,
                  ease: "power2.out",
                })
                .to(physicsObject, {
                  duration: animationDuration / 4,
                  y: 0,
                  ease: "power2.in",
                })
                .to(physicsObject, {
                  duration: animationDuration / 4,
                  y: -30,
                  ease: "power2.out",
                })
                .to(physicsObject, {
                  duration: animationDuration / 4,
                  y: 0,
                  ease: "power2.in",
                });
              gsap.to(physicsObject, {
                duration: animationDuration,
                x: (Math.random() - 0.5) * 100,
                delay: i * 0.1,
              });
              break;
            }
            case "explode": {
              const angle = (i / (objectCount || 10)) * Math.PI * 2;
              const distance = 80 + Math.random() * 40;
              gsap.to(physicsObject, {
                duration: animationDuration,
                x: Math.cos(angle) * distance,
                y: Math.sin(angle) * distance,
                rotation: Math.random() * 360,
                ease: "power2.out",
                delay: i * 0.05,
              });
              break;
            }
            case "attract": {
              // Create a more realistic attract animation - accelerating toward center
              const timeline = gsap.timeline({ delay: i * 0.1 });

              // Start slow, then accelerate toward center (like real attraction)
              timeline
                .to(physicsObject, {
                  duration: animationDuration * 0.8,
                  x: centerX - size / 2,
                  y: centerY - size / 2,
                  ease: "power3.in", // Accelerating ease to simulate attraction force
                  rotation: Math.random() * 360,
                })
                // Then they cluster and vibrate slightly at center
                .to(physicsObject, {
                  duration: animationDuration * 0.2,
                  x: centerX - size / 2 + (Math.random() - 0.5) * 15,
                  y: centerY - size / 2 + (Math.random() - 0.5) * 15,
                  ease: "elastic.out(1, 0.5)",
                  scale: 0.8 + Math.random() * 0.4, // Slight size variation
                });
              break;
            }
            case "repel": {
              // Create a more realistic repel animation - fast initial push, then slow down
              const repelAngle = Math.atan2(startY - centerY, startX - centerX);
              const repelDistance = 120 + Math.random() * 80; // Variable distance

              const timeline = gsap.timeline({ delay: i * 0.05 });

              // Fast initial repulsion (like an explosion)
              timeline
                .to(physicsObject, {
                  duration: animationDuration * 0.3,
                  x: startX + Math.cos(repelAngle) * repelDistance * 0.7,
                  y: startY + Math.sin(repelAngle) * repelDistance * 0.7,
                  ease: "power3.out", // Fast start, then slow down
                  rotation: Math.random() * 720, // Spinning effect
                  scale: 1.2, // Slight expansion from force
                })
                // Then continue moving but slower (like real physics)
                .to(physicsObject, {
                  duration: animationDuration * 0.7,
                  x: startX + Math.cos(repelAngle) * repelDistance,
                  y: startY + Math.sin(repelAngle) * repelDistance,
                  ease: "power2.out",
                  scale: 1, // Return to normal size
                });
              break;
            }
          }

          // Remove object after animation
          setTimeout(() => {
            if (physicsObject.parentNode) {
              physicsObject.remove();
            }
          }, (animationDuration + i * 0.1 + 1) * 1000);
        }
      }, (delay || 0) * 1000);
    }

    createPhysicsPreview();
  };

  const previewRippleEffect = () => {
    if (!previewRef.current || !elementRef.current) return;

    const {
      rippleEffect,
      size,
      color,
      opacity,
      rippleOrigin,
      customOrigin,
      duration,
      delay,
      easing,
      allowMultiple,
    } = config;

    function createRipplePreview() {
      setTimeout(() => {
        const previewBox = previewRef.current!;
        const element = elementRef.current!;
        const rect = element.getBoundingClientRect();
        const previewRect = previewBox.getBoundingClientRect();

        // Clear existing ripples if multiple not allowed
        if (!allowMultiple) {
          const existingRipples =
            previewBox.querySelectorAll(".ripple-preview");
          existingRipples.forEach((ripple) => ripple.remove());
        }

        // Calculate ripple size
        let rippleSize;
        switch (size) {
          case "small":
            rippleSize = 50;
            break;
          case "medium":
            rippleSize = 100;
            break;
          case "large":
            rippleSize = 150;
            break;
          case "auto":
            rippleSize = Math.max(rect.width, rect.height) * 1.5;
            break;
          default:
            rippleSize = 100;
        }

        // Calculate ripple position
        let rippleX, rippleY;
        switch (rippleOrigin) {
          case "click":
            // For preview, simulate click at a random position
            rippleX = rect.left - previewRect.left + Math.random() * rect.width;
            rippleY = rect.top - previewRect.top + Math.random() * rect.height;
            break;
          case "custom":
            rippleX =
              rect.left -
              previewRect.left +
              ((customOrigin?.x || 50) / 100) * rect.width;
            rippleY =
              rect.top -
              previewRect.top +
              ((customOrigin?.y || 50) / 100) * rect.height;
            break;
          case "center":
          default:
            rippleX = rect.left - previewRect.left + rect.width / 2;
            rippleY = rect.top - previewRect.top + rect.height / 2;
        }

        // Create ripple effect based on type
        switch (rippleEffect) {
          case "material": {
            const ripple = document.createElement("div");
            ripple.className = "ripple-preview";
            ripple.style.position = "absolute";
            ripple.style.borderRadius = "50%";
            ripple.style.backgroundColor = color || "#2563eb";
            ripple.style.opacity = (opacity || 0.3).toString();
            ripple.style.pointerEvents = "none";
            ripple.style.transform = "scale(0)";
            ripple.style.zIndex = "10";
            ripple.style.width = rippleSize + "px";
            ripple.style.height = rippleSize + "px";
            ripple.style.left = rippleX - rippleSize / 2 + "px";
            ripple.style.top = rippleY - rippleSize / 2 + "px";

            previewBox.appendChild(ripple);

            gsap.fromTo(
              ripple,
              {
                scale: 0,
                opacity: opacity || 0.25,
              },
              {
                duration: duration || 0.8,
                scale: 1,
                opacity: 0,
                ease: easing || "power2.out",
                onComplete: () => ripple.remove(),
              }
            );
            break;
          }

          case "water": {
            // Create multiple waves for water effect
            for (let i = 0; i < 3; i++) {
              const wave = document.createElement("div");
              wave.className = "ripple-preview";
              wave.style.position = "absolute";
              wave.style.borderRadius = "50%";
              wave.style.border = `2px solid ${color || "#2563eb"}`;
              wave.style.backgroundColor = "transparent";
              wave.style.opacity = (opacity || 0.3).toString();
              wave.style.pointerEvents = "none";
              wave.style.transform = "scale(0)";
              wave.style.zIndex = "10";
              wave.style.width = rippleSize + "px";
              wave.style.height = rippleSize + "px";
              wave.style.left = rippleX - rippleSize / 2 + "px";
              wave.style.top = rippleY - rippleSize / 2 + "px";

              previewBox.appendChild(wave);

              gsap.to(wave, {
                duration: duration || 0.8,
                scale: 1,
                opacity: 0,
                ease: easing || "power2.out",
                delay: i * ((duration || 0.8) * 0.15),
                onComplete: () => wave.remove(),
              });
            }
            break;
          }

          case "pulse": {
            const pulse = document.createElement("div");
            pulse.className = "ripple-preview";
            pulse.style.position = "absolute";
            pulse.style.borderRadius = "50%";
            pulse.style.backgroundColor = color || "#2563eb";
            pulse.style.opacity = (opacity || 0.3).toString();
            pulse.style.pointerEvents = "none";
            pulse.style.transform = "scale(1)";
            pulse.style.zIndex = "10";
            pulse.style.width = rippleSize + "px";
            pulse.style.height = rippleSize + "px";
            pulse.style.left = rippleX - rippleSize / 2 + "px";
            pulse.style.top = rippleY - rippleSize / 2 + "px";

            previewBox.appendChild(pulse);

            gsap.to(pulse, {
              duration: (duration || 0.8) / 2,
              scale: 1.5,
              opacity: (opacity || 0.25) * 0.7,
              ease: "power2.out",
              yoyo: true,
              repeat: 1,
              onComplete: () => pulse.remove(),
            });
            break;
          }

          case "shockwave": {
            const shockwave = document.createElement("div");
            shockwave.className = "ripple-preview";
            shockwave.style.position = "absolute";
            shockwave.style.borderRadius = "50%";
            shockwave.style.border = `3px solid ${color || "#2563eb"}`;
            shockwave.style.backgroundColor = "transparent";
            shockwave.style.opacity = (opacity || 0.3).toString();
            shockwave.style.pointerEvents = "none";
            shockwave.style.transform = "scale(0)";
            shockwave.style.zIndex = "10";
            shockwave.style.boxShadow = `0 0 20px ${color || "#2563eb"}`;
            shockwave.style.width = rippleSize + "px";
            shockwave.style.height = rippleSize + "px";
            shockwave.style.left = rippleX - rippleSize / 2 + "px";
            shockwave.style.top = rippleY - rippleSize / 2 + "px";

            previewBox.appendChild(shockwave);

            // Enhanced shockwave animation with multiple phases
            gsap
              .timeline()
              .to(shockwave, {
                duration: (duration || 0.8) * 0.3,
                scale: 0.3,
                opacity: opacity || 0.25,
                ease: "power2.out",
              })
              .to(shockwave, {
                duration: (duration || 0.8) * 0.7,
                scale: 1,
                opacity: 0,
                ease: "power3.out",
                onComplete: () => shockwave.remove(),
              });
            break;
          }
        }
      }, (delay || 0) * 1000);
    }

    createRipplePreview();
  };

  const previewTypewriterEffect = () => {
    if (!elementRef.current) return;

    const {
      strings,
      typeSpeed,
      backSpeed,
      startDelay,
      showCursor,
      cursorChar,
      smartBackspace,
      loop,
    } = config;

    // Load Typed.js library if not already loaded
    if (typeof window.Typed === "undefined") {
      const script = document.createElement("script");
      script.src = "https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js";
      script.onload = () => {
        executeTypewriterPreview();
      };
      document.head.appendChild(script);
    } else {
      executeTypewriterPreview();
    }

    function executeTypewriterPreview() {
      if (!elementRef.current) return;

      // Clear existing content and any existing Typed instances
      elementRef.current.innerHTML = "";

      // Destroy any existing Typed instance
      if ((elementRef.current as any)._typed) {
        (elementRef.current as any)._typed.destroy();
      }

      const typedOptions = {
        strings:
          strings && strings.length > 0
            ? strings
            : ["Hello World!", "This is a typewriter effect."],
        typeSpeed: typeSpeed || 50,
        backSpeed: backSpeed || 30,
        startDelay: startDelay || 0,
        backDelay: 700,
        loop: loop || false,
        showCursor: showCursor !== false,
        cursorChar: cursorChar || "|",
        smartBackspace: smartBackspace !== false,
        onComplete: () => {
          // For preview, restart after a delay if not looping
          if (!loop) {
            setTimeout(() => {
              if (elementRef.current && (elementRef.current as any)._typed) {
                (elementRef.current as any)._typed.reset();
              }
            }, 2000);
          }
        },
      };

      // Initialize Typed.js
      const typed = new (window as any).Typed(elementRef.current, typedOptions);
      (elementRef.current as any)._typed = typed;
    }
  };

  const previewFillEffect = (isHoverOn: boolean = true) => {
    if (!elementRef.current) return;

    const { origin, direction, duration, delay, easing, color, shape, borderRadius } = config;

    // Define clip-path patterns for different origins and shapes
    const getClipPath = (origin: string, isStart: boolean, shape: string) => {
      if (shape === 'circle') {
        const circlePatterns: Record<string, { start: string; end: string }> = {
          'bottom-right': {
            start: 'circle(0% at 100% 100%)',
            end: 'circle(150% at 100% 100%)'
          },
          'bottom-left': {
            start: 'circle(0% at 0% 100%)',
            end: 'circle(150% at 0% 100%)'
          },
          'top-right': {
            start: 'circle(0% at 100% 0%)',
            end: 'circle(150% at 100% 0%)'
          },
          'top-left': {
            start: 'circle(0% at 0% 0%)',
            end: 'circle(150% at 0% 0%)'
          },
          'center': {
            start: 'circle(0% at 50% 50%)',
            end: 'circle(150% at 50% 50%)'
          },
          'left': {
            start: 'circle(0% at 0% 50%)',
            end: 'circle(150% at 0% 50%)'
          },
          'right': {
            start: 'circle(0% at 100% 50%)',
            end: 'circle(150% at 100% 50%)'
          },
          'top': {
            start: 'circle(0% at 50% 0%)',
            end: 'circle(150% at 50% 0%)'
          },
          'bottom': {
            start: 'circle(0% at 50% 100%)',
            end: 'circle(150% at 50% 100%)'
          }
        };
        const pattern = circlePatterns[origin] || circlePatterns['center'];
        return isStart ? pattern.start : pattern.end;
      }
      
      if (shape === 'ellipse') {
        const ellipsePatterns: Record<string, { start: string; end: string }> = {
          'bottom-right': {
            start: 'ellipse(0% 0% at 100% 100%)',
            end: 'ellipse(150% 100% at 100% 100%)'
          },
          'bottom-left': {
            start: 'ellipse(0% 0% at 0% 100%)',
            end: 'ellipse(150% 100% at 0% 100%)'
          },
          'top-right': {
            start: 'ellipse(0% 0% at 100% 0%)',
            end: 'ellipse(150% 100% at 100% 0%)'
          },
          'top-left': {
            start: 'ellipse(0% 0% at 0% 0%)',
            end: 'ellipse(150% 100% at 0% 0%)'
          },
          'center': {
            start: 'ellipse(0% 0% at 50% 50%)',
            end: 'ellipse(150% 100% at 50% 50%)'
          },
          'left': {
            start: 'ellipse(0% 0% at 0% 50%)',
            end: 'ellipse(150% 100% at 0% 50%)'
          },
          'right': {
            start: 'ellipse(0% 0% at 100% 50%)',
            end: 'ellipse(150% 100% at 100% 50%)'
          },
          'top': {
            start: 'ellipse(0% 0% at 50% 0%)',
            end: 'ellipse(150% 100% at 50% 0%)'
          },
          'bottom': {
            start: 'ellipse(0% 0% at 50% 100%)',
            end: 'ellipse(150% 100% at 50% 100%)'
          }
        };
        const pattern = ellipsePatterns[origin] || ellipsePatterns['center'];
        return isStart ? pattern.start : pattern.end;
      }
      
      if (shape === 'rounded-rectangle') {
        const radius = borderRadius || 12;
        const roundedPatterns: Record<string, { start: string; end: string }> = {
          'bottom-right': {
            start: `inset(100% 0% 0% 100% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'bottom-left': {
            start: `inset(100% 100% 0% 0% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'top-right': {
            start: `inset(0% 0% 100% 100% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'top-left': {
            start: `inset(0% 100% 100% 0% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'center': {
            start: `inset(50% 50% 50% 50% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'left': {
            start: `inset(0% 100% 0% 0% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'right': {
            start: `inset(0% 0% 0% 100% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'top': {
            start: `inset(0% 0% 100% 0% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          },
          'bottom': {
            start: `inset(100% 0% 0% 0% round ${radius}px)`,
            end: `inset(0% 0% 0% 0% round ${radius}px)`
          }
        };
        const pattern = roundedPatterns[origin] || roundedPatterns['bottom-right'];
        return isStart ? pattern.start : pattern.end;
      }
      
      // Default to rectangle (original polygon patterns)
      const rectanglePatterns: Record<string, { start: string; end: string }> = {
        'bottom-right': {
          start: 'polygon(100% 100%, 100% 100%, 100% 100%, 100% 100%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'bottom-left': {
          start: 'polygon(0% 100%, 0% 100%, 0% 100%, 0% 100%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'top-right': {
          start: 'polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'top-left': {
          start: 'polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'center': {
          start: 'polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'left': {
          start: 'polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'right': {
          start: 'polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'top': {
          start: 'polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        },
        'bottom': {
          start: 'polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)',
          end: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
        }
      };
      
      const pattern = rectanglePatterns[origin || 'bottom-right'] || rectanglePatterns['bottom-right'];
      return isStart ? pattern.start : pattern.end;
    };

    const originStr = typeof origin === 'string' ? origin : 'bottom-right';
    const shapeStr = typeof shape === 'string' ? shape : 'rectangle';
    const startClip = direction === 'reveal' ? getClipPath(originStr, true, shapeStr) : getClipPath(originStr, false, shapeStr);
    const endClip = direction === 'reveal' ? getClipPath(originStr, false, shapeStr) : getClipPath(originStr, true, shapeStr);

    // Set up element for pseudo-element
    elementRef.current.style.position = 'relative';
    elementRef.current.style.overflow = 'hidden';

    // Get or create pseudo-element
    let pseudoElement = elementRef.current.querySelector('.fill-pseudo-element') as HTMLElement;
    if (!pseudoElement) {
      pseudoElement = document.createElement('div');
      pseudoElement.className = 'fill-pseudo-element';
      pseudoElement.style.position = 'absolute';
      pseudoElement.style.top = '0';
      pseudoElement.style.left = '0';
      pseudoElement.style.right = '0';
      pseudoElement.style.bottom = '0';
      pseudoElement.style.clipPath = startClip;
      pseudoElement.style.zIndex = '-1';
      pseudoElement.style.pointerEvents = 'none';

      // Insert pseudo-element
      elementRef.current.insertBefore(pseudoElement, elementRef.current.firstChild);
    }

    // Always update the background color (whether new or existing element)
    pseudoElement.style.backgroundColor = color || '#3b82f6';

    // Animate pseudo-element's clip path based on hover state
    const targetClip = isHoverOn ? endClip : startClip;
    gsap.to(pseudoElement, {
      clipPath: targetClip,
      duration: duration || 1,
      delay: isHoverOn ? (delay || 0) : 0,
      ease: isHoverOn ? (easing || "power2.out") : "power2.inOut",
    });
  };

  useEffect(() => {
    resetElement();

    // Cleanup any existing magnetic listeners
    const magneticElement = previewRef.current as HTMLElementWithMagnetic;
    if (magneticElement && magneticElement._magneticCleanup) {
      magneticElement._magneticCleanup();
    }
  }, [config]);

  // Cleanup on unmount
  useEffect(() => {
    const element = elementRef.current as HTMLElementWithTilt | null;
    return () => {
      if (element && element.vanillaTilt) {
        element.vanillaTilt.destroy();
      }
    };
  }, []);

  const getTriggerText = () => {
    if (effectType === "magnetic") {
      return "Move your mouse over the demo box to see magnetic effect";
    }

    if (effectType === "pathdraw") {
      return "Click 'Play' to see path drawing animation";
    }

    if (effectType === "typewriter") {
      const trigger = config.trigger || "timeline";
      switch (trigger) {
        case "click":
          return "Click the text to start typewriter effect";
        case "hover":
          return "Hover over the text to start typewriter effect";
        case "timeline":
          return "Click 'Play' to start typewriter effect";
        default:
          return "Click 'Play' to start typewriter effect";
      }
    }

    if (effectType === "confetti") {
      const trigger = config.trigger || "click";
      switch (trigger) {
        case "click":
          return "Click the demo box to launch confetti";
        case "hover":
          return "Hover over the demo box to launch confetti";
        case "timeline":
          return "Click 'Play' to launch confetti";
        default:
          return "Click 'Play' to launch confetti";
      }
    }

    if (effectType === "physics") {
      const trigger = config.trigger || "click";
      switch (trigger) {
        case "click":
          return "Click the demo box to start physics simulation";
        case "hover":
          return "Hover over the demo box to start physics simulation";
        case "timeline":
          return "Click 'Play' to start physics simulation";
        default:
          return "Click 'Play' to start physics simulation";
      }
    }

    if (effectType === "ripple") {
      const trigger = config.trigger || "click";
      switch (trigger) {
        case "click":
          return "Click the demo box to create ripple effect";
        case "hover":
          return "Hover over the demo box to create ripple effect";
        case "timeline":
          return "Click 'Play' to create ripple effect";
        default:
          return "Click 'Play' to create ripple effect";
      }
    }

    const trigger = config.trigger || "timeline";
    switch (trigger) {
      case "click":
        return "Click the demo box to trigger";
      case "hover":
        return "Hover over the demo box to trigger";
      case "timeline":
        return "Click 'Play' to see timeline animation";
      case "continuous":
        return "Click 'Play' to see continuous animation";
      default:
        return "Click 'Play' to see the animation";
    }
  };

  const handleDemoInteraction = (eventType: "click" | "hover") => {
    const trigger = config.trigger || "timeline";
    if (trigger === eventType) {
      executePreview();
    }
  };

  const handleHoverEnter = () => {
    const trigger = config.trigger || "timeline";
    if (trigger === "hover") {
      setIsHovered(true);
      if (effectType === "fill") {
        previewFillEffect(true);
      } else {
        executePreview();
      }
    }
  };

  const handleHoverLeave = () => {
    const trigger = config.trigger || "timeline";
    if (trigger === "hover") {
      setIsHovered(false);
      if (effectType === "fill") {
        previewFillEffect(false);
      }
    }
  };

  // Check if we should show text preview instead of demo box
  const shouldShowTextPreview = () => {
    if (!config.objectIds || config.objectIds.length === 0) return false;

    // Show text preview for typewriter effects or if any objectId starts with "text"
    return (
      effectType === "typewriter" ||
      config.objectIds.some((id: string) => id.toLowerCase().startsWith("text"))
    );
  };

  const getPreviewText = () => {
    if (!config.objectIds || config.objectIds.length === 0) return "Demo";
    return config.objectIds[0]; // Show the first item as preview text
  };

  return (
    <div className="w-96 bg-gray-50 border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-900">Live Preview</h3>
        {(config.trigger === "timeline" ||
          config.trigger === "continuous" ||
          !config.trigger) && (
          <button
            onClick={executePreview}
            className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-700 transition-colors cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            Play
          </button>
        )}
      </div>

      <div
        ref={previewRef}
        className="h-32 bg-white border border-gray-300 rounded flex items-center justify-center relative overflow-hidden cursor-pointer"
        onClick={() => handleDemoInteraction("click")}
        onMouseEnter={handleHoverEnter}
        onMouseLeave={handleHoverLeave}
      >
        {effectType === "pathdraw" ? (
          <div
            ref={elementRef}
            className="w-full h-full relative flex items-center justify-center"
          >
            {/* SVG will be dynamically added here by the preview function */}
          </div>
        ) : shouldShowTextPreview() || effectType === "typewriter" ? (
          <div
            ref={elementRef}
            className="px-6 py-3 text-gray-800 font-medium text-base text-center max-w-full overflow-hidden"
            style={{ wordBreak: "break-word" }}
          >
            {effectType === "typewriter" ? "" : getPreviewText()}
          </div>
        ) : (
          <div
            ref={elementRef}
            className="w-24 h-20 bg-blue-500 shadow-md flex items-center justify-center text-white font-bold text-sm"
            style={{
              borderRadius: config.shape === 'rounded-rectangle' 
                ? `${config.borderRadius || 12}px` 
                : config.shape === 'circle' 
                  ? '50%' 
                  : '6px' // Default rounded class value
            }}
          >
            Demo
          </div>
        )}
      </div>

      <p className="text-xs text-gray-600 mt-3 text-center">
        {getTriggerText()}
      </p>
    </div>
  );
}
