import { useState, useEffect } from "react";
import { generateShakeCode, type ShakeConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";
import TargetingMethodSelector from "./TargetingMethodSelector";

export default function ShakeGenerator() {
  const [config, setConfig] = useState<ShakeConfig>({
    objectIds: [],
    targetingMethod: "data-model-id",
    trigger: "click",
    intensity: 1, // Changed to 1% viewport for better responsiveness
    duration: 0.5,
    direction: "both",
    frequency: 10,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateShakeCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Shake Effect Generator
      </h2>

      <div className="space-y-4">
        <TargetingMethodSelector

          value={config.targetingMethod}

          onChange={(method) =>

            setConfig((prev) => ({ ...prev, targetingMethod: method }))

          }

        />


        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. error1, warning2, feedback3"
          label={config.targetingMethod === "data-model-id" ? "Object IDs" : "Accessibility Text Values"}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">On Click</option>
              <option value="hover">On Hover</option>
              <option value="timeline">Timeline Start</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direction:
            </label>
            <select
              value={config.direction}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  direction: e.target.value as
                    | "horizontal"
                    | "vertical"
                    | "both",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="both">Both Directions</option>
              <option value="horizontal">Horizontal Only</option>
              <option value="vertical">Vertical Only</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Intensity (%):
            </label>
            <input
              type="number"
              value={config.intensity}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  intensity: parseInt(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              step="0.1"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 0.5,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Frequency (shakes/sec):
            </label>
            <input
              type="number"
              value={config.frequency}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  frequency: parseInt(e.target.value) || 10,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="shake" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
