import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

type GeneratorType =
  | "tilt"
  | "rotate"
  | "fade"
  | "slide"
  | "pulse"
  | "shake"
  | "bounce"
  | "zoom"
  | "morph"
  | "glitch"
  | "neon"
  | "magnetic"
  | "transform3d"
  | "pathdraw"
  | "confetti"
  | "physics"
  | "ripple"
  | "typewriter"
  | "fill";

interface EffectCategory {
  name: string;
  effects: Array<{
    id: GeneratorType;
    name: string;
  }>;
}

interface SidebarProps {
  activeGenerator: GeneratorType;
  onGeneratorChange: (generator: GeneratorType) => void;
}

const effectCategories: EffectCategory[] = [
  {
    name: "Basic",
    effects: [
      { id: "fade", name: "Fade" },
      { id: "slide", name: "Slide" },
      { id: "bounce", name: "<PERSON><PERSON><PERSON>" },
      { id: "zoom", name: "Zoom" },
      { id: "fill", name: "Fill" },
    ],
  },
  {
    name: "Motion",
    effects: [
      { id: "pulse", name: "Pulse" },
      { id: "shake", name: "Shake" },
      { id: "tilt", name: "Tilt" },
      { id: "rotate", name: "Rotate" },
    ],
  },
  {
    name: "Advanced",
    effects: [
      { id: "morph", name: "Morph" },
      { id: "glitch", name: "Glitch" },
      { id: "neon", name: "Neon" },
      { id: "magnetic", name: "Magnetic" },
      { id: "transform3d", name: "3D Transform" },
    ],
  },
  {
    name: "Interactive",
    effects: [
      { id: "pathdraw", name: "Path Draw" },
      { id: "confetti", name: "Confetti" },
      { id: "physics", name: "Physics" },
      { id: "ripple", name: "Ripple" },
      { id: "typewriter", name: "Typewriter" },
    ],
  },
];

export default function Sidebar({
  activeGenerator,
  onGeneratorChange,
}: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const contentVariants = {
    expanded: {
      opacity: 1,
      transition: {
        delay: 0.1,
        duration: 0.2,
      },
    },
    collapsed: {
      opacity: 0,
      transition: {
        duration: 0.1,
      },
    },
  };

  return (
    <>
      {/* Mobile Menu Button - only visible on mobile */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-3 bg-white rounded-lg shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Toggle menu"
        >
          <svg
            className="w-6 h-6 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={
                isMobileMenuOpen
                  ? "M6 18L18 6M6 6l12 12"
                  : "M4 6h16M4 12h16M4 18h16"
              }
            />
          </svg>
        </button>
      </div>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.div
        className={`bg-white border-r border-gray-200 shadow-lg flex flex-col h-full z-40 lg:relative lg:translate-x-0 fixed inset-y-0 left-0 transition-transform duration-300 ease-in-out w-[280px] lg:w-auto ${
          isMobileMenuOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0"
        }`}
        variants={{
          expanded: {
            width: "280px",
            transition: {
              duration: 0.3,
              ease: "easeInOut",
            },
          },
          collapsed: {
            width: "60px",
            transition: {
              duration: 0.3,
              ease: "easeInOut",
            },
          },
        }}
        animate={isCollapsed ? "collapsed" : "expanded"}
        initial="expanded"
      >
        {/* Header with toggle button */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.h2
                className="text-lg font-semibold text-gray-900"
                variants={contentVariants}
                initial="expanded"
                animate="expanded"
                exit="collapsed"
              >
                Effects
              </motion.h2>
            )}
          </AnimatePresence>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:block p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            <motion.div
              animate={{ rotate: isCollapsed ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </motion.div>
          </button>
        </div>

        {/* Navigation content */}
        <div className="flex-1 overflow-y-auto">
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                className="p-4 space-y-6"
                variants={contentVariants}
                initial="expanded"
                animate="expanded"
                exit="collapsed"
              >
                {effectCategories.map((category) => (
                  <div key={category.name}>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                      {category.name}
                    </h3>
                    <div className="space-y-1">
                      {category.effects.map((effect) => (
                        <button
                          key={effect.id}
                          onClick={() => {
                            onGeneratorChange(effect.id);
                            setIsMobileMenuOpen(false);
                          }}
                          className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            activeGenerator === effect.id
                              ? "bg-blue-500 text-white shadow-md"
                              : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          {effect.name}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Collapsed state - only on desktop */}
          {isCollapsed && (
            <div className="hidden lg:block p-2">
              {effectCategories.map((category) =>
                category.effects.map((effect) => (
                  <button
                    key={effect.id}
                    onClick={() => {
                      onGeneratorChange(effect.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full mb-1 px-2 py-2 rounded-md text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      activeGenerator === effect.id
                        ? "bg-blue-500 text-white"
                        : "text-gray-400 hover:bg-gray-100 hover:text-gray-700"
                    }`}
                    title={effect.name}
                  >
                    {effect.name.charAt(0).toUpperCase()}
                  </button>
                ))
              )}
            </div>
          )}
        </div>
      </motion.div>
    </>
  );
}
