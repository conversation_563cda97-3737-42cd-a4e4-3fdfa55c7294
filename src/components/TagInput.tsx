import { useState, useRef, type KeyboardEvent } from "react";

type TagInputProps = {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  label?: string;
};

export default function TagInput({
  value,
  onChange,
  placeholder = "Type and press Enter or comma to add...",
  label = "Object IDs",
}: TagInputProps) {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const addTag = (tagText: string) => {
    const trimmed = tagText.trim();
    if (trimmed && !value.includes(trimmed)) {
      onChange([...value, trimmed]);
    }
    setInputValue("");
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === "Backspace" && inputValue === "" && value.length > 0) {
      // Remove last tag if input is empty and backspace is pressed
      removeTag(value.length - 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Check if user typed a comma or space - auto-add the tag
    if (newValue.includes(",") || newValue.includes(" ")) {
      const parts = newValue.split(/[,\s]+/);
      const tagsToAdd = parts.slice(0, -1).filter((tag) => tag.trim());
      const remaining = parts[parts.length - 1];

      tagsToAdd.forEach((tag) => {
        const trimmed = tag.trim();
        if (trimmed && !value.includes(trimmed)) {
          onChange([...value, trimmed]);
        }
      });

      setInputValue(remaining);
    } else {
      setInputValue(newValue);
    }
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} (from Accessibility - alternative text):
      </label>

      <div
        onClick={handleContainerClick}
        className="min-h-[42px] w-full px-3 py-2 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent bg-white cursor-text"
      >
        <div className="flex flex-wrap gap-2 items-center">
          {value.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200 transition-colors"
            >
              {tag}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removeTag(index);
                }}
                className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                aria-label={`Remove ${tag}`}
              >
                ×
              </button>
            </span>
          ))}

          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={value.length === 0 ? placeholder : ""}
            className="flex-1 min-w-[120px] outline-none bg-transparent text-sm"
          />
        </div>
      </div>

      <p className="text-xs text-gray-500 mt-1 font-medium">
        Type and press Enter, comma, or space to add tags. Click × to remove.
      </p>
    </div>
  );
}
