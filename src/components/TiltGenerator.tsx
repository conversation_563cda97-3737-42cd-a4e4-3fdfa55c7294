import { useState, useEffect } from "react";
import { generateTiltCode, type TiltConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";
import TargetingMethodSelector from "./TargetingMethodSelector";

export default function TiltGenerator() {
  const [config, setConfig] = useState<TiltConfig>({
    objectIds: [],
    targetingMethod: "data-model-id",
    maxTilt: 15,
    speed: 400,
    glare: true,
    scale: 1.02,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateTiltCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Tilt Effect Generator
      </h2>

      <div className="space-y-4">
        <TargetingMethodSelector
          value={config.targetingMethod}
          onChange={(method) =>
            setConfig((prev) => ({ ...prev, targetingMethod: method }))
          }
        />

        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. card1, card2, buttonMain"
          label={
            config.targetingMethod === "data-model-id"
              ? "Object IDs"
              : "Accessibility Text Values"
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Tilt (degrees):
            </label>
            <input
              type="number"
              value={config.maxTilt}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  maxTilt: parseInt(e.target.value) || 15,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Speed (ms):
            </label>
            <input
              type="number"
              value={config.speed}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  speed: parseInt(e.target.value) || 400,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Scale Factor:
            </label>
            <input
              type="number"
              step="0.01"
              value={config.scale}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  scale: parseFloat(e.target.value) || 1.02,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Glare Effect:
            </label>
            <select
              value={config.glare.toString()}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  glare: e.target.value === "true",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="true">Enabled</option>
              <option value="false">Disabled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="tilt" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
