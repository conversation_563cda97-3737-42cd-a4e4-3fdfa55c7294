import { useState, useEffect } from "react";
import { generateMorphCode, type MorphConfig } from "../utils/codeTemplates";
import CodeOutput from "./CodeOutput";
import PreviewBox from "./PreviewBox";
import TagInput from "./TagInput";

export default function MorphGenerator() {
  const [config, setConfig] = useState<MorphConfig>({
    objectIds: [],
    trigger: "click",
    effect: "skew",
    duration: 1,
    delay: 0,
    easing: "ease",
    skewX: 15,
    skewY: 5,
    perspective: 100, // Changed to 100% viewport for better responsiveness
    rotateX: 15,
    rotateY: 15,
    scaleX: 1.2,
    scaleY: 1.2,
    translateX: 0,
    translateY: 0,
    shapeType: "diamond",
    intensity: 5,
  });
  const [generatedCode, setGeneratedCode] = useState("");

  const handleObjectIdChange = (tags: string[]) => {
    setConfig((prev) => ({ ...prev, objectIds: tags }));
  };

  // Auto-generate code whenever config changes
  useEffect(() => {
    if (config.objectIds.length > 0) {
      const code = generateMorphCode(config);
      setGeneratedCode(code);
    } else {
      setGeneratedCode("");
    }
  }, [config]);

  const renderEffectControls = () => {
    switch (config.effect) {
      case "skew":
        return (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Skew X (degrees):
              </label>
              <input
                type="number"
                value={config.skewX}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    skewX: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-45"
                max="45"
                step="1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Skew Y (degrees):
              </label>
              <input
                type="number"
                value={config.skewY}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    skewY: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-45"
                max="45"
                step="1"
              />
            </div>
          </div>
        );

      case "perspective":
        return (
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Perspective (%):
              </label>
              <input
                type="number"
                value={config.perspective}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    perspective: parseFloat(e.target.value) || 100,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="10"
                max="200"
                step="5"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rotate X (degrees):
              </label>
              <input
                type="number"
                value={config.rotateX}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    rotateX: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-90"
                max="90"
                step="5"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rotate Y (degrees):
              </label>
              <input
                type="number"
                value={config.rotateY}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    rotateY: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-90"
                max="90"
                step="5"
              />
            </div>
          </div>
        );

      case "matrix":
        return (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Scale X:
              </label>
              <input
                type="number"
                value={config.scaleX}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    scaleX: parseFloat(e.target.value) || 1,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="0.1"
                max="3"
                step="0.1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Scale Y:
              </label>
              <input
                type="number"
                value={config.scaleY}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    scaleY: parseFloat(e.target.value) || 1,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="0.1"
                max="3"
                step="0.1"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Translate X (%):
              </label>
              <input
                type="number"
                value={config.translateX}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    translateX: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-200"
                max="200"
                step="10"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Translate Y (%):
              </label>
              <input
                type="number"
                value={config.translateY}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    translateY: parseFloat(e.target.value) || 0,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="-200"
                max="200"
                step="10"
              />
            </div>
          </div>
        );

      case "shape":
        return (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shape Type:
              </label>
              <select
                value={config.shapeType}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    shapeType: e.target.value as
                      | "diamond"
                      | "parallelogram"
                      | "trapezoid"
                      | "custom",
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="diamond">Diamond</option>
                <option value="parallelogram">Parallelogram</option>
                <option value="trapezoid">Trapezoid</option>
                <option value="custom">Custom</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Intensity:
              </label>
              <input
                type="number"
                value={config.intensity}
                onChange={(e) =>
                  setConfig((prev) => ({
                    ...prev,
                    intensity: parseFloat(e.target.value) || 5,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1"
                max="20"
                step="1"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Morph Effect Generator
      </h2>

      <div className="space-y-4">
        <TagInput
          value={config.objectIds}
          onChange={handleObjectIdChange}
          placeholder="e.g. card1, card2, buttonMain"
          label="Object IDs"
        />

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trigger:
            </label>
            <select
              value={config.trigger}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  trigger: e.target.value as "click" | "hover" | "timeline",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="click">Click</option>
              <option value="hover">Hover</option>
              <option value="timeline">Timeline</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Effect Type:
            </label>
            <select
              value={config.effect}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  effect: e.target.value as
                    | "skew"
                    | "perspective"
                    | "matrix"
                    | "shape",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="skew">Skew</option>
              <option value="perspective">Perspective</option>
              <option value="matrix">Matrix</option>
              <option value="shape">Shape</option>
            </select>
          </div>
        </div>

        {renderEffectControls()}

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (seconds):
            </label>
            <input
              type="number"
              value={config.duration}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  duration: parseFloat(e.target.value) || 1,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0.1"
              max="5"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay (seconds):
            </label>
            <input
              type="number"
              value={config.delay}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  delay: parseFloat(e.target.value) || 0,
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="10"
              step="0.1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Easing:
            </label>
            <select
              value={config.easing}
              onChange={(e) =>
                setConfig((prev) => ({
                  ...prev,
                  easing: e.target.value as
                    | "ease"
                    | "ease-in"
                    | "ease-out"
                    | "ease-in-out"
                    | "linear",
                }))
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ease">Ease</option>
              <option value="ease-in">Ease In</option>
              <option value="ease-out">Ease Out</option>
              <option value="ease-in-out">Ease In-Out</option>
              <option value="linear">Linear</option>
            </select>
          </div>
        </div>
      </div>

      {/* Live Preview Section */}
      <div className="mt-8">
        <PreviewBox effectType="morph" config={config} />
      </div>

      {generatedCode && <CodeOutput code={generatedCode} />}
    </div>
  );
}
