import { useState, lazy, Suspense } from "react";
import { motion, AnimatePresence } from "framer-motion";
import "./App.css";
import Sidebar from "./components/Sidebar";

// Lazy load generator components for code splitting
const TiltGenerator = lazy(() => import("./components/TiltGenerator"));
const RotateGenerator = lazy(() => import("./components/RotateGenerator"));
const FadeGenerator = lazy(() => import("./components/FadeGenerator"));
const SlideGenerator = lazy(() => import("./components/SlideGenerator"));
const PulseGenerator = lazy(() => import("./components/PulseGenerator"));
const ShakeGenerator = lazy(() => import("./components/ShakeGenerator"));
const BounceGenerator = lazy(() => import("./components/BounceGenerator"));
const ZoomGenerator = lazy(() => import("./components/ZoomGenerator"));
const MorphGenerator = lazy(() => import("./components/MorphGenerator"));
const GlitchGenerator = lazy(() => import("./components/GlitchGenerator"));
const NeonGenerator = lazy(() => import("./components/NeonGenerator"));
const MagneticGenerator = lazy(() => import("./components/MagneticGenerator"));
const Transform3DGenerator = lazy(
  () => import("./components/Transform3DGenerator")
);
const PathDrawGenerator = lazy(() => import("./components/PathDrawGenerator"));
const ConfettiGenerator = lazy(() => import("./components/ConfettiGenerator"));
const PhysicsGenerator = lazy(() => import("./components/PhysicsGenerator"));
const RippleGenerator = lazy(() => import("./components/RippleGenerator"));
const TypewriterGenerator = lazy(
  () => import("./components/TypewriterGenerator")
);
const FillGenerator = lazy(() => import("./components/FillGenerator"));

// Loading component for lazy-loaded generators
const GeneratorLoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    <span className="ml-3 text-gray-600">Loading generator...</span>
  </div>
);

type GeneratorType =
  | "tilt"
  | "rotate"
  | "fade"
  | "slide"
  | "pulse"
  | "shake"
  | "bounce"
  | "zoom"
  | "morph"
  | "glitch"
  | "neon"
  | "magnetic"
  | "transform3d"
  | "pathdraw"
  | "confetti"
  | "physics"
  | "ripple"
  | "typewriter"
  | "fill";

function App() {
  const [activeGenerator, setActiveGenerator] = useState<GeneratorType>("fade");
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const handleGeneratorChange = (newGenerator: GeneratorType) => {
    setActiveGenerator(newGenerator);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0,
        delayChildren: 0,
      },
    },
  };

  // Title drops in from completely off-screen
  const titleVariants = {
    hidden: { opacity: 0, y: -200 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 15,
        stiffness: 120,
        mass: 1.5,
        delay: 0.2,
      },
    },
  };

  // Subtitle drops in from above like title
  const subtitleVariants = {
    hidden: { opacity: 0, y: -200 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 15,
        stiffness: 120,
        mass: 1.5,
        delay: 0.4,
      },
    },
  };

  // Simple fade + scale animation for content transitions
  const getContentVariants = () => {
    return {
      hidden: {
        opacity: 0,
        scale: 0.95,
      },
      visible: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 0.3,
          ease: [0.25, 0.46, 0.45, 0.94],
        },
      },
      exit: {
        opacity: 0,
        scale: 0.95,
        transition: {
          duration: 0.2,
          ease: [0.25, 0.46, 0.45, 0.94],
        },
      },
    };
  };

  // Initial content box animation (for first load)
  const initialContentVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.8,
      },
    },
  };

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50"
      style={{
        fontFamily: "Quicksand, sans-serif",
      }}
    >
      <motion.div
        className="flex h-screen"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Sidebar */}
        <Sidebar
          activeGenerator={activeGenerator}
          onGeneratorChange={handleGeneratorChange}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <motion.header className="text-center py-8 px-4 lg:px-8 pt-20 lg:pt-8">
            <motion.h1
              className="text-4xl lg:text-6xl font-medium text-gray-900 mb-4 leading-tight"
              variants={titleVariants}
            >
              TAM Storyline Tool
            </motion.h1>
            <motion.p
              className="text-lg lg:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed"
              variants={subtitleVariants}
            >
              Create custom effects for Articulate Storyline projects
            </motion.p>
          </motion.header>

          <div className="flex-1 overflow-y-auto px-4 lg:px-8 pb-8">
            {/* Generator Content */}
            <div className="relative overflow-hidden rounded-2xl">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeGenerator}
                  className="bg-white/95 rounded-2xl p-8 shadow-xl backdrop-blur-sm border border-gray-50/30"
                  variants={
                    isInitialLoad
                      ? initialContentVariants
                      : getContentVariants()
                  }
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  onAnimationComplete={() => setIsInitialLoad(false)}
                >
                  <Suspense fallback={<GeneratorLoadingSpinner />}>
                    {activeGenerator === "fade" && <FadeGenerator />}
                    {activeGenerator === "slide" && <SlideGenerator />}
                    {activeGenerator === "bounce" && <BounceGenerator />}
                    {activeGenerator === "zoom" && <ZoomGenerator />}
                    {activeGenerator === "pulse" && <PulseGenerator />}
                    {activeGenerator === "shake" && <ShakeGenerator />}
                    {activeGenerator === "tilt" && <TiltGenerator />}
                    {activeGenerator === "rotate" && <RotateGenerator />}
                    {activeGenerator === "morph" && <MorphGenerator />}
                    {activeGenerator === "glitch" && <GlitchGenerator />}
                    {activeGenerator === "neon" && <NeonGenerator />}
                    {activeGenerator === "magnetic" && <MagneticGenerator />}
                    {activeGenerator === "transform3d" && (
                      <Transform3DGenerator />
                    )}
                    {activeGenerator === "pathdraw" && <PathDrawGenerator />}
                    {activeGenerator === "confetti" && <ConfettiGenerator />}
                    {activeGenerator === "physics" && <PhysicsGenerator />}
                    {activeGenerator === "ripple" && <RippleGenerator />}
                    {activeGenerator === "typewriter" && (
                      <TypewriterGenerator />
                    )}
                    {activeGenerator === "fill" && <FillGenerator />}
                  </Suspense>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default App;
