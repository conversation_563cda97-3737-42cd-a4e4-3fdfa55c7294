import{r as q,j as e}from"./vendor-react-Ch3oStpB.js";import{g as C}from"./vendor-animation-BKCFbksN.js";const ke=n=>{const{objectIds:o,maxTilt:d,speed:c,glare:t,scale:a}=n;return`
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function(id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Apply essential CSS properties for proper tilt functionality
      el.style.transformStyle = "preserve-3d";
      el.style.willChange = "transform";
      el.style.transformOrigin = "center center";
      el.style.display = "inline-block";
      el.style.position = "relative";
      el.style.zIndex = 2;

      VanillaTilt.init(el, {
        max: ${d},
        speed: ${c},
        glare: ${t},
        "max-glare": ${t?"0.2":"0"},
        scale: ${a}
      });
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim()},Se=n=>{const{objectIds:o,axis:d,degrees:c,duration:t,easing:a}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      let rotation = 0;

      el.style.transformStyle = "preserve-3d";
      el.style.transition = "transform ${t}s";

      el.addEventListener("click", function () {
        rotation += ${c};
        gsap.to(el, {
          duration: ${t},
          ["rotation" + "${d}"]: rotation,
          transformOrigin: "center center",
          ease: "${a}"
        });
      });
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Ne=n=>{const{objectIds:o,trigger:d,effect:c,duration:t,delay:a,easing:r,initialOpacity:y}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${c==="fadeIn"&&d==="timeline"?`el.style.opacity = "${y/100}";`:""}

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${t},
          ${c==="fadeIn"?"opacity: 1":c==="fadeOut"?"opacity: 0":"opacity: window.getComputedStyle(el).opacity > 0.5 ? 0 : 1"},
          ease: "${r}"
        });
      }, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        ${c==="fadeIn"?`
        // Set initial opacity before animating
        el.style.opacity = "${y/100}";`:""}

        setTimeout(function() {
          ${c==="fadeToggle"?`
          const currentOpacity = window.getComputedStyle(el).opacity;
          const targetOpacity = parseFloat(currentOpacity) > 0.5 ? 0 : 1;
          gsap.to(el, {
            duration: ${t},
            opacity: targetOpacity,
            ease: "${r}"
          });`:`
          gsap.to(el, {
            duration: ${t},
            ${c==="fadeIn"?"opacity: 1":"opacity: 0"},
            ease: "${r}"
          });`}
        }, ${a*1e3});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Ee=n=>{const{objectIds:o,trigger:d,direction:c,distance:t,duration:a,delay:r,easing:y}=n,l=(M,m)=>{switch(M){case"left":return{x:-m,y:0};case"right":return{x:m,y:0};case"up":return{x:0,y:-m};case"down":return{x:0,y:m};default:return{x:0,y:0}}},{x:s,y:i}=l(c,t);return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${d==="timeline"?`
      // Set initial position for timeline trigger using percentage units
      el.style.transform = "translate(${s}%, ${i}%)";
      el.style.opacity = "0";`:""}

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${a},
          x: 0,
          y: 0,
          opacity: 1,
          ease: "${y}"
        });
      }, ${r*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        // Set initial position before animating using percentage units
        el.style.transform = "translate(${s}%, ${i}%)";
        el.style.opacity = "0";

        setTimeout(function() {
          gsap.to(el, {
            duration: ${a},
            x: 0,
            y: 0,
            opacity: 1,
            ease: "${y}"
          });
        }, ${r*1e3});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Me=n=>{const{objectIds:o,trigger:d,scale:c,duration:t,iterations:a,transformOrigin:r}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${d==="continuous"?`
      // Continuous pulse effect - start immediately
      gsap.to(el, {
        duration: ${t},
        scale: ${c},
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        transformOrigin: "${r} ${r}"
      });`:d==="timeline"?`

      // Timeline trigger - execute immediately
      gsap.to(el, {
        duration: ${t/2},
        scale: ${c},
        repeat: ${a*2-1},
        yoyo: true,
        ease: "power2.inOut",
        transformOrigin: "${r} ${r}"
      });`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        gsap.to(el, {
          duration: ${t/2},
          scale: ${c},
          repeat: ${a*2-1},
          yoyo: true,
          ease: "power2.inOut",
          transformOrigin: "${r} ${r}"
        });
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Oe=n=>{const{objectIds:o,trigger:d,intensity:c,duration:t,direction:a,frequency:r}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${d==="timeline"?`
      // Timeline trigger - execute immediately
      const timeline = gsap.timeline();
      const shakeCount = Math.ceil(${t} * ${r}); // ${r} shakes per second

      for (let i = 0; i < shakeCount; i++) {
        const xOffset = ${a==="vertical"?"0":`(Math.random() - 0.5) * ${c*2}`};
        const yOffset = ${a==="horizontal"?"0":`(Math.random() - 0.5) * ${c*2}`};

        timeline.to(el, {
          duration: ${t/r},
          x: ${a==="vertical"?"0":'xOffset + "%"'},
          y: ${a==="horizontal"?"0":'yOffset + "%"'},
          ease: "power2.inOut"
        });
      }

      // Return to original position
      timeline.to(el, {
        duration: ${t/r},
        x: 0,
        y: 0,
        ease: "power2.out"
      });`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        const timeline = gsap.timeline();
        const shakeCount = Math.ceil(${t} * ${r}); // ${r} shakes per second

        for (let i = 0; i < shakeCount; i++) {
          const xOffset = ${a==="vertical"?"0":`(Math.random() - 0.5) * ${c*2}`};
          const yOffset = ${a==="horizontal"?"0":`(Math.random() - 0.5) * ${c*2}`};

          timeline.to(el, {
            duration: ${t/r},
            x: ${a==="vertical"?"0":'xOffset + "%"'},
            y: ${a==="horizontal"?"0":'yOffset + "%"'},
            ease: "power2.inOut"
          });
        }

        // Return to original position
        timeline.to(el, {
          duration: ${t/r},
          x: 0,
          y: 0,
          ease: "power2.out"
        });
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Pe=n=>{const{objectIds:o,trigger:d,height:c,bounces:t,duration:a,delay:r}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        const timeline = gsap.timeline();

        for (let i = 0; i < ${t}; i++) {
          const bounceHeight = ${c} * (1 - i / ${t}); // Decreasing height in percentage
          timeline.to(el, {
            duration: ${a/(t*2)},
            y: "-" + bounceHeight + "%",
            ease: "power2.out"
          }).to(el, {
            duration: ${a/(t*2)},
            y: 0,
            ease: "power2.in"
          });
        }
      }, ${r*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        setTimeout(function() {
          const timeline = gsap.timeline();

          for (let i = 0; i < ${t}; i++) {
            const bounceHeight = ${c} * (1 - i / ${t}); // Decreasing height in percentage
            timeline.to(el, {
              duration: ${a/(t*2)},
              y: "-" + bounceHeight + "%",
              ease: "power2.out"
            }).to(el, {
              duration: ${a/(t*2)},
              y: 0,
              ease: "power2.in"
            });
          }
        }, ${r*1e3});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Te=n=>{const{objectIds:o,trigger:d,effect:c,scale:t,duration:a,delay:r,easing:y,transformOrigin:l}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${c==="zoomIn"&&d==="timeline"?'el.style.transform = "scale(0)";':""}

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${a},
          ${c==="zoomIn"?`scale: ${t}`:c==="zoomOut"?"scale: 0":`scale: el.style.transform.includes('scale') ? 1 : ${t}`},
          ease: "${y}",
          transformOrigin: "${l} ${l}"
        });
      }, ${r*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        ${c==="zoomIn"?`
        // Set initial scale before animating
        el.style.transform = "scale(0)";`:""}

        setTimeout(function() {
          ${c==="zoomToggle"?`
          const currentScale = el.style.transform.match(/scale\\(([^)]+)\\)/);
          const isScaled = currentScale && parseFloat(currentScale[1]) > 1;
          const targetScale = isScaled ? 1 : ${t};
          gsap.to(el, {
            duration: ${a},
            scale: targetScale,
            ease: "${y}",
            transformOrigin: "${l} ${l}"
          });`:`
          gsap.to(el, {
            duration: ${a},
            ${c==="zoomIn"?`scale: ${t}`:"scale: 0"},
            ease: "${y}",
            transformOrigin: "${l} ${l}"
          });`}
        }, ${r*1e3});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Ie=n=>{const{objectIds:o,trigger:d,effect:c,duration:t,delay:a,easing:r,skewX:y,skewY:l,perspective:s,rotateX:i,rotateY:M,scaleX:m,scaleY:P,translateX:z,translateY:F,shapeType:G,intensity:X}=n,Y=()=>{switch(c){case"skew":return`skew(${y}deg, ${l}deg)`;case"perspective":return`perspective(${s*10}px) rotateX(${i}deg) rotateY(${M}deg)`;case"matrix":{const V=Math.sin(0);return`matrix(${m}, ${V}, ${-V}, ${P}, ${z}, ${F})`}case"shape":switch(G){case"diamond":return`rotate(45deg) scale(${1+X*.1})`;case"parallelogram":return`skew(${X*2}deg, 0deg) scale(${1+X*.05})`;case"trapezoid":return`perspective(${(50+X*10)*10}px) rotateX(${X*3}deg)`;default:return`skew(${X}deg, ${X*.5}deg) scale(${1+X*.1})`}default:return`skew(${y}deg, ${l}deg)`}};return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original transform for reset
      const originalTransform = el.style.transform || 'none';

      // Set transform origin for better morphing
      el.style.transformOrigin = "center center";
      el.style.transformStyle = "preserve-3d";

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        gsap.to(el, {
          duration: ${t},
          transform: "${Y()}",
          ease: "${r}",
          onComplete: function() {
            // Optional: Reset after animation completes
            // gsap.to(el, { duration: 0.5, transform: originalTransform, ease: "power2.out" });
          }
        });
      }, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        setTimeout(function() {
          gsap.to(el, {
            duration: ${t},
            transform: "${Y()}",
            ease: "${r}"
          });
        }, ${a*1e3});
      });

      ${d==="hover"?`
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${t*.7},
          transform: originalTransform,
          ease: "${r}"
        });
      });`:""}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Le=n=>{const{objectIds:o,trigger:d,effect:c,duration:t,delay:a,intensity:r,speed:y,iterations:l}=n,s=()=>{switch(c){case"digital":return`
        // Digital glitch effect
        const timeline = gsap.timeline({ repeat: ${l-1} });

        // Create multiple rapid transforms
        for (let i = 0; i < ${Math.ceil(y)}; i++) {
          timeline.to(el, {
            duration: ${t/y/4},
            x: (Math.random() - 0.5) * ${r} + "%",
            y: (Math.random() - 0.5) * ${r*.5} + "%",
            scaleX: 1 + (Math.random() - 0.5) * ${r*.01},
            scaleY: 1 + (Math.random() - 0.5) * ${r*.01},
            ease: "none"
          }).to(el, {
            duration: ${t/y/4},
            x: 0,
            y: 0,
            scaleX: 1,
            scaleY: 1,
            ease: "power2.out"
          });
        }`;case"rgb":return`
        // RGB split glitch effect
        const timeline = gsap.timeline({ repeat: ${l-1} });

        // Create pseudo-elements for RGB channels
        const beforeEl = window.getComputedStyle(el, '::before');
        const afterEl = window.getComputedStyle(el, '::after');

        // Add CSS for RGB split effect
        const style = document.createElement('style');
        style.textContent = \`
          [data-acc-text="\${id}"]::before,
          [data-acc-text="\${id}"]::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            mix-blend-mode: screen;
          }
          [data-acc-text="\${id}"]::before {
            color: #ff0000;
            animation: glitch-before ${t}s infinite;
          }
          [data-acc-text="\${id}"]::after {
            color: #00ffff;
            animation: glitch-after ${t}s infinite;
          }
          @keyframes glitch-before {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-${r}%, ${r}%); }
            40% { transform: translate(-${r}%, -${r}%); }
            60% { transform: translate(${r}%, ${r}%); }
            80% { transform: translate(${r}%, -${r}%); }
          }
          @keyframes glitch-after {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(${r}%, ${r}%); }
            40% { transform: translate(${r}%, -${r}%); }
            60% { transform: translate(-${r}%, ${r}%); }
            80% { transform: translate(-${r}%, -${r}%); }
          }
        \`;
        document.head.appendChild(style);

        // Set data attribute for content
        el.setAttribute('data-text', el.textContent || el.innerText);
        el.style.position = 'relative';

        // Remove animation after duration
        setTimeout(() => {
          document.head.removeChild(style);
        }, ${t*l*1e3});`;case"shake":return`
        // Intense shake glitch
        const timeline = gsap.timeline({ repeat: ${l-1} });

        for (let i = 0; i < ${y*2}; i++) {
          timeline.to(el, {
            duration: ${t/(y*4)},
            x: (Math.random() - 0.5) * ${r*2} + "%",
            y: (Math.random() - 0.5) * ${r*2} + "%",
            rotation: (Math.random() - 0.5) * ${r*.5},
            ease: "none"
          });
        }

        timeline.to(el, {
          duration: ${t/y},
          x: 0,
          y: 0,
          rotation: 0,
          ease: "power2.out"
        });`;case"corrupt":return`
        // Data corruption effect
        const timeline = gsap.timeline({ repeat: ${l-1} });

        timeline.to(el, {
          duration: ${t/3},
          scaleX: 1 + ${r*.1},
          scaleY: 1 - ${r*.05},
          skewX: ${r*2},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${t/3},
          scaleX: 1 - ${r*.05},
          scaleY: 1 + ${r*.1},
          skewX: -${r},
          skewY: ${r},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${t/3},
          scaleX: 1,
          scaleY: 1,
          skewX: 0,
          skewY: 0,
          ease: "power2.out"
        });`;default:return`
        // Default digital glitch
        const timeline = gsap.timeline({ repeat: ${l-1} });
        timeline.to(el, {
          duration: ${t},
          x: (Math.random() - 0.5) * ${r},
          y: (Math.random() - 0.5) * ${r},
          ease: "power2.inOut"
        }).to(el, {
          duration: ${t/2},
          x: 0,
          y: 0,
          ease: "power2.out"
        });`}};return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${s()}
      }, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        setTimeout(function() {
          ${s()}
        }, ${a*1e3});
      });`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},De=n=>{const{objectIds:o,trigger:d,effect:c,duration:t,delay:a,color:r,intensity:y,blurRadius:l,spreadRadius:s}=n,i=()=>{var P;const M=`0 0 ${l*5}px ${r}, 0 0 ${l*10}px ${r}, 0 0 ${l*15}px ${r}`,m=`0 0 ${l*y*5}px ${r}, 0 0 ${l*y*10}px ${r}, 0 0 ${l*y*15}px ${r}, 0 0 ${s*5}px ${r}`;switch(c){case"glow":return`
        // Static neon glow effect
        el.style.textShadow = "${m}";
        el.style.color = "${r}";
        el.style.transition = "text-shadow ${t}s ease";`;case"pulse":return`
        // Pulsing neon effect
        const timeline = gsap.timeline({ repeat: -1, yoyo: true });

        timeline.to(el, {
          duration: ${t/2},
          textShadow: "${m}",
          color: "${r}",
          ease: "power2.inOut"
        }).to(el, {
          duration: ${t/2},
          textShadow: "${M}",
          ease: "power2.inOut"
        });`;case"flicker":return`
        // Flickering neon effect
        const flickerTimeline = gsap.timeline({ repeat: -1 });

        // Random flicker pattern
        const flickerPattern = [1, 0.8, 1, 0.3, 1, 0.9, 0.1, 1, 0.7, 1];

        flickerPattern.forEach((opacity, index) => {
          flickerTimeline.to(el, {
            duration: ${t/10},
            textShadow: opacity > 0.5 ? "${m}" : "${M}",
            color: opacity > 0.5 ? "${r}" : "rgba(${((P=r.replace("#","").match(/.{2}/g))==null?void 0:P.map(z=>parseInt(z,16)).join(", "))||"255, 255, 255"}, 0.5)",
            ease: "none"
          });
        });`;case"rainbow":return`
        // Rainbow neon effect
        const colors = ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];
        const timeline = gsap.timeline({ repeat: -1 });

        colors.forEach((rainbowColor, index) => {
          const rainbowGlow = \`0 0 ${l*5}px \${rainbowColor}, 0 0 ${l*10}px \${rainbowColor}, 0 0 ${l*15}px \${rainbowColor}\`;
          timeline.to(el, {
            duration: ${t/7}, // 7 colors in rainbow
            textShadow: rainbowGlow,
            color: rainbowColor,
            ease: "power2.inOut"
          });
        });`;default:return`
        // Default glow effect
        el.style.textShadow = "${m}";
        el.style.color = "${r}";`}};return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original styles
      const originalTextShadow = el.style.textShadow || 'none';
      const originalColor = el.style.color || window.getComputedStyle(el).color;

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${i()}
      }, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        setTimeout(function() {
          ${i()}
        }, ${a*1e3});
      });

      ${d==="hover"?`
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${t*.3},
          textShadow: originalTextShadow,
          color: originalColor,
          ease: "power2.out"
        });
      });`:""}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Re=n=>{const{objectIds:o,strength:d,distance:c,speed:t,returnSpeed:a,easing:r}=n;return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Store original position
      const originalRect = el.getBoundingClientRect();
      const originalX = originalRect.left + originalRect.width / 2;
      const originalY = originalRect.top + originalRect.height / 2;

      // Set up element for smooth transforms
      el.style.willChange = "transform";
      el.style.transformOrigin = "center center";

      let isHovering = false;
      let animationFrame;

      // Create magnetic area (larger than the element) - distance is percentage of viewport
      const magneticArea = document.createElement('div');
      magneticArea.style.position = 'absolute';
      const distancePixels = ${c} * Math.min(window.innerWidth, window.innerHeight) / 100;
      magneticArea.style.left = (originalRect.left - distancePixels) + 'px';
      magneticArea.style.top = (originalRect.top - distancePixels) + 'px';
      magneticArea.style.width = (originalRect.width + distancePixels * 2) + 'px';
      magneticArea.style.height = (originalRect.height + distancePixels * 2) + 'px';
      magneticArea.style.pointerEvents = 'none';
      magneticArea.style.zIndex = '9999';
      document.body.appendChild(magneticArea);

      function updatePosition(mouseX, mouseY) {
        if (!isHovering) return;

        const rect = el.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // Calculate distance from mouse to element center
        const deltaX = mouseX - centerX;
        const deltaY = mouseY - centerY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Only apply magnetic effect if within range (distance is percentage of viewport)
        const maxDistance = ${c} * Math.min(window.innerWidth, window.innerHeight) / 100;
        if (distance < maxDistance) {
          // Calculate magnetic force (stronger when closer)
          const force = Math.max(0, 1 - distance / maxDistance);
          const moveX = deltaX * force * ${d};
          const moveY = deltaY * force * ${d};

          gsap.to(el, {
            duration: ${t},
            x: moveX,
            y: moveY,
            ease: "${r}",
            overwrite: true
          });
        }
      }

      function handleMouseMove(e) {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }

        animationFrame = requestAnimationFrame(() => {
          updatePosition(e.clientX, e.clientY);
        });
      }

      function handleMouseEnter() {
        isHovering = true;
        document.addEventListener('mousemove', handleMouseMove);
      }

      function handleMouseLeave() {
        isHovering = false;
        document.removeEventListener('mousemove', handleMouseMove);

        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }

        // Return to original position
        gsap.to(el, {
          duration: ${a},
          x: 0,
          y: 0,
          ease: "${r}"
        });
      }

      // Add event listeners to the magnetic area
      magneticArea.addEventListener('mouseenter', handleMouseEnter);
      magneticArea.addEventListener('mouseleave', handleMouseLeave);

      // Also add listeners to the element itself for better UX
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);

    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},me=n=>{const{objectIds:o,trigger:d,effect:c,duration:t,delay:a,easing:r,rotateX:y,rotateY:l,rotateZ:s,translateZ:i,perspective:M,transformOrigin:m,flipDirection:P,rotationSpeed:z,continuous:F}=n,G=()=>{switch(c){case"cardFlip":return P==="horizontal"?`
        // Card flip horizontal
        gsap.to(el, {
          duration: ${t},
          rotateY: 180,
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`:`
        // Card flip vertical
        gsap.to(el, {
          duration: ${t},
          rotateX: 180,
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`;case"rotate3D":return F?`
        // Continuous 3D rotation
        gsap.to(el, {
          duration: ${z},
          rotateX: ${y},
          rotateY: ${l},
          rotateZ: ${s},
          repeat: -1,
          ease: "none",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`:`
        // Single 3D rotation
        gsap.to(el, {
          duration: ${t},
          rotateX: ${y},
          rotateY: ${l},
          rotateZ: ${s},
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`;case"perspective":return`
        // 3D perspective tilt
        gsap.to(el, {
          duration: ${t},
          rotateX: ${y},
          rotateY: ${l},
          transformPerspective: ${M*10},
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`;case"depth":return`
        // 3D depth movement
        gsap.to(el, {
          duration: ${t},
          translateZ: ${i*10},
          rotateX: ${y*.3},
          rotateY: ${l*.3},
          transformPerspective: ${M*10},
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`;case"cube":return`
        // 3D cube rotation
        const timeline = gsap.timeline();
        timeline
          .to(el, {
            duration: ${t/3},
            rotateY: 90,
            ease: "${r}",
            transformOrigin: "${m} ${m}",
            transformStyle: "preserve-3d"
          })
          .to(el, {
            duration: ${t/3},
            rotateX: 90,
            ease: "${r}"
          })
          .to(el, {
            duration: ${t/3},
            rotateZ: 90,
            ease: "${r}"
          });`;default:return`
        // Default 3D transform
        gsap.to(el, {
          duration: ${t},
          rotateX: ${y},
          rotateY: ${l},
          rotateZ: ${s},
          ease: "${r}",
          transformOrigin: "${m} ${m}",
          transformStyle: "preserve-3d"
        });`}};return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Set up 3D transform properties
      el.style.transformStyle = "preserve-3d";
      el.style.transformPerspective = "${M*10}px";
      el.style.willChange = "transform";
      el.style.backfaceVisibility = "hidden";

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(function() {
        ${G()}
      }, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
        setTimeout(function() {
          ${G()}
        }, ${a*1e3});
      });

      ${d==="hover"?`
      el.addEventListener("mouseleave", function () {
        gsap.to(el, {
          duration: ${t*.7},
          rotateX: 0,
          rotateY: 0,
          rotateZ: 0,
          translateZ: 0,
          ease: "${r}"
        });
      });`:""}`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},fe=n=>{const{library:o}=n;return o==="anime"?ge(n):he(n)},ge=n=>{const{objectIds:o,trigger:d,direction:c,duration:t,delay:a,easing:r,loop:y,stagger:l}=n;return`
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/animejs@3.2.2/lib/anime.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function(id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Find existing SVG paths in the element
      const paths = el.querySelectorAll('path');

      if (paths.length === 0) {
        console.error("No SVG paths found in element with ID: " + id);
        return;
      }

      // Set initial state for all paths
      paths.forEach(function(path) {
        const pathLength = path.getTotalLength();
        path.style.strokeDasharray = pathLength;
        path.style.strokeDashoffset = ${c==="forward"?"pathLength":"0"};
      });

      function animatePath() {
        paths.forEach(function(path, index) {
          const pathLength = path.getTotalLength();
          anime({
            targets: path,
            strokeDashoffset: ${c==="forward"?"0":"pathLength"},
            duration: ${t*1e3},
            delay: ${a*1e3} + (index * ${(l||0)*1e3}),
            easing: '${r==="ease"?"easeInOutQuad":r==="ease-in"?"easeInQuad":r==="ease-out"?"easeOutQuad":r==="ease-in-out"?"easeInOutQuad":"linear"}',
            ${y?"loop: true,":""}
            complete: function() {
              ${y?"":"// Animation complete"}
            }
          });
        });
      }

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(animatePath, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", animatePath);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim()},he=n=>{const{objectIds:o,trigger:d,direction:c,duration:t,delay:a,easing:r,loop:y,stagger:l}=n;return`
// Load GSAP DrawSVG plugin (now free as of 2025!)
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/DrawSVGPlugin.min.js";
script.onload = function () {
  // Register the DrawSVG plugin
  gsap.registerPlugin(DrawSVGPlugin);

  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Replace element content with the uploaded SVG
      el.innerHTML = '';

      // Find existing SVG paths in the element
      const paths = el.querySelectorAll('path');

      if (paths.length === 0) {
        console.error("No SVG paths found in element with ID: " + id);
        return;
      }

      // Animate all paths found in the element
      paths.forEach(function(path) {
        // Set initial state using DrawSVG plugin
        gsap.set(path, {
          drawSVG: ${c==="forward"?'"0%"':'"100%"'}
        });
      });

      function animatePath() {
        paths.forEach(function(path, index) {
          gsap.to(path, {
            drawSVG: ${c==="forward"?'"100%"':'"0%"'},
            duration: ${t},
            delay: ${a} + (index * ${l||0}),
            ease: "${r}",
            ${y?"repeat: -1,":""}
            onComplete: function() {
              ${y?"":"// Animation complete"}
            }
          });
        });
      }

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      setTimeout(animatePath, ${a*1e3});`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", animatePath);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim()},xe=n=>{const{objectIds:o,trigger:d,particleCount:c,colors:t,shapes:a,duration:r,delay:y,origin:l,spread:s,angle:i,gravity:M,startVelocity:m,scalar:P}=n,z={particleCount:c,colors:t.length>0?t:void 0,shapes:a.length>0?a:void 0,spread:s,angle:i,gravity:M,startVelocity:m,scalar:P,ticks:Math.max(50,r*60)},F=Object.fromEntries(Object.entries(z).filter(([Y,V])=>V!==void 0)),G=a.filter(Y=>!["square","circle","star"].includes(Y)),X=a.filter(Y=>["square","circle","star"].includes(Y));return`
// Load confetti.js library
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js";
script.onload = function () {
  const ids = ${JSON.stringify(o)};

  // Create custom shapes
  const customShapes = {};
  ${G.map(Y=>`customShapes.${Y} = confetti.shapeFromPath({ path: '${{triangle:"M0 10 L5 0 L10 10z",heart:"M5 0 C2.5 -2.5 0 0 0 2.5 C0 5 2.5 7.5 5 10 C7.5 7.5 10 5 10 2.5 C10 0 7.5 -2.5 5 0z",plus:"M4 0 L6 0 L6 4 L10 4 L10 6 L6 6 L6 10 L4 10 L4 6 L0 6 L0 4 L4 4z",ribbon:"M0 3 Q3 0 6 3 Q9 6 12 3 Q15 0 18 3 L18 7 Q15 10 12 7 Q9 4 6 7 Q3 10 0 7 Z",squiggle:"M0 8 Q2 2 4 8 Q6 14 8 8 Q10 2 12 8 Q14 14 16 8 Q18 2 20 8",streamer:"M0 2 Q5 0 10 2 Q15 4 20 2 Q25 0 30 2 L30 4 Q25 6 20 4 Q15 2 10 4 Q5 6 0 4 Z"}[Y]}' });`).join(`
  `)}

  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      function launchConfetti() {
        setTimeout(function() {
          // Get element position for confetti origin
          const rect = el.getBoundingClientRect();
          const originX = (rect.left + rect.width * ${l.x}) / window.innerWidth;
          const originY = (rect.top + rect.height * ${l.y}) / window.innerHeight;

          // Combine built-in and custom shapes
          const allShapes = [${X.map(Y=>`'${Y}'`).join(", ")}${X.length>0&&G.length>0?", ":""}${G.map(Y=>`customShapes.${Y}`).join(", ")}];

          const confettiOptions = ${JSON.stringify({...F,shapes:void 0},null,12)};
          confettiOptions.origin = { x: originX, y: originY };
          ${a.length>0?"confettiOptions.shapes = allShapes;":""}

          confetti(confettiOptions);
        }, ${y*1e3});
      }

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      launchConfetti();`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", launchConfetti);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim()},ye=n=>{const{objectIds:o,trigger:d,effect:c,objectCount:t,objectShape:a,objectSize:r,gravity:y,restitution:l,friction:s,duration:i,delay:M,colors:m,initialVelocity:P}=n,z=()=>{switch(c){case"drop":return`
        // Drop objects with gravity
        for (let i = 0; i < ${t}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top - 50 - Math.random() * 100;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${a==="circle"?"circle":a==="rectangle"?"rectangle":"polygon"}(
            x, y,
            ${a==="circle"?`${r/2}`:a==="rectangle"?`${r}, ${r}`:`${r/2}, 6`},
            {
              restitution: ${l},
              friction: ${s},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);
        }`;case"bounce":return`
        // Bouncing objects
        for (let i = 0; i < ${t}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top + rect.height / 2;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${a==="circle"?"circle":a==="rectangle"?"rectangle":"polygon"}(
            x, y,
            ${a==="circle"?`${r/2}`:a==="rectangle"?`${r}, ${r}`:`${r/2}, 6`},
            {
              restitution: ${l},
              friction: ${s},
              render: {
                fillStyle: color
              }
            }
          );

          // Add initial velocity for bouncing
          Body.setVelocity(body, {
            x: (Math.random() - 0.5) * ${P.x},
            y: ${P.y}
          });

          World.add(world, body);
        }`;case"explode":return`
        // Exploding objects from center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${t}; i++) {
          const angle = (i / ${t}) * Math.PI * 2;
          const distance = 20 + Math.random() * 30;
          const x = centerX + Math.cos(angle) * distance;
          const y = centerY + Math.sin(angle) * distance;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${a==="circle"?"circle":a==="rectangle"?"rectangle":"polygon"}(
            x, y,
            ${a==="circle"?`${r/2}`:a==="rectangle"?`${r}, ${r}`:`${r/2}, 6`},
            {
              restitution: ${l},
              friction: ${s},
              render: {
                fillStyle: color
              }
            }
          );

          // Explosive velocity
          const force = 0.1 + Math.random() * 0.2;
          Body.setVelocity(body, {
            x: Math.cos(angle) * force * ${P.x},
            y: Math.sin(angle) * force * ${P.y}
          });

          World.add(world, body);
        }`;case"attract":return`
        // Objects attracted to center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${t}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top + Math.random() * rect.height;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${a==="circle"?"circle":a==="rectangle"?"rectangle":"polygon"}(
            x, y,
            ${a==="circle"?`${r/2}`:a==="rectangle"?`${r}, ${r}`:`${r/2}, 6`},
            {
              restitution: ${l},
              friction: ${s},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);

          // Apply attraction force towards center
          setInterval(() => {
            const dx = centerX - body.position.x;
            const dy = centerY - body.position.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance > 10) {
              const force = 0.001;
              Body.applyForce(body, body.position, {
                x: (dx / distance) * force,
                y: (dy / distance) * force
              });
            }
          }, 16);
        }`;case"repel":return`
        // Objects repelled from center
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < ${t}; i++) {
          const x = centerX + (Math.random() - 0.5) * 100;
          const y = centerY + (Math.random() - 0.5) * 100;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.${a==="circle"?"circle":a==="rectangle"?"rectangle":"polygon"}(
            x, y,
            ${a==="circle"?`${r/2}`:a==="rectangle"?`${r}, ${r}`:`${r/2}, 6`},
            {
              restitution: ${l},
              friction: ${s},
              render: {
                fillStyle: color
              }
            }
          );

          World.add(world, body);

          // Apply repulsion force from center
          setInterval(() => {
            const dx = body.position.x - centerX;
            const dy = body.position.y - centerY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            if (distance < 200 && distance > 0) {
              const force = 0.002;
              Body.applyForce(body, body.position, {
                x: (dx / distance) * force,
                y: (dy / distance) * force
              });
            }
          }, 16);
        }`;default:return`
        // Default drop effect
        for (let i = 0; i < ${t}; i++) {
          const x = rect.left + Math.random() * rect.width;
          const y = rect.top - 50;
          const color = colors[Math.floor(Math.random() * colors.length)];

          const body = Bodies.circle(x, y, ${r/2}, {
            restitution: ${l},
            friction: ${s},
            render: {
              fillStyle: color
            }
          });

          World.add(world, body);
        }`}};return`
// Load Matter.js library
var script = document.createElement("script");
script.src = "https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js";
script.onload = function () {
  const { Engine, Render, World, Bodies, Body, Mouse, MouseConstraint } = Matter;
  const ids = ${JSON.stringify(o)};
  const colors = ${JSON.stringify(m.length>0?m:["#ff0000","#00ff00","#0000ff","#ffff00","#ff00ff","#00ffff"])};

  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      function createPhysicsScene() {
        setTimeout(function() {
          // Get element position and size
          const rect = el.getBoundingClientRect();

          // Create physics engine
          const engine = Engine.create();
          const world = engine.world;

          // Set gravity
          engine.world.gravity.y = ${y};

          // Create renderer
          const render = Render.create({
            element: document.body,
            engine: engine,
            options: {
              width: window.innerWidth,
              height: window.innerHeight,
              wireframes: false,
              background: 'transparent',
              showAngleIndicator: false,
              showVelocity: false
            }
          });

          // Position renderer over the page
          render.canvas.style.position = 'fixed';
          render.canvas.style.top = '0';
          render.canvas.style.left = '0';
          render.canvas.style.pointerEvents = 'none';
          render.canvas.style.zIndex = '9999';

          // Create boundaries (invisible walls)
          const ground = Bodies.rectangle(window.innerWidth / 2, window.innerHeight + 50, window.innerWidth, 100, { isStatic: true, render: { visible: false } });
          const leftWall = Bodies.rectangle(-50, window.innerHeight / 2, 100, window.innerHeight, { isStatic: true, render: { visible: false } });
          const rightWall = Bodies.rectangle(window.innerWidth + 50, window.innerHeight / 2, 100, window.innerHeight, { isStatic: true, render: { visible: false } });

          World.add(world, [ground, leftWall, rightWall]);

          // Create physics objects based on effect
          ${z()}

          // Run the engine and renderer
          Engine.run(engine);
          Render.run(render);

          // Clean up after duration
          setTimeout(() => {
            Render.stop(render);
            Engine.clear(engine);
            render.canvas.remove();
          }, ${i*1e3});

        }, ${M*1e3});
      }

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      createPhysicsScene();`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", createPhysicsScene);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
};
document.head.appendChild(script);`.trim()},Xe=n=>{const{objectIds:o,trigger:d,effect:c,size:t,color:a,opacity:r,duration:y,delay:l,origin:s,customOrigin:i,allowMultiple:M,easing:m}=n,P=()=>{switch(t){case"small":return"100";case"medium":return"200";case"large":return"400";case"auto":return"Math.max(rect.width, rect.height) * 2";default:return"200"}},z=()=>{const F=P();switch(c){case"material":return`
        // Material Design ripple effect
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.backgroundColor = '${a}';
        ripple.style.opacity = '${r}';
        ripple.style.pointerEvents = 'none';
        ripple.style.transform = 'scale(0)';
        ripple.style.zIndex = '1000';

        // Calculate ripple position
        let rippleX, rippleY;
        if ('${s}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          rippleX = event.clientX - rect.left;
          rippleY = event.clientY - rect.top;
        } else if ('${s}' === 'custom') {
          const rect = el.getBoundingClientRect();
          rippleX = (${i.x} / 100) * rect.width;
          rippleY = (${i.y} / 100) * rect.height;
        } else {
          // Center origin
          const rect = el.getBoundingClientRect();
          rippleX = rect.width / 2;
          rippleY = rect.height / 2;
        }

        const rippleSize = ${F};
        ripple.style.width = rippleSize + 'px';
        ripple.style.height = rippleSize + 'px';
        ripple.style.left = (rippleX - rippleSize / 2) + 'px';
        ripple.style.top = (rippleY - rippleSize / 2) + 'px';

        el.appendChild(ripple);

        // Animate ripple with better scaling
        gsap.fromTo(ripple,
          {
            scale: 0,
            opacity: ${r}
          },
          {
            duration: ${y},
            scale: 1,
            opacity: 0,
            ease: "${m}",
            onComplete: function() {
              if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
              }
            }
          }
        );`;case"water":return`
        // Water ripple effect with multiple waves
        const createWave = (delay) => {
          const wave = document.createElement('div');
          wave.style.position = 'absolute';
          wave.style.borderRadius = '50%';
          wave.style.border = '2px solid ${a}';
          wave.style.backgroundColor = 'transparent';
          wave.style.opacity = '${r}';
          wave.style.pointerEvents = 'none';
          wave.style.transform = 'scale(0)';
          wave.style.zIndex = '1000';

          // Calculate wave position
          let waveX, waveY;
          if ('${s}' === 'click' && event && event.type === 'click') {
            const rect = el.getBoundingClientRect();
            waveX = event.clientX - rect.left;
            waveY = event.clientY - rect.top;
          } else if ('${s}' === 'custom') {
            const rect = el.getBoundingClientRect();
            waveX = (${i.x} / 100) * rect.width;
            waveY = (${i.y} / 100) * rect.height;
          } else {
            const rect = el.getBoundingClientRect();
            waveX = rect.width / 2;
            waveY = rect.height / 2;
          }

          const waveSize = ${F};
          wave.style.width = waveSize + 'px';
          wave.style.height = waveSize + 'px';
          wave.style.left = (waveX - waveSize / 2) + 'px';
          wave.style.top = (waveY - waveSize / 2) + 'px';

          el.appendChild(wave);

          gsap.to(wave, {
            duration: ${y},
            scale: 1,
            opacity: 0,
            ease: "${m}",
            delay: delay,
            onComplete: function() {
              if (wave.parentNode) {
                wave.parentNode.removeChild(wave);
              }
            }
          });
        };

        // Create multiple waves for water effect with better timing
        createWave(0);
        createWave(${y*.15});
        createWave(${y*.3});`;case"pulse":return`
        // Pulse ripple effect
        const pulse = document.createElement('div');
        pulse.style.position = 'absolute';
        pulse.style.borderRadius = '50%';
        pulse.style.backgroundColor = '${a}';
        pulse.style.opacity = '${r}';
        pulse.style.pointerEvents = 'none';
        pulse.style.transform = 'scale(1)';
        pulse.style.zIndex = '1000';

        // Calculate pulse position
        let pulseX, pulseY;
        if ('${s}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          pulseX = event.clientX - rect.left;
          pulseY = event.clientY - rect.top;
        } else if ('${s}' === 'custom') {
          const rect = el.getBoundingClientRect();
          pulseX = (${i.x} / 100) * rect.width;
          pulseY = (${i.y} / 100) * rect.height;
        } else {
          const rect = el.getBoundingClientRect();
          pulseX = rect.width / 2;
          pulseY = rect.height / 2;
        }

        const pulseSize = ${F};
        pulse.style.width = pulseSize + 'px';
        pulse.style.height = pulseSize + 'px';
        pulse.style.left = (pulseX - pulseSize / 2) + 'px';
        pulse.style.top = (pulseY - pulseSize / 2) + 'px';

        el.appendChild(pulse);

        // Pulse animation
        gsap.to(pulse, {
          duration: ${y/2},
          scale: 1.5,
          opacity: ${r*.7},
          ease: "power2.out",
          yoyo: true,
          repeat: 1,
          onComplete: function() {
            if (pulse.parentNode) {
              pulse.parentNode.removeChild(pulse);
            }
          }
        });`;case"shockwave":return`
        // Shockwave ripple effect
        const shockwave = document.createElement('div');
        shockwave.style.position = 'absolute';
        shockwave.style.borderRadius = '50%';
        shockwave.style.border = '3px solid ${a}';
        shockwave.style.backgroundColor = 'transparent';
        shockwave.style.opacity = '${r}';
        shockwave.style.pointerEvents = 'none';
        shockwave.style.transform = 'scale(0)';
        shockwave.style.zIndex = '1000';
        shockwave.style.boxShadow = '0 0 20px ${a}';

        // Calculate shockwave position
        let shockX, shockY;
        if ('${s}' === 'click' && event && event.type === 'click') {
          const rect = el.getBoundingClientRect();
          shockX = event.clientX - rect.left;
          shockY = event.clientY - rect.top;
        } else if ('${s}' === 'custom') {
          const rect = el.getBoundingClientRect();
          shockX = (${i.x} / 100) * rect.width;
          shockY = (${i.y} / 100) * rect.height;
        } else {
          const rect = el.getBoundingClientRect();
          shockX = rect.width / 2;
          shockY = rect.height / 2;
        }

        const shockSize = ${F};
        shockwave.style.width = shockSize + 'px';
        shockwave.style.height = shockSize + 'px';
        shockwave.style.left = (shockX - shockSize / 2) + 'px';
        shockwave.style.top = (shockY - shockSize / 2) + 'px';

        el.appendChild(shockwave);

        // Enhanced shockwave animation with multiple phases
        gsap.timeline()
          .to(shockwave, {
            duration: ${y*.3},
            scale: 0.3,
            opacity: ${r},
            ease: "power2.out"
          })
          .to(shockwave, {
            duration: ${y*.7},
            scale: 1,
            opacity: 0,
            ease: "power3.out",
            onComplete: function() {
              if (shockwave.parentNode) {
                shockwave.parentNode.removeChild(shockwave);
              }
            }
          });`;default:return`
        // Default material ripple
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.backgroundColor = '${a}';
        ripple.style.opacity = '${r}';
        ripple.style.pointerEvents = 'none';
        ripple.style.transform = 'scale(0)';
        ripple.style.zIndex = '1000';

        const rect = el.getBoundingClientRect();
        const rippleSize = ${F};
        ripple.style.width = rippleSize + 'px';
        ripple.style.height = rippleSize + 'px';
        ripple.style.left = (rect.width / 2 - rippleSize / 2) + 'px';
        ripple.style.top = (rect.height / 2 - rippleSize / 2) + 'px';

        el.appendChild(ripple);

        gsap.to(ripple, {
          duration: ${y},
          scale: 1,
          opacity: 0,
          ease: "${m}",
          onComplete: function() {
            if (ripple.parentNode) {
              ripple.parentNode.removeChild(ripple);
            }
          }
        });`}};return`
(function () {
  const ids = ${JSON.stringify(o)};
  ids.forEach(function (id) {
    const el = document.querySelector('[data-acc-text="' + id + '"]');
    if (el) {
      // Set up element for ripple effects
      const originalPosition = el.style.position;
      const originalOverflow = el.style.overflow;

      if (getComputedStyle(el).position === 'static') {
        el.style.position = 'relative';
      }
      el.style.overflow = 'hidden';

      function createRipple(event) {
        setTimeout(function() {
          // Check if multiple ripples are allowed
          if (!${M}) {
            // Remove existing ripples
            const existingRipples = el.querySelectorAll('[data-ripple]');
            existingRipples.forEach(ripple => {
              if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
              }
            });
          }

          ${z()}

          // Mark ripple element for cleanup
          if (typeof ripple !== 'undefined') {
            ripple.setAttribute('data-ripple', 'true');
          }
          if (typeof wave !== 'undefined') {
            wave.setAttribute('data-ripple', 'true');
          }
          if (typeof pulse !== 'undefined') {
            pulse.setAttribute('data-ripple', 'true');
          }
          if (typeof shockwave !== 'undefined') {
            shockwave.setAttribute('data-ripple', 'true');
          }
        }, ${l*1e3});
      }

      ${d==="timeline"?`
      // Timeline trigger - execute immediately with delay
      createRipple();`:`

      el.addEventListener("${d==="hover"?"mouseenter":"click"}", createRipple);`}
    } else {
      console.error("Element with data-acc-text='" + id + "' not found.");
    }
  });
})();`.trim()},Ye=n=>{const{objectIds:o,trigger:d,strings:c,typeSpeed:t,backSpeed:a,startDelay:r,backDelay:y,loop:l,loopCount:s,showCursor:i,cursorChar:M,smartBackspace:m,shuffle:P,fadeOut:z,fadeOutDelay:F}=n,G={strings:c.length>0?c:["Hello World!"],typeSpeed:t,backSpeed:a,startDelay:r,backDelay:y,loop:l,loopCount:l?s:1,showCursor:i,cursorChar:M,smartBackspace:m,shuffle:P,fadeOut:z,fadeOutDelay:F},X=Object.fromEntries(Object.entries(G).filter(([Y,V])=>V!==void 0&&V!==""));return`
// Load Typed.js library
var script = document.createElement("script");
script.src = "https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js";
script.onload = function() {
  (function () {
    const ids = ${JSON.stringify(o)};
    const options = ${JSON.stringify(X,null,2)};

    ids.forEach(function (id) {
      const el = document.querySelector('[data-acc-text="' + id + '"]');
      if (el) {
        ${d==="timeline"?`
        // Timeline trigger - execute immediately with delay
        setTimeout(function() {
          // Clear existing content
          el.innerHTML = '';
          // Initialize Typed.js
          new Typed(el, options);
        }, ${r});`:`

        el.addEventListener("${d==="hover"?"mouseenter":"click"}", function () {
          // Clear existing content
          el.innerHTML = '';
          // Initialize Typed.js
          new Typed(el, options);
        });`}
      } else {
        console.error("Element with data-acc-text='" + id + "' not found.");
      }
    });
  })();
};
document.head.appendChild(script);`.trim()},ze=n=>{const{objectIds:o,trigger:d,origin:c,direction:t,duration:a,delay:r,easing:y,stagger:l,color:s,shape:i,borderRadius:M}=n,m=(G,X,Y)=>{if(Y==="circle"){const A={"bottom-right":{start:"circle(0% at 100% 100%)",end:"circle(150% at 100% 100%)"},"bottom-left":{start:"circle(0% at 0% 100%)",end:"circle(150% at 0% 100%)"},"top-right":{start:"circle(0% at 100% 0%)",end:"circle(150% at 100% 0%)"},"top-left":{start:"circle(0% at 0% 0%)",end:"circle(150% at 0% 0%)"},center:{start:"circle(0% at 50% 50%)",end:"circle(150% at 50% 50%)"},left:{start:"circle(0% at 0% 50%)",end:"circle(150% at 0% 50%)"},right:{start:"circle(0% at 100% 50%)",end:"circle(150% at 100% 50%)"},top:{start:"circle(0% at 50% 0%)",end:"circle(150% at 50% 0%)"},bottom:{start:"circle(0% at 50% 100%)",end:"circle(150% at 50% 100%)"}},Z=A[G]||A.center;return X?Z.start:Z.end}if(Y==="ellipse"){const A={"bottom-right":{start:"ellipse(0% 0% at 100% 100%)",end:"ellipse(150% 100% at 100% 100%)"},"bottom-left":{start:"ellipse(0% 0% at 0% 100%)",end:"ellipse(150% 100% at 0% 100%)"},"top-right":{start:"ellipse(0% 0% at 100% 0%)",end:"ellipse(150% 100% at 100% 0%)"},"top-left":{start:"ellipse(0% 0% at 0% 0%)",end:"ellipse(150% 100% at 0% 0%)"},center:{start:"ellipse(0% 0% at 50% 50%)",end:"ellipse(150% 100% at 50% 50%)"},left:{start:"ellipse(0% 0% at 0% 50%)",end:"ellipse(150% 100% at 0% 50%)"},right:{start:"ellipse(0% 0% at 100% 50%)",end:"ellipse(150% 100% at 100% 50%)"},top:{start:"ellipse(0% 0% at 50% 0%)",end:"ellipse(150% 100% at 50% 0%)"},bottom:{start:"ellipse(0% 0% at 50% 100%)",end:"ellipse(150% 100% at 50% 100%)"}},Z=A[G]||A.center;return X?Z.start:Z.end}if(Y==="rounded-rectangle"){const A=M||12,Z={"bottom-right":{start:`inset(100% 0% 0% 100% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},"bottom-left":{start:`inset(100% 100% 0% 0% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},"top-right":{start:`inset(0% 0% 100% 100% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},"top-left":{start:`inset(0% 100% 100% 0% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},center:{start:`inset(50% 50% 50% 50% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},left:{start:`inset(0% 100% 0% 0% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},right:{start:`inset(0% 0% 0% 100% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},top:{start:`inset(0% 0% 100% 0% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`},bottom:{start:`inset(100% 0% 0% 0% round ${A}px)`,end:`inset(0% 0% 0% 0% round ${A}px)`}},H=Z[G]||Z["bottom-right"];return X?H.start:H.end}const V={"bottom-right":{start:"polygon(100% 100%, 100% 100%, 100% 100%, 100% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"bottom-left":{start:"polygon(0% 100%, 0% 100%, 0% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"top-right":{start:"polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"top-left":{start:"polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},center:{start:"polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},left:{start:"polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},right:{start:"polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},top:{start:"polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},bottom:{start:"polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"}},W=V[G]||V["bottom-right"];return X?W.start:W.end},P=t==="reveal"?m(c,!0,i||"rectangle"):m(c,!1,i||"rectangle"),z=t==="reveal"?m(c,!1,i||"rectangle"):m(c,!0,i||"rectangle"),F=G=>{switch(G){case"timeline":return`
    // Auto-execute on timeline
    gsap.set(pseudoElements, { clipPath: "${P}" });
    gsap.to(pseudoElements, {
      clipPath: "${z}",
      duration: ${a},
      delay: ${r},
      ease: "${y}",
      stagger: ${l}
    });`;case"hover":return`
    elements.forEach(function(el, index) {
      const pseudoEl = pseudoElements[index];
      gsap.set(pseudoEl, { clipPath: "${P}" });
      
      el.addEventListener("mouseenter", function() {
        gsap.to(pseudoEl, {
          clipPath: "${z}",
          duration: ${a},
          ease: "${y}"
        });
      });
      
      el.addEventListener("mouseleave", function() {
        gsap.to(pseudoEl, {
          clipPath: "${P}",
          duration: ${a},
          ease: "${y}"
        });
      });
    });`;case"click":return`
    elements.forEach(function(el, index) {
      const pseudoEl = pseudoElements[index];
      gsap.set(pseudoEl, { clipPath: "${P}" });
      
      el.addEventListener("click", function() {
        gsap.to(pseudoEl, {
          clipPath: "${z}",
          duration: ${a},
          ease: "${y}"
        });
      });
    });`;default:return""}};return`
var script = document.createElement("script");
script.src = "https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js";
script.onload = function () {
  (function () {
    const ids = ${JSON.stringify(o)};
    const fillColor = "${s}";
    
    const elements = ids.map(function(id) {
      const el = document.querySelector('[data-acc-text="' + id + '"]');
      if (el) {
        // Set up element for pseudo-element
        el.style.position = el.style.position || 'relative';
        el.style.overflow = 'hidden';
        return el;
      } else {
        console.error("Element with data-acc-text='" + id + "' not found.");
        return null;
      }
    }).filter(Boolean);
    
    if (elements.length === 0) {
      console.error("No valid elements found for fill effect.");
      return;
    }
    
    // Create pseudo-elements for each element
    const pseudoElements = elements.map(function(el) {
      const pseudoEl = document.createElement('div');
      pseudoEl.style.position = 'absolute';
      pseudoEl.style.top = '0';
      pseudoEl.style.left = '0';
      pseudoEl.style.right = '0';
      pseudoEl.style.bottom = '0';
      pseudoEl.style.backgroundColor = fillColor;
      pseudoEl.style.clipPath = 'polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)';
      pseudoEl.style.zIndex = '-1';
      pseudoEl.style.pointerEvents = 'none';
      
      // Insert pseudo-element as first child
      el.insertBefore(pseudoEl, el.firstChild);
      return pseudoEl;
    });
    
    ${F(d)}
  })();
};
document.head.appendChild(script);`.trim()};function K({code:n,title:o="Copy this JavaScript:"}){const[d,c]=q.useState(!1),t=async()=>{try{await navigator.clipboard.writeText(n),c(!0),setTimeout(()=>c(!1),2e3)}catch(a){console.error("Failed to copy text: ",a)}};return e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:o}),e.jsx("button",{onClick:t,className:`px-4 py-2 text-sm font-medium rounded-md transition-colors ${d?"bg-green-100 text-green-800 border border-green-300":"bg-blue-500 text-white hover:bg-blue-700"}`,children:d?"Copied!":"Copy Code"})]}),e.jsx("div",{className:"relative",children:e.jsx("pre",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm leading-relaxed",children:e.jsx("code",{children:n})})}),e.jsx("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md",children:e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("span",{className:"font-medium",children:"💡 Note:"})," Paste this code into a JavaScript trigger in Articulate Storyline. Make sure your objects have the correct accessibility alt-text IDs."]})})]})}function ee({effectType:n,config:o,onTrigger:d}){const c=q.useRef(null),t=q.useRef(null),[a,r]=q.useState(!1),y=()=>{if(!t.current)return;const h=t.current;h.vanillaTilt&&h.vanillaTilt.destroy(),C.set(t.current,{x:0,y:0,scale:1,rotation:0,rotationX:0,rotationY:0,rotationZ:0,opacity:1,transformOrigin:"center center",boxShadow:"none"}),t.current.style.transformStyle="",t.current.style.willChange=""},l=()=>{if(t.current){switch(y(),n){case"fade":s();break;case"slide":i();break;case"bounce":M();break;case"zoom":m();break;case"pulse":P();break;case"shake":z();break;case"rotate":F();break;case"tilt":G();break;case"morph":X();break;case"glitch":Y();break;case"neon":V();break;case"magnetic":W();break;case"transform3d":A();break;case"pathdraw":Z();break;case"confetti":H();break;case"physics":re();break;case"ripple":ne();break;case"typewriter":oe();break;case"fill":J();break}d&&d()}},s=()=>{if(!t.current)return;const{effect:h,duration:u,easing:f,initialOpacity:x,delay:b}=o;if(h==="fadeIn")C.set(t.current,{opacity:(x||0)/100}),C.to(t.current,{duration:u||1,opacity:1,ease:f||"ease",delay:b||0});else if(h==="fadeOut")C.to(t.current,{duration:u||1,opacity:0,ease:f||"ease",delay:b||0});else if(h==="fadeToggle"){const v=C.getProperty(t.current,"opacity");C.to(t.current,{duration:u||1,opacity:v>.5?0:1,ease:f||"ease",delay:b||0})}},i=()=>{if(!t.current)return;const{direction:h,distance:u,duration:f,easing:x,delay:b}=o,$=(()=>{const R=(u||10)*10;switch(h){case"left":return{x:-R,y:0};case"right":return{x:R,y:0};case"up":return{x:0,y:-R};case"down":return{x:0,y:R};default:return{x:0,y:0}}})();C.set(t.current,{...$,opacity:0}),C.to(t.current,{duration:f||1,x:0,y:0,opacity:1,ease:x||"ease",delay:b||0})},M=()=>{if(!t.current)return;const{height:h,bounces:u,duration:f}=o,x=C.timeline();for(let b=0;b<(u||3);b++){const v=(h||5)*10*(1-b/(u||3));x.to(t.current,{duration:(f||1)/((u||3)*2),y:-v,ease:"power2.out"}).to(t.current,{duration:(f||1)/((u||3)*2),y:0,ease:"power2.in"})}},m=()=>{if(!t.current)return;const{effect:h,scale:u,duration:f,easing:x,transformOrigin:b,delay:v}=o;if(C.set(t.current,{transformOrigin:`${b||"center"} ${b||"center"}`}),h==="zoomIn")C.set(t.current,{scale:0}),C.to(t.current,{duration:f||1,scale:u||1.5,ease:x||"ease",delay:v||0});else if(h==="zoomOut")C.to(t.current,{duration:f||1,scale:0,ease:x||"ease",delay:v||0});else if(h==="zoomToggle"){const $=C.getProperty(t.current,"scaleX");C.to(t.current,{duration:f||1,scale:$>1?1:u||1.5,ease:x||"ease",delay:v||0})}},P=()=>{if(!t.current)return;const{scale:h,duration:u,iterations:f,transformOrigin:x,trigger:b}=o;C.set(t.current,{transformOrigin:`${x||"center"} ${x||"center"}`}),b==="continuous"?C.to(t.current,{duration:u||1,scale:h||1.2,repeat:-1,yoyo:!0,ease:"power2.inOut"}):C.to(t.current,{duration:(u||1)/2,scale:h||1.2,repeat:(f||2)*2-1,yoyo:!0,ease:"power2.inOut"})},z=()=>{if(!t.current)return;const{intensity:h,duration:u,direction:f,frequency:x}=o,b=C.timeline(),v=Math.ceil((u||1)*(x||10));for(let $=0;$<v;$++){const R=(h||1)*10,I=f==="vertical"?0:(Math.random()-.5)*R*2,E=f==="horizontal"?0:(Math.random()-.5)*R*2;b.to(t.current,{duration:(u||1)/(x||10),x:I,y:E,ease:"power2.inOut"})}b.to(t.current,{duration:(u||1)/(x||10),x:0,y:0,ease:"power2.out"})},F=()=>{if(!t.current)return;const{axis:h,degrees:u,duration:f,easing:x}=o;C.to(t.current,{duration:f||1,[`rotation${h||"Z"}`]:u||90,transformOrigin:"center center",ease:x||"ease"})},G=()=>{if(!t.current)return;const{maxTilt:h,scale:u,speed:f,glare:x}=o;if(window.VanillaTilt)b();else{const v=document.createElement("script");v.src="https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js",v.onload=()=>{b()},document.head.appendChild(v)}function b(){if(!t.current)return;const v=t.current;v.vanillaTilt&&v.vanillaTilt.destroy(),t.current.style.transformStyle="preserve-3d",t.current.style.willChange="transform",t.current.style.transformOrigin="center center",window.VanillaTilt.init(t.current,{max:h||15,speed:f||400,glare:x||!1,"max-glare":x?.2:0,scale:u||1.02}),setTimeout(()=>{if(t.current){const $=t.current.getBoundingClientRect(),R=$.left+$.width/2,I=$.top+$.height/2,E=new MouseEvent("mousemove",{clientX:R+20,clientY:I-20,bubbles:!0});t.current.dispatchEvent(E),setTimeout(()=>{if(t.current){const D=new MouseEvent("mouseleave",{bubbles:!0});t.current.dispatchEvent(D)}},1e3)}},100)}},X=()=>{if(!t.current)return;const{effect:h,skewX:u,skewY:f,perspective:x,rotateX:b,rotateY:v,scaleX:$,scaleY:R,translateX:I,translateY:E,shapeType:D,intensity:j,duration:g,easing:w}=o;let k="";switch(h){case"skew":k=`skew(${u||15}deg, ${f||5}deg)`;break;case"perspective":k=`perspective(${(x||100)*10}px) rotateX(${b||15}deg) rotateY(${v||15}deg)`;break;case"matrix":k=`matrix(${$||1.2}, 0, 0, ${R||1.2}, ${I||0}, ${E||0})`;break;case"shape":switch(D){case"diamond":k=`rotate(45deg) scale(${1+(j||5)*.1})`;break;case"parallelogram":k=`skew(${(j||5)*2}deg, 0deg) scale(${1+(j||5)*.05})`;break;case"trapezoid":k=`perspective(${500+(j||5)*100}px) rotateX(${(j||5)*3}deg)`;break;default:k=`skew(${j||5}deg, ${(j||5)*.5}deg) scale(${1+(j||5)*.1})`}break;default:k=`skew(${u||15}deg, ${f||5}deg)`}C.set(t.current,{transformOrigin:"center center",transformStyle:"preserve-3d"}),C.to(t.current,{duration:g||1,transform:k,ease:w||"ease"})},Y=()=>{if(!t.current)return;const{effect:h,intensity:u,speed:f,duration:x}=o;switch(h){case"digital":{const b=C.timeline(),v=(u||1)*10;for(let $=0;$<(f||5);$++)b.to(t.current,{duration:(x||1)/(f||5)/4,x:(Math.random()-.5)*v,y:(Math.random()-.5)*v*.5,scaleX:1+(Math.random()-.5)*v*.01,scaleY:1+(Math.random()-.5)*v*.01,ease:"none"}).to(t.current,{duration:(x||1)/(f||5)/4,x:0,y:0,scaleX:1,scaleY:1,ease:"power2.out"});break}case"shake":{const b=C.timeline(),v=(u||1)*10;for(let $=0;$<(f||5)*2;$++)b.to(t.current,{duration:(x||1)/((f||5)*4),x:(Math.random()-.5)*v*2,y:(Math.random()-.5)*v*2,rotation:(Math.random()-.5)*v*.5,ease:"none"});b.to(t.current,{duration:(x||1)/(f||5),x:0,y:0,rotation:0,ease:"power2.out"});break}case"corrupt":{const b=(u||1)*10;C.timeline().to(t.current,{duration:(x||1)/3,scaleX:1+b*.1,scaleY:1-b*.05,skewX:b*2,ease:"power2.inOut"}).to(t.current,{duration:(x||1)/3,scaleX:1-b*.05,scaleY:1+b*.1,skewX:-b,skewY:b,ease:"power2.inOut"}).to(t.current,{duration:(x||1)/3,scaleX:1,scaleY:1,skewX:0,skewY:0,ease:"power2.out"});break}default:{const b=(u||1)*10;C.timeline().to(t.current,{duration:x||1,x:(Math.random()-.5)*b,y:(Math.random()-.5)*b,ease:"power2.inOut"}).to(t.current,{duration:(x||1)/2,x:0,y:0,ease:"power2.out"})}}},V=()=>{if(!t.current)return;const{effect:h,color:u,intensity:f,blurRadius:x,spreadRadius:b,duration:v}=o,$=(x||1)*5,R=(b||2)*5,I=`0 0 ${$}px ${u||"#00ffff"}, 0 0 ${$*2}px ${u||"#00ffff"}, 0 0 ${$*3}px ${u||"#00ffff"}`,E=`0 0 ${$*(f||2)}px ${u||"#00ffff"}, 0 0 ${$*(f||2)*2}px ${u||"#00ffff"}, 0 0 ${$*(f||2)*3}px ${u||"#00ffff"}, 0 0 ${R}px ${u||"#00ffff"}`;switch(h){case"glow":C.set(t.current,{textShadow:E,color:u||"#00ffff"});break;case"pulse":C.timeline({repeat:2,yoyo:!0}).to(t.current,{duration:(v||2)/2,textShadow:E,color:u||"#00ffff",ease:"power2.inOut"}).to(t.current,{duration:(v||2)/2,textShadow:I,ease:"power2.inOut"});break;case"flicker":{const D=C.timeline();[1,.8,1,.3,1,.9,.1,1,.7,1].forEach(g=>{D.to(t.current,{duration:(v||2)/10,textShadow:g>.5?E:I,color:g>.5?u||"#00ffff":"rgba(0, 255, 255, 0.5)",ease:"none"})});break}case"rainbow":{const D=["#ff0000","#ff7f00","#ffff00","#00ff00","#0000ff","#4b0082","#9400d3"],j=C.timeline();D.forEach(g=>{const w=`0 0 ${x||10}px ${g}, 0 0 ${(x||10)*2}px ${g}, 0 0 ${(x||10)*3}px ${g}`;j.to(t.current,{duration:(v||2)/D.length,textShadow:w,color:g,ease:"power2.inOut"})});break}default:C.set(t.current,{textShadow:E,color:u||"#00ffff"})}},W=()=>{if(!t.current||!c.current)return;const{strength:h,distance:u,speed:f,returnSpeed:x,easing:b}=o;t.current.style.willChange="transform",t.current.style.transformOrigin="center center";let v=!1,$;function R(g,w){if(!v||!t.current||!c.current)return;const k=c.current.getBoundingClientRect(),S=t.current.getBoundingClientRect(),T=g-k.left,L=w-k.top,p=S.left-k.left+S.width/2,O=S.top-k.top+S.height/2,B=T-p,N=L-O,Q=Math.sqrt(B*B+N*N),_=(u||10)*10;if(Q<_){const U=Math.max(0,1-Q/_),se=B*U*(h||.3),pe=N*U*(h||.3);C.to(t.current,{duration:f||.3,x:se,y:pe,ease:b||"ease-out",overwrite:!0})}}function I(g){$&&cancelAnimationFrame($),$=requestAnimationFrame(()=>{R(g.clientX,g.clientY)})}function E(){v=!0,document.addEventListener("mousemove",I)}function D(){v=!1,document.removeEventListener("mousemove",I),$&&cancelAnimationFrame($),t.current&&C.to(t.current,{duration:x||.6,x:0,y:0,ease:b||"ease-out"})}c.current.addEventListener("mouseenter",E),c.current.addEventListener("mouseleave",D);const j=()=>{c.current&&(c.current.removeEventListener("mouseenter",E),c.current.removeEventListener("mouseleave",D)),document.removeEventListener("mousemove",I),$&&cancelAnimationFrame($)};c.current._magneticCleanup=j},A=()=>{if(!t.current)return;const{effect:h,duration:u,easing:f,rotateX:x,rotateY:b,rotateZ:v,translateZ:$,perspective:R,transformOrigin:I,flipDirection:E,rotationSpeed:D,continuous:j}=o;switch(t.current.style.transformStyle="preserve-3d",t.current.style.perspective=`${(R||100)*10}px`,t.current.style.willChange="transform",t.current.style.backfaceVisibility="hidden",C.set(t.current,{transformOrigin:`${I||"center"} ${I||"center"}`}),h){case"cardFlip":E==="horizontal"?C.to(t.current,{duration:u||1,rotateY:180,ease:f||"ease"}):C.to(t.current,{duration:u||1,rotateX:180,ease:f||"ease"});break;case"rotate3D":j?C.to(t.current,{duration:D||2,rotateX:x||0,rotateY:b||360,rotateZ:v||0,repeat:-1,ease:"none"}):C.to(t.current,{duration:u||1,rotateX:x||0,rotateY:b||180,rotateZ:v||0,ease:f||"ease"});break;case"perspective":C.to(t.current,{duration:u||1,rotateX:x||15,rotateY:b||15,ease:f||"ease"});break;case"depth":C.to(t.current,{duration:u||1,translateZ:($||5)*10,rotateX:(x||0)*.3,rotateY:(b||0)*.3,ease:f||"ease"});break;case"cube":{C.timeline().to(t.current,{duration:(u||1)/3,rotateY:90,ease:f||"ease"}).to(t.current,{duration:(u||1)/3,rotateX:90,ease:f||"ease"}).to(t.current,{duration:(u||1)/3,rotateZ:90,ease:f||"ease"});break}default:C.to(t.current,{duration:u||1,rotateX:x||0,rotateY:b||180,rotateZ:v||0,ease:f||"ease"})}},Z=()=>{if(!t.current)return;const{pathDirection:h,duration:u,delay:f,easing:x,pathData:b,allPaths:v,library:$,loop:R,stagger:I}=o;t.current.querySelectorAll("svg").forEach(g=>g.remove());let D=t.current.querySelectorAll("path");if(D.length===0&&(b||v)){const g=document.createElementNS("http://www.w3.org/2000/svg","svg");g.setAttribute("width","100%"),g.setAttribute("height","100%");const w=v&&v.length>0?v:[b||""];let k="0 0 800 600";const S=w[0];if(S){const T=S.match(/[\d.]+/g);if(T&&T.length>0){const L=T.map(se=>parseFloat(se)),p=Math.min(...L),O=Math.max(...L),B=Math.max(50,(O-p)*.1),N=Math.max(0,p-B),Q=O+B,_=Math.max(0,p-B),U=O+B;k=`${N} ${_} ${Q-N} ${U-_}`}}g.setAttribute("viewBox",k),g.setAttribute("preserveAspectRatio","xMidYMid meet"),g.style.position="absolute",g.style.top="0",g.style.left="0",g.style.pointerEvents="none",w.forEach(T=>{if(T&&T.trim()){const L=document.createElementNS("http://www.w3.org/2000/svg","path");L.setAttribute("d",T),L.setAttribute("stroke","#3b82f6"),L.setAttribute("stroke-width","3"),L.setAttribute("fill","none"),L.setAttribute("stroke-linecap","round"),L.setAttribute("stroke-linejoin","round"),L.style.filter="drop-shadow(0 1px 2px rgba(0,0,0,0.1))",g.appendChild(L)}}),t.current.appendChild(g),D=t.current.querySelectorAll("path")}if(D.length===0){console.error("No SVG paths found for animation");return}$==="anime"?typeof window.anime<"u"?(D.forEach(g=>{const w=g.getTotalLength();g.style.strokeDasharray=w.toString(),g.style.strokeDashoffset=(h==="forward"?w:0).toString()}),D.forEach((g,w)=>{const k=g.getTotalLength();window.anime({targets:g,strokeDashoffset:h==="forward"?0:k,duration:(u||2)*1e3,delay:(f||0)*1e3+w*(I||0)*1e3,easing:x==="ease"?"easeInOutQuad":x==="ease-in"?"easeInQuad":x==="ease-out"?"easeOutQuad":x==="ease-in-out"?"easeInOutQuad":"linear",loop:R||!1})})):(console.warn("Anime.js not loaded, falling back to GSAP for preview"),j()):j();function j(){D.forEach((g,w)=>{const k=g.getTotalLength();C.set(g,{strokeDasharray:k,strokeDashoffset:h==="forward"?k:0}),C.to(g,{strokeDashoffset:h==="forward"?0:k,duration:u||2,delay:(f||0)+w*(I||0),ease:x||"ease",repeat:R?-1:0})})}},H=()=>{if(!c.current)return;const{particleCount:h,colors:u,shapes:f,spread:x,angle:b,gravity:v,startVelocity:$,scalar:R,origin:I,delay:E}=o;if(window.confetti)D();else{const j=document.createElement("script");j.src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js",j.onload=()=>{D()},document.head.appendChild(j)}function D(){setTimeout(()=>{var B;const j=c.current.getBoundingClientRect(),g=(j.left+j.width*((I==null?void 0:I.x)||.5))/window.innerWidth,w=(j.top+j.height*((I==null?void 0:I.y)||.5))/window.innerHeight,k={},S={triangle:"M0 10 L5 0 L10 10z",heart:"M5 0 C2.5 -2.5 0 0 0 2.5 C0 5 2.5 7.5 5 10 C7.5 7.5 10 5 10 2.5 C10 0 7.5 -2.5 5 0z",plus:"M4 0 L6 0 L6 4 L10 4 L10 6 L6 6 L6 10 L4 10 L4 6 L0 6 L0 4 L4 4z",ribbon:"M0 3 Q3 0 6 3 Q9 6 12 3 Q15 0 18 3 L18 7 Q15 10 12 7 Q9 4 6 7 Q3 10 0 7 Z",squiggle:"M0 8 Q2 2 4 8 Q6 14 8 8 Q10 2 12 8 Q14 14 16 8 Q18 2 20 8",streamer:"M0 2 Q5 0 10 2 Q15 4 20 2 Q25 0 30 2 L30 4 Q25 6 20 4 Q15 2 10 4 Q5 6 0 4 Z"};f&&f.length>0&&f.forEach(N=>{var Q;S[N]&&(k[N]=(Q=window.confetti)==null?void 0:Q.shapeFromPath({path:S[N]}))});const T=(f==null?void 0:f.filter(N=>["square","circle","star"].includes(N)))||[],L=(f==null?void 0:f.filter(N=>!["square","circle","star"].includes(N)).map(N=>k[N]).filter(Boolean))||[],p=[...T,...L],O={particleCount:h||50,spread:x||45,angle:b||90,gravity:v||1,startVelocity:$||45,scalar:R||1,origin:{x:g,y:w},colors:u&&u.length>0?u:void 0,shapes:p.length>0?p:void 0};(B=window.confetti)==null||B.call(window,O)},(E||0)*1e3)}},re=()=>{if(!c.current)return;const{physicsEffect:h,objectCount:u,objectShape:f,objectSize:x,colors:b,delay:v,duration:$}=o;function R(){setTimeout(()=>{const I=c.current,E=I.getBoundingClientRect();I.querySelectorAll(".physics-object").forEach(j=>j.remove());for(let j=0;j<Math.min(u||10,15);j++){const g=document.createElement("div");g.className="physics-object";const w=Math.max(5,Math.min(x||20,30)),k=b&&b.length>0?b[Math.floor(Math.random()*b.length)]:"#3b82f6";g.style.position="absolute",g.style.width=w+"px",g.style.height=w+"px",g.style.backgroundColor=k,g.style.pointerEvents="none",g.style.zIndex="10",f==="circle"?g.style.borderRadius="50%":f==="polygon"&&(g.style.clipPath="polygon(50% 0%, 0% 100%, 100% 100%)");let S,T;const L=E.width/2,p=E.height/2;switch(h){case"drop":S=Math.random()*(E.width-w),T=-w;break;case"bounce":S=Math.random()*(E.width-w),T=p;break;case"explode":S=L-w/2,T=p-w/2;break;case"attract":{switch(Math.floor(Math.random()*4)){case 0:S=Math.random()*(E.width-w),T=0;break;case 1:S=E.width-w,T=Math.random()*(E.height-w);break;case 2:S=Math.random()*(E.width-w),T=E.height-w;break;case 3:S=0,T=Math.random()*(E.height-w);break;default:S=Math.random()*(E.width-w),T=Math.random()*(E.height-w)}break}case"repel":{S=L-w/2+(Math.random()-.5)*20,T=p-w/2+(Math.random()-.5)*20;break}default:S=Math.random()*(E.width-w),T=-w}g.style.left=S+"px",g.style.top=T+"px",I.appendChild(g);const O=Math.min($||5,3);switch(h){case"drop":C.to(g,{duration:O,y:E.height+w,ease:"power2.in",delay:j*.1});break;case"bounce":{C.timeline({delay:j*.1}).to(g,{duration:O/4,y:-50,ease:"power2.out"}).to(g,{duration:O/4,y:0,ease:"power2.in"}).to(g,{duration:O/4,y:-30,ease:"power2.out"}).to(g,{duration:O/4,y:0,ease:"power2.in"}),C.to(g,{duration:O,x:(Math.random()-.5)*100,delay:j*.1});break}case"explode":{const B=j/(u||10)*Math.PI*2,N=80+Math.random()*40;C.to(g,{duration:O,x:Math.cos(B)*N,y:Math.sin(B)*N,rotation:Math.random()*360,ease:"power2.out",delay:j*.05});break}case"attract":{C.timeline({delay:j*.1}).to(g,{duration:O*.8,x:L-w/2,y:p-w/2,ease:"power3.in",rotation:Math.random()*360}).to(g,{duration:O*.2,x:L-w/2+(Math.random()-.5)*15,y:p-w/2+(Math.random()-.5)*15,ease:"elastic.out(1, 0.5)",scale:.8+Math.random()*.4});break}case"repel":{const B=Math.atan2(T-p,S-L),N=120+Math.random()*80;C.timeline({delay:j*.05}).to(g,{duration:O*.3,x:S+Math.cos(B)*N*.7,y:T+Math.sin(B)*N*.7,ease:"power3.out",rotation:Math.random()*720,scale:1.2}).to(g,{duration:O*.7,x:S+Math.cos(B)*N,y:T+Math.sin(B)*N,ease:"power2.out",scale:1});break}}setTimeout(()=>{g.parentNode&&g.remove()},(O+j*.1+1)*1e3)}},(v||0)*1e3)}R()},ne=()=>{if(!c.current||!t.current)return;const{rippleEffect:h,size:u,color:f,opacity:x,rippleOrigin:b,customOrigin:v,duration:$,delay:R,easing:I,allowMultiple:E}=o;function D(){setTimeout(()=>{const j=c.current,w=t.current.getBoundingClientRect(),k=j.getBoundingClientRect();E||j.querySelectorAll(".ripple-preview").forEach(O=>O.remove());let S;switch(u){case"small":S=50;break;case"medium":S=100;break;case"large":S=150;break;case"auto":S=Math.max(w.width,w.height)*1.5;break;default:S=100}let T,L;switch(b){case"click":T=w.left-k.left+Math.random()*w.width,L=w.top-k.top+Math.random()*w.height;break;case"custom":T=w.left-k.left+((v==null?void 0:v.x)||50)/100*w.width,L=w.top-k.top+((v==null?void 0:v.y)||50)/100*w.height;break;case"center":default:T=w.left-k.left+w.width/2,L=w.top-k.top+w.height/2}switch(h){case"material":{const p=document.createElement("div");p.className="ripple-preview",p.style.position="absolute",p.style.borderRadius="50%",p.style.backgroundColor=f||"#2563eb",p.style.opacity=(x||.3).toString(),p.style.pointerEvents="none",p.style.transform="scale(0)",p.style.zIndex="10",p.style.width=S+"px",p.style.height=S+"px",p.style.left=T-S/2+"px",p.style.top=L-S/2+"px",j.appendChild(p),C.fromTo(p,{scale:0,opacity:x||.25},{duration:$||.8,scale:1,opacity:0,ease:I||"power2.out",onComplete:()=>p.remove()});break}case"water":{for(let p=0;p<3;p++){const O=document.createElement("div");O.className="ripple-preview",O.style.position="absolute",O.style.borderRadius="50%",O.style.border=`2px solid ${f||"#2563eb"}`,O.style.backgroundColor="transparent",O.style.opacity=(x||.3).toString(),O.style.pointerEvents="none",O.style.transform="scale(0)",O.style.zIndex="10",O.style.width=S+"px",O.style.height=S+"px",O.style.left=T-S/2+"px",O.style.top=L-S/2+"px",j.appendChild(O),C.to(O,{duration:$||.8,scale:1,opacity:0,ease:I||"power2.out",delay:p*(($||.8)*.15),onComplete:()=>O.remove()})}break}case"pulse":{const p=document.createElement("div");p.className="ripple-preview",p.style.position="absolute",p.style.borderRadius="50%",p.style.backgroundColor=f||"#2563eb",p.style.opacity=(x||.3).toString(),p.style.pointerEvents="none",p.style.transform="scale(1)",p.style.zIndex="10",p.style.width=S+"px",p.style.height=S+"px",p.style.left=T-S/2+"px",p.style.top=L-S/2+"px",j.appendChild(p),C.to(p,{duration:($||.8)/2,scale:1.5,opacity:(x||.25)*.7,ease:"power2.out",yoyo:!0,repeat:1,onComplete:()=>p.remove()});break}case"shockwave":{const p=document.createElement("div");p.className="ripple-preview",p.style.position="absolute",p.style.borderRadius="50%",p.style.border=`3px solid ${f||"#2563eb"}`,p.style.backgroundColor="transparent",p.style.opacity=(x||.3).toString(),p.style.pointerEvents="none",p.style.transform="scale(0)",p.style.zIndex="10",p.style.boxShadow=`0 0 20px ${f||"#2563eb"}`,p.style.width=S+"px",p.style.height=S+"px",p.style.left=T-S/2+"px",p.style.top=L-S/2+"px",j.appendChild(p),C.timeline().to(p,{duration:($||.8)*.3,scale:.3,opacity:x||.25,ease:"power2.out"}).to(p,{duration:($||.8)*.7,scale:1,opacity:0,ease:"power3.out",onComplete:()=>p.remove()});break}}},(R||0)*1e3)}D()},oe=()=>{if(!t.current)return;const{strings:h,typeSpeed:u,backSpeed:f,startDelay:x,showCursor:b,cursorChar:v,smartBackspace:$,loop:R}=o;if(typeof window.Typed>"u"){const E=document.createElement("script");E.src="https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js",E.onload=()=>{I()},document.head.appendChild(E)}else I();function I(){if(!t.current)return;t.current.innerHTML="",t.current._typed&&t.current._typed.destroy();const E={strings:h&&h.length>0?h:["Hello World!","This is a typewriter effect."],typeSpeed:u||50,backSpeed:f||30,startDelay:x||0,backDelay:700,loop:R||!1,showCursor:b!==!1,cursorChar:v||"|",smartBackspace:$!==!1,onComplete:()=>{R||setTimeout(()=>{t.current&&t.current._typed&&t.current._typed.reset()},2e3)}},D=new window.Typed(t.current,E);t.current._typed=D}},J=(h=!0)=>{if(!t.current)return;const{origin:u,direction:f,duration:x,delay:b,easing:v,color:$,shape:R,borderRadius:I}=o,E=(T,L,p)=>{if(p==="circle"){const N={"bottom-right":{start:"circle(0% at 100% 100%)",end:"circle(150% at 100% 100%)"},"bottom-left":{start:"circle(0% at 0% 100%)",end:"circle(150% at 0% 100%)"},"top-right":{start:"circle(0% at 100% 0%)",end:"circle(150% at 100% 0%)"},"top-left":{start:"circle(0% at 0% 0%)",end:"circle(150% at 0% 0%)"},center:{start:"circle(0% at 50% 50%)",end:"circle(150% at 50% 50%)"},left:{start:"circle(0% at 0% 50%)",end:"circle(150% at 0% 50%)"},right:{start:"circle(0% at 100% 50%)",end:"circle(150% at 100% 50%)"},top:{start:"circle(0% at 50% 0%)",end:"circle(150% at 50% 0%)"},bottom:{start:"circle(0% at 50% 100%)",end:"circle(150% at 50% 100%)"}},Q=N[T]||N.center;return L?Q.start:Q.end}if(p==="ellipse"){const N={"bottom-right":{start:"ellipse(0% 0% at 100% 100%)",end:"ellipse(150% 100% at 100% 100%)"},"bottom-left":{start:"ellipse(0% 0% at 0% 100%)",end:"ellipse(150% 100% at 0% 100%)"},"top-right":{start:"ellipse(0% 0% at 100% 0%)",end:"ellipse(150% 100% at 100% 0%)"},"top-left":{start:"ellipse(0% 0% at 0% 0%)",end:"ellipse(150% 100% at 0% 0%)"},center:{start:"ellipse(0% 0% at 50% 50%)",end:"ellipse(150% 100% at 50% 50%)"},left:{start:"ellipse(0% 0% at 0% 50%)",end:"ellipse(150% 100% at 0% 50%)"},right:{start:"ellipse(0% 0% at 100% 50%)",end:"ellipse(150% 100% at 100% 50%)"},top:{start:"ellipse(0% 0% at 50% 0%)",end:"ellipse(150% 100% at 50% 0%)"},bottom:{start:"ellipse(0% 0% at 50% 100%)",end:"ellipse(150% 100% at 50% 100%)"}},Q=N[T]||N.center;return L?Q.start:Q.end}if(p==="rounded-rectangle"){const N=I||12,Q={"bottom-right":{start:`inset(100% 0% 0% 100% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},"bottom-left":{start:`inset(100% 100% 0% 0% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},"top-right":{start:`inset(0% 0% 100% 100% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},"top-left":{start:`inset(0% 100% 100% 0% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},center:{start:`inset(50% 50% 50% 50% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},left:{start:`inset(0% 100% 0% 0% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},right:{start:`inset(0% 0% 0% 100% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},top:{start:`inset(0% 0% 100% 0% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`},bottom:{start:`inset(100% 0% 0% 0% round ${N}px)`,end:`inset(0% 0% 0% 0% round ${N}px)`}},_=Q[T]||Q["bottom-right"];return L?_.start:_.end}const O={"bottom-right":{start:"polygon(100% 100%, 100% 100%, 100% 100%, 100% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"bottom-left":{start:"polygon(0% 100%, 0% 100%, 0% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"top-right":{start:"polygon(100% 0%, 100% 0%, 100% 0%, 100% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},"top-left":{start:"polygon(0% 0%, 0% 0%, 0% 0%, 0% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},center:{start:"polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},left:{start:"polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},right:{start:"polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},top:{start:"polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"},bottom:{start:"polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)",end:"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)"}},B=O[T||"bottom-right"]||O["bottom-right"];return L?B.start:B.end},D=typeof u=="string"?u:"bottom-right",j=typeof R=="string"?R:"rectangle",g=f==="reveal"?E(D,!0,j):E(D,!1,j),w=f==="reveal"?E(D,!1,j):E(D,!0,j);t.current.style.position="relative",t.current.style.overflow="hidden";let k=t.current.querySelector(".fill-pseudo-element");k||(k=document.createElement("div"),k.className="fill-pseudo-element",k.style.position="absolute",k.style.top="0",k.style.left="0",k.style.right="0",k.style.bottom="0",k.style.clipPath=g,k.style.zIndex="-1",k.style.pointerEvents="none",t.current.insertBefore(k,t.current.firstChild)),k.style.backgroundColor=$||"#3b82f6";const S=h?w:g;C.to(k,{clipPath:S,duration:x||1,delay:h&&b||0,ease:h?v||"power2.out":"power2.inOut"})};q.useEffect(()=>{y();const h=c.current;h&&h._magneticCleanup&&h._magneticCleanup()},[o]),q.useEffect(()=>{const h=t.current;return()=>{h&&h.vanillaTilt&&h.vanillaTilt.destroy()}},[]);const ae=()=>{if(n==="magnetic")return"Move your mouse over the demo box to see magnetic effect";if(n==="pathdraw")return"Click 'Play' to see path drawing animation";if(n==="typewriter")switch(o.trigger||"timeline"){case"click":return"Click the text to start typewriter effect";case"hover":return"Hover over the text to start typewriter effect";case"timeline":return"Click 'Play' to start typewriter effect";default:return"Click 'Play' to start typewriter effect"}if(n==="confetti")switch(o.trigger||"click"){case"click":return"Click the demo box to launch confetti";case"hover":return"Hover over the demo box to launch confetti";case"timeline":return"Click 'Play' to launch confetti";default:return"Click 'Play' to launch confetti"}if(n==="physics")switch(o.trigger||"click"){case"click":return"Click the demo box to start physics simulation";case"hover":return"Hover over the demo box to start physics simulation";case"timeline":return"Click 'Play' to start physics simulation";default:return"Click 'Play' to start physics simulation"}if(n==="ripple")switch(o.trigger||"click"){case"click":return"Click the demo box to create ripple effect";case"hover":return"Hover over the demo box to create ripple effect";case"timeline":return"Click 'Play' to create ripple effect";default:return"Click 'Play' to create ripple effect"}switch(o.trigger||"timeline"){case"click":return"Click the demo box to trigger";case"hover":return"Hover over the demo box to trigger";case"timeline":return"Click 'Play' to see timeline animation";case"continuous":return"Click 'Play' to see continuous animation";default:return"Click 'Play' to see the animation"}},ie=h=>{(o.trigger||"timeline")===h&&l()},le=()=>{(o.trigger||"timeline")==="hover"&&(r(!0),n==="fill"?J(!0):l())},ce=()=>{(o.trigger||"timeline")==="hover"&&(r(!1),n==="fill"&&J(!1))},de=()=>!o.objectIds||o.objectIds.length===0?!1:n==="typewriter"||o.objectIds.some(h=>h.toLowerCase().startsWith("text")),ue=()=>!o.objectIds||o.objectIds.length===0?"Demo":o.objectIds[0];return e.jsxs("div",{className:"w-96 bg-gray-50 border border-gray-200 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Live Preview"}),(o.trigger==="timeline"||o.trigger==="continuous"||!o.trigger)&&e.jsx("button",{onClick:l,className:"px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-700 transition-colors cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500",children:"Play"})]}),e.jsx("div",{ref:c,className:"h-32 bg-white border border-gray-300 rounded flex items-center justify-center relative overflow-hidden cursor-pointer",onClick:()=>ie("click"),onMouseEnter:le,onMouseLeave:ce,children:n==="pathdraw"?e.jsx("div",{ref:t,className:"w-full h-full relative flex items-center justify-center"}):de()||n==="typewriter"?e.jsx("div",{ref:t,className:"px-6 py-3 text-gray-800 font-medium text-base text-center max-w-full overflow-hidden",style:{wordBreak:"break-word"},children:n==="typewriter"?"":ue()}):e.jsx("div",{ref:t,className:"w-24 h-20 bg-blue-500 shadow-md flex items-center justify-center text-white font-bold text-sm",style:{borderRadius:o.shape==="rounded-rectangle"?`${o.borderRadius||12}px`:o.shape==="circle"?"50%":"6px"},children:"Demo"})}),e.jsx("p",{className:"text-xs text-gray-600 mt-3 text-center",children:ae()})]})}function te({value:n,onChange:o,placeholder:d="Type and press Enter or comma to add...",label:c="Object IDs"}){const[t,a]=q.useState(""),r=q.useRef(null),y=m=>{const P=m.trim();P&&!n.includes(P)&&o([...n,P]),a("")},l=m=>{o(n.filter((P,z)=>z!==m))},s=m=>{m.key==="Enter"||m.key===","?(m.preventDefault(),y(t)):m.key==="Backspace"&&t===""&&n.length>0&&l(n.length-1)},i=m=>{const P=m.target.value;if(P.includes(",")||P.includes(" ")){const z=P.split(/[,\s]+/),F=z.slice(0,-1).filter(X=>X.trim()),G=z[z.length-1];F.forEach(X=>{const Y=X.trim();Y&&!n.includes(Y)&&o([...n,Y])}),a(G)}else a(P)},M=()=>{var m;(m=r.current)==null||m.focus()};return e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[c," (from Accessibility - alternative text):"]}),e.jsx("div",{onClick:M,className:"min-h-[42px] w-full px-3 py-2 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent bg-white cursor-text",children:e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[n.map((m,P)=>e.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200 transition-colors",children:[m,e.jsx("button",{type:"button",onClick:z=>{z.stopPropagation(),l(P)},className:"ml-2 text-blue-600 hover:text-blue-800 focus:outline-none","aria-label":`Remove ${m}`,children:"×"})]},P)),e.jsx("input",{ref:r,type:"text",value:t,onChange:i,onKeyDown:s,placeholder:n.length===0?d:"",className:"flex-1 min-w-[120px] outline-none bg-transparent text-sm"})]})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1 font-medium",children:"Type and press Enter, comma, or space to add tags. Click × to remove."})]})}function be(){const[n,o]=q.useState({objectIds:[],trigger:"click",effect:"cardFlip",duration:1,delay:0,easing:"ease",rotateX:0,rotateY:180,rotateZ:0,translateZ:0,perspective:100,transformOrigin:"center",flipDirection:"horizontal",rotationSpeed:2,continuous:!1}),[d,c]=q.useState(""),t=a=>{o(r=>({...r,objectIds:a}))};return q.useEffect(()=>{if(n.objectIds.length>0){const a=me(n);c(a)}else c("")},[n]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"3D Transform Effect Generator"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(te,{label:"Object IDs",value:n.objectIds,onChange:t,placeholder:"Enter object IDs (e.g., card1, button2)..."}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Enter the data-acc-text values of elements you want to apply 3D transforms to"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:n.trigger,onChange:a=>o(r=>({...r,trigger:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"3D Effect Type:"}),e.jsxs("select",{value:n.effect,onChange:a=>o(r=>({...r,effect:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"cardFlip",children:"Card Flip"}),e.jsx("option",{value:"rotate3D",children:"3D Rotation"}),e.jsx("option",{value:"perspective",children:"Perspective Tilt"}),e.jsx("option",{value:"depth",children:"Depth Movement"}),e.jsx("option",{value:"cube",children:"Cube Rotation"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transform Origin:"}),e.jsxs("select",{value:n.transformOrigin,onChange:a=>o(r=>({...r,transformOrigin:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"top",children:"Top"}),e.jsx("option",{value:"bottom",children:"Bottom"}),e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"right",children:"Right"}),e.jsx("option",{value:"top-left",children:"Top Left"}),e.jsx("option",{value:"top-right",children:"Top Right"}),e.jsx("option",{value:"bottom-left",children:"Bottom Left"}),e.jsx("option",{value:"bottom-right",children:"Bottom Right"})]})]})]}),n.effect==="cardFlip"&&e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-3",children:"Card Flip Settings"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Flip Direction:"}),e.jsxs("select",{value:n.flipDirection,onChange:a=>o(r=>({...r,flipDirection:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"horizontal",children:"Horizontal (Y-axis)"}),e.jsx("option",{value:"vertical",children:"Vertical (X-axis)"})]})]})]}),n.effect==="rotate3D"&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-green-900 mb-3",children:"3D Rotation Settings"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{children:e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:n.continuous,onChange:a=>o(r=>({...r,continuous:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Continuous Rotation"})]})}),n.continuous&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotation Speed (seconds per cycle):"}),e.jsx("input",{type:"number",step:"0.1",min:"0.1",value:n.rotationSpeed,onChange:a=>o(r=>({...r,rotationSpeed:parseFloat(a.target.value)||2})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotate X (degrees):"}),e.jsx("input",{type:"range",min:"-360",max:"360",step:"15",value:n.rotateX,onChange:a=>o(r=>({...r,rotateX:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"-360°"}),e.jsxs("span",{className:"font-medium",children:[n.rotateX,"°"]}),e.jsx("span",{children:"360°"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotate Y (degrees):"}),e.jsx("input",{type:"range",min:"-360",max:"360",step:"15",value:n.rotateY,onChange:a=>o(r=>({...r,rotateY:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"-360°"}),e.jsxs("span",{className:"font-medium",children:[n.rotateY,"°"]}),e.jsx("span",{children:"360°"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotate Z (degrees):"}),e.jsx("input",{type:"range",min:"-360",max:"360",step:"15",value:n.rotateZ,onChange:a=>o(r=>({...r,rotateZ:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"-360°"}),e.jsxs("span",{className:"font-medium",children:[n.rotateZ,"°"]}),e.jsx("span",{children:"360°"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Perspective (px):"}),e.jsx("input",{type:"range",min:"100",max:"2000",step:"50",value:n.perspective,onChange:a=>o(r=>({...r,perspective:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Close (100px)"}),e.jsxs("span",{className:"font-medium",children:[n.perspective,"px"]}),e.jsx("span",{children:"Far (2000px)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Translate Z (%):"}),e.jsx("input",{type:"range",min:"-20",max:"20",step:"1",value:n.translateZ,onChange:a=>o(r=>({...r,translateZ:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Back (-20%)"}),e.jsxs("span",{className:"font-medium",children:[n.translateZ,"%"]}),e.jsx("span",{children:"Forward (20%)"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0.1",value:n.duration,onChange:a=>o(r=>({...r,duration:parseFloat(a.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",value:n.delay,onChange:a=>o(r=>({...r,delay:parseFloat(a.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:n.easing,onChange:a=>o(r=>({...r,easing:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]}),e.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-purple-900 mb-2",children:"3D Transform Effects:"}),e.jsxs("ul",{className:"text-sm text-purple-800 space-y-1",children:[e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Card Flip:"})," Classic flip animation for cards and panels"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"3D Rotation:"})," Rotate around X, Y, and Z axes simultaneously"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Perspective Tilt:"})," Subtle 3D tilt with depth perception"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Depth Movement:"})," Move elements forward/backward in 3D space"]}),e.jsxs("li",{children:["• ",e.jsx("strong",{children:"Cube Rotation:"})," Sequential rotation on all three axes"]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(ee,{effectType:"transform3d",config:n})}),d&&e.jsx(K,{code:d})]})}const Ae=Object.freeze(Object.defineProperty({__proto__:null,default:be},Symbol.toStringTag,{value:"Module"}));function ve(){var l;const[n,o]=q.useState({objectIds:[],trigger:"timeline",library:"gsap",direction:"forward",duration:5,delay:0,easing:"ease",pathData:"M495.29 15.2104C478.109 16.0871 462.102 21.2475 450.305 34.2814C434.222 52.0498 426.297 77.2663 427.555 100.969C428.017 109.667 429.795 117.685 432.661 125.884C425.432 120.497 417.861 115.684 410.156 111.013C394.142 101.305 379.599 93.8453 360.259 96.6641C344.513 98.9591 332.671 107.521 323.296 120.144C312.694 134.419 307.191 152.517 309.963 170.249C312.993 189.631 325.22 205.222 340.843 216.402C357.531 228.344 377.638 234.422 397.797 237.085C380.367 244.749 365.277 257.823 358.318 275.894C349.62 298.482 351.872 326.025 361.837 347.849C364.142 352.856 366.942 357.621 370.195 362.072C353.508 363.696 337.221 368.203 325.154 380.545C321.772 365.346 315.349 351.111 301.732 342.434C290.556 335.314 276.563 334.272 263.868 337.203C250.241 340.349 238.245 348.561 230.946 360.539C221.761 375.751 219.033 394.002 223.367 411.235C225.036 417.999 227.708 424.316 230.557 430.651C213.205 423.578 193.473 415.257 174.97 423.133C163.675 427.941 154.22 438.343 149.779 449.699C145.251 461.278 144.757 474.55 149.94 485.992C156.908 501.377 171.179 511.014 186.574 516.725C198.054 520.984 209.038 521.941 221.164 521.322C210.173 536.492 203.17 551.621 206.114 570.738C208.537 586.471 215.93 601.442 228.964 610.936C240.989 619.696 253.778 619.403 267.931 620.637C273.772 621.146 279.937 622.256 285.767 621.243C296.849 619.318 306.515 611.166 312.634 602.047C317.799 594.348 321.357 585.098 323.54 576.121C332.884 592.623 349.394 609.108 368.158 614.281C375.975 616.436 383.748 615.495 390.748 611.439C399.697 606.254 406.776 597.357 410.674 587.877C425.228 601.917 437.268 618.06 449.199 634.307C498.669 701.67 522.963 763.688 535.576 846.362C537.001 855.478 537.971 864.66 538.482 873.872C539.03 883.882 539.083 894.05 540.205 904.008C537.098 898.746 532.82 893.1 530.575 887.5C528.478 882.271 528.287 875.961 526.986 870.451C524.231 859.234 521.136 848.103 517.705 837.075C513.422 822.912 508.968 808.481 503.158 794.866C491.353 767.196 465.442 726.975 440.888 709.953C431.492 703.439 420.337 698.838 409.307 695.965C377.246 687.616 335.482 693.258 306.948 710.257C306.021 710.802 305.111 711.374 304.218 711.974C317.498 713.857 331.268 715.112 343.455 721.134C349.902 724.32 355.862 728.532 361.782 732.593C396.483 756.394 424.839 787.538 453.573 817.937C479.882 845.769 506.103 873.708 529.178 904.349C532.417 908.65 537.504 914.814 538.629 920.153C539.868 926.033 536.921 941.957 536.257 948.957C534.623 966.2 532.591 983.695 532.346 1001.02C532.304 1003.99 532.166 1010 535.855 1010.9C537.075 1011.2 538.733 1010.68 539.661 1009.92C540.795 1009 541.382 1007.67 541.891 1006.34C543.668 1001.68 544.847 996.577 546.267 991.788C548.81 983.216 551.478 974.752 554.584 966.364C559.572 952.898 565.287 937.985 572.732 925.726C576.48 919.554 581.08 914.374 586.42 909.539C602.362 911.398 627.113 902.881 641.302 895.542C648.85 891.57 655.995 886.876 662.638 881.527C677.96 869.33 689.948 852.337 699.976 835.632C713.147 813.692 727.129 779.449 750.372 766.717C763.588 759.477 776.861 758.526 790.649 753.926C783.656 751.62 776.064 750.684 768.766 749.8C736.886 745.936 699.566 748.172 670.521 762.813C661.317 767.453 652.577 773.327 644.565 779.812C639.658 783.922 634.861 788.011 630.52 792.736C609.764 815.33 593.636 847.887 583.507 876.622C580.704 884.576 577.152 893.23 576.365 901.677C576.208 903.364 576.168 905.073 576.155 906.768C570.917 913.25 566.641 919.646 562.123 926.623C565.328 906.222 568.345 885.792 571.174 865.335C572.605 855.253 573.576 844.982 575.597 835.004C577.925 823.507 581.259 812.251 584.58 801.01C595.67 763.976 609.642 727.867 626.367 693.013C630.469 684.426 634.358 675.56 639.002 667.261C642.245 661.463 646.247 656.003 649.882 650.44C655.206 642.165 660.456 633.843 665.633 625.475C667.498 634.273 669.987 645.542 677.96 650.902C688.644 658.084 705.895 659.29 718.163 656.606C730.536 653.899 740.64 646.061 747.313 635.405C748.477 633.546 749.545 631.616 750.611 629.699C757.089 642.339 767.423 656.228 781.513 660.831C792.595 664.452 805.386 661.869 815.537 656.639C826.942 650.764 837.102 640.424 840.945 627.987C844.273 617.216 841.733 606.005 836.541 596.256C831.688 587.144 824.604 580.178 817.259 573.1C836.45 571.374 854.179 564.796 866.881 549.636C874.185 540.918 879.435 530.194 878.298 518.575C876.614 501.358 863.525 485.589 850.603 475.074C841.695 467.826 831.629 462.116 820.354 459.62C829.147 447.25 836.998 435.607 834.384 419.728C832.292 407.017 824.236 395.35 813.769 388.011C803.202 380.601 789.486 377.048 776.72 379.329C757.393 382.782 742.023 395.202 731.192 411.076C729.074 414.178 727.206 417.442 725.603 420.838C719.278 401.389 714.393 381.551 694.589 371.593C685.271 366.908 675.52 366.371 665.668 369.682C651.744 374.36 638.373 385.1 631.795 398.358C623.969 414.13 628.243 431.615 633.706 447.426C635.675 453.125 638.26 458.482 641.126 463.776C625.539 463.388 611.589 465.812 599.936 477.008C589.416 487.116 577.38 509.491 577.082 524.318C576.733 541.696 593.085 565.679 604.668 577.604C612.896 586.077 621.187 590.888 633.2 590.931C647.383 590.983 657.417 585.204 670.327 581.118C665.041 597.552 653.957 611.076 644.309 625.146C638.149 634.151 632.17 643.278 626.374 652.522C620.588 661.629 616.154 671.208 611.441 680.884C602.156 699.947 593.808 718.877 585.983 738.584C582.17 748.188 577.586 758.192 575.133 768.24C572.674 674.536 555.866 580.837 533.14 490.064C529.832 476.852 525.974 463.794 522.454 450.639C516.375 427.547 509.323 404.722 501.319 382.225C498.953 375.715 496.801 368.568 493.571 362.442C496.856 357.539 500.185 352.674 503.123 347.552C506.418 355.228 509.538 362.99 513.62 370.293C522.639 386.429 537.968 404.698 555.64 411.608C565.628 415.513 576.033 414.887 585.77 410.609C601.979 403.489 620.717 384.803 627.01 368.123C632.692 353.063 632.417 339.902 626.418 325.061C646.01 332.171 670.231 337.199 690.051 327.898C705.567 320.617 717.873 303.423 723.397 287.579C728.106 274.073 728.644 259.305 722.337 246.203C714.746 230.431 698.661 220.981 682.649 215.521C667.774 210.448 652.764 208.831 637.149 208.494C657.822 197.631 675.464 183.091 682.704 160.058C687.626 144.397 687.962 122.848 680.233 108.008C673.507 95.0943 660.982 87.6337 647.448 83.4166C631.964 78.5923 609.053 75.6432 593.167 78.8787C586.052 80.3276 579.243 83.1824 572.839 86.5556C569.546 88.3052 566.314 90.1679 563.149 92.1403C566.112 73.6631 566.23 56.6858 554.799 40.9018C544.764 27.0449 528.734 18.5769 512.009 16.0711C506.472 15.2908 500.877 15.0028 495.29 15.2104Z",allPaths:void 0,loop:!1,stagger:.2}),[d,c]=q.useState(""),t=q.useRef(null),a=s=>{o(i=>({...i,objectIds:s}))},r=s=>{var M;const i=(M=s.target.files)==null?void 0:M[0];if(i&&i.type==="image/svg+xml"){const m=new FileReader;m.onload=P=>{var G;const F=((G=P.target)==null?void 0:G.result).match(/<path[^>]*d="([^"]*)"[^>]*>/g);if(F&&F.length>0){const X=[];F.forEach(Y=>{const V=Y.match(/d="([^"]*)"/);V&&V[1].split(/[Zz]/).filter(Z=>Z.trim().length>0).forEach(Z=>{const H=Z.trim();if(H.length===0)return;const re=H+(H.endsWith("Z")||H.endsWith("z")?"":"Z"),ne=/^M\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L\s*\d+\s*\d+\s*L?\s*\d*\s*\d*\s*$/i.test(H),oe=H.length<20,J=/^[ML\d\s.,]+$/i.test(H);!ne&&!oe&&!J&&X.push(re)})}),X.length===0&&F.forEach(Y=>{const V=Y.match(/d="([^"]*)"/);if(V){const W=V[1];!/^M\s*0\s*0\s*L\s*\d+\s*0\s*L\s*\d+\s*\d+\s*L\s*0\s*\d+\s*L?\s*0\s*0\s*Z?$/i.test(W.trim())&&W.trim().length>10&&X.push(W)}}),X.length>0?o(Y=>({...Y,pathData:X[0],allPaths:X})):alert("No drawable paths found in the SVG file. The file may only contain background elements or very simple shapes.")}else alert("No valid path data found in the SVG file. Please ensure the SVG contains a <path> element with a 'd' attribute.")},m.readAsText(i)}else alert("Please select a valid SVG file.")};q.useEffect(()=>{if(n.objectIds.length>0){const s=fe(n);c(s)}else c("")},[n]);const y={wave:"M10,150 C10,150 100,25 190,150 C280,275 370,150 370,150",heart:"M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z",star:"M12,2l3.09,6.26L22,9.27l-5,4.87 L18.18,21L12,17.77L5.82,21L7,14.14L2,9.27l6.91-1.01L12,2z",circle:"M50,10 A40,40 0 1,1 49.9,10",arrow:"M10,50 L50,10 L50,30 L90,30 L90,70 L50,70 L50,90 Z",flower:"M495.29 15.2104C478.109 16.0871 462.102 21.2475 450.305 34.2814C434.222 52.0498 426.297 77.2663 427.555 100.969C428.017 109.667 429.795 117.685 432.661 125.884C425.432 120.497 417.861 115.684 410.156 111.013C394.142 101.305 379.599 93.8453 360.259 96.6641C344.513 98.9591 332.671 107.521 323.296 120.144C312.694 134.419 307.191 152.517 309.963 170.249C312.993 189.631 325.22 205.222 340.843 216.402C357.531 228.344 377.638 234.422 397.797 237.085C380.367 244.749 365.277 257.823 358.318 275.894C349.62 298.482 351.872 326.025 361.837 347.849C364.142 352.856 366.942 357.621 370.195 362.072C353.508 363.696 337.221 368.203 325.154 380.545C321.772 365.346 315.349 351.111 301.732 342.434C290.556 335.314 276.563 334.272 263.868 337.203C250.241 340.349 238.245 348.561 230.946 360.539C221.761 375.751 219.033 394.002 223.367 411.235C225.036 417.999 227.708 424.316 230.557 430.651C213.205 423.578 193.473 415.257 174.97 423.133C163.675 427.941 154.22 438.343 149.779 449.699C145.251 461.278 144.757 474.55 149.94 485.992C156.908 501.377 171.179 511.014 186.574 516.725C198.054 520.984 209.038 521.941 221.164 521.322C210.173 536.492 203.17 551.621 206.114 570.738C208.537 586.471 215.93 601.442 228.964 610.936C240.989 619.696 253.778 619.403 267.931 620.637C273.772 621.146 279.937 622.256 285.767 621.243C296.849 619.318 306.515 611.166 312.634 602.047C317.799 594.348 321.357 585.098 323.54 576.121C332.884 592.623 349.394 609.108 368.158 614.281C375.975 616.436 383.748 615.495 390.748 611.439C399.697 606.254 406.776 597.357 410.674 587.877C425.228 601.917 437.268 618.06 449.199 634.307C498.669 701.67 522.963 763.688 535.576 846.362C537.001 855.478 537.971 864.66 538.482 873.872C539.03 883.882 539.083 894.05 540.205 904.008C537.098 898.746 532.82 893.1 530.575 887.5C528.478 882.271 528.287 875.961 526.986 870.451C524.231 859.234 521.136 848.103 517.705 837.075C513.422 822.912 508.968 808.481 503.158 794.866C491.353 767.196 465.442 726.975 440.888 709.953C431.492 703.439 420.337 698.838 409.307 695.965C377.246 687.616 335.482 693.258 306.948 710.257C306.021 710.802 305.111 711.374 304.218 711.974C317.498 713.857 331.268 715.112 343.455 721.134C349.902 724.32 355.862 728.532 361.782 732.593C396.483 756.394 424.839 787.538 453.573 817.937C479.882 845.769 506.103 873.708 529.178 904.349C532.417 908.65 537.504 914.814 538.629 920.153C539.868 926.033 536.921 941.957 536.257 948.957C534.623 966.2 532.591 983.695 532.346 1001.02C532.304 1003.99 532.166 1010 535.855 1010.9C537.075 1011.2 538.733 1010.68 539.661 1009.92C540.795 1009 541.382 1007.67 541.891 1006.34C543.668 1001.68 544.847 996.577 546.267 991.788C548.81 983.216 551.478 974.752 554.584 966.364C559.572 952.898 565.287 937.985 572.732 925.726C576.48 919.554 581.08 914.374 586.42 909.539C602.362 911.398 627.113 902.881 641.302 895.542C648.85 891.57 655.995 886.876 662.638 881.527C677.96 869.33 689.948 852.337 699.976 835.632C713.147 813.692 727.129 779.449 750.372 766.717C763.588 759.477 776.861 758.526 790.649 753.926C783.656 751.62 776.064 750.684 768.766 749.8C736.886 745.936 699.566 748.172 670.521 762.813C661.317 767.453 652.577 773.327 644.565 779.812C639.658 783.922 634.861 788.011 630.52 792.736C609.764 815.33 593.636 847.887 583.507 876.622C580.704 884.576 577.152 893.23 576.365 901.677C576.208 903.364 576.168 905.073 576.155 906.768C570.917 913.25 566.641 919.646 562.123 926.623C565.328 906.222 568.345 885.792 571.174 865.335C572.605 855.253 573.576 844.982 575.597 835.004C577.925 823.507 581.259 812.251 584.58 801.01C595.67 763.976 609.642 727.867 626.367 693.013C630.469 684.426 634.358 675.56 639.002 667.261C642.245 661.463 646.247 656.003 649.882 650.44C655.206 642.165 660.456 633.843 665.633 625.475C667.498 634.273 669.987 645.542 677.96 650.902C688.644 658.084 705.895 659.29 718.163 656.606C730.536 653.899 740.64 646.061 747.313 635.405C748.477 633.546 749.545 631.616 750.611 629.699C757.089 642.339 767.423 656.228 781.513 660.831C792.595 664.452 805.386 661.869 815.537 656.639C826.942 650.764 837.102 640.424 840.945 627.987C844.273 617.216 841.733 606.005 836.541 596.256C831.688 587.144 824.604 580.178 817.259 573.1C836.45 571.374 854.179 564.796 866.881 549.636C874.185 540.918 879.435 530.194 878.298 518.575C876.614 501.358 863.525 485.589 850.603 475.074C841.695 467.826 831.629 462.116 820.354 459.62C829.147 447.25 836.998 435.607 834.384 419.728C832.292 407.017 824.236 395.35 813.769 388.011C803.202 380.601 789.486 377.048 776.72 379.329C757.393 382.782 742.023 395.202 731.192 411.076C729.074 414.178 727.206 417.442 725.603 420.838C719.278 401.389 714.393 381.551 694.589 371.593C685.271 366.908 675.52 366.371 665.668 369.682C651.744 374.36 638.373 385.1 631.795 398.358C623.969 414.13 628.243 431.615 633.706 447.426C635.675 453.125 638.26 458.482 641.126 463.776C625.539 463.388 611.589 465.812 599.936 477.008C589.416 487.116 577.38 509.491 577.082 524.318C576.733 541.696 593.085 565.679 604.668 577.604C612.896 586.077 621.187 590.888 633.2 590.931C647.383 590.983 657.417 585.204 670.327 581.118C665.041 597.552 653.957 611.076 644.309 625.146C638.149 634.151 632.17 643.278 626.374 652.522C620.588 661.629 616.154 671.208 611.441 680.884C602.156 699.947 593.808 718.877 585.983 738.584C582.17 748.188 577.586 758.192 575.133 768.24C572.674 674.536 555.866 580.837 533.14 490.064C529.832 476.852 525.974 463.794 522.454 450.639C516.375 427.547 509.323 404.722 501.319 382.225C498.953 375.715 496.801 368.568 493.571 362.442C496.856 357.539 500.185 352.674 503.123 347.552C506.418 355.228 509.538 362.99 513.62 370.293C522.639 386.429 537.968 404.698 555.64 411.608C565.628 415.513 576.033 414.887 585.77 410.609C601.979 403.489 620.717 384.803 627.01 368.123C632.692 353.063 632.417 339.902 626.418 325.061C646.01 332.171 670.231 337.199 690.051 327.898C705.567 320.617 717.873 303.423 723.397 287.579C728.106 274.073 728.644 259.305 722.337 246.203C714.746 230.431 698.661 220.981 682.649 215.521C667.774 210.448 652.764 208.831 637.149 208.494C657.822 197.631 675.464 183.091 682.704 160.058C687.626 144.397 687.962 122.848 680.233 108.008C673.507 95.0943 660.982 87.6337 647.448 83.4166C631.964 78.5923 609.053 75.6432 593.167 78.8787C586.052 80.3276 579.243 83.1824 572.839 86.5556C569.546 88.3052 566.314 90.1679 563.149 92.1403C566.112 73.6631 566.23 56.6858 554.799 40.9018C544.764 27.0449 528.734 18.5769 512.009 16.0711C506.472 15.2908 500.877 15.0028 495.29 15.2104Z"};return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"SVG Path Drawing Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(te,{value:n.objectIds,onChange:a,placeholder:"e.g. content1, text2, image3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:n.trigger,onChange:s=>o(i=>({...i,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation Library:"}),e.jsxs("select",{value:n.library,onChange:s=>o(i=>({...i,library:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"gsap",children:"GSAP"}),e.jsx("option",{value:"anime",children:"Anime.js"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Direction:"}),e.jsxs("select",{value:n.direction,onChange:s=>o(i=>({...i,direction:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"forward",children:"Draw Forward"}),e.jsx("option",{value:"reverse",children:"Draw Reverse"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0.1",value:n.duration,onChange:s=>o(i=>({...i,duration:parseFloat(s.target.value)||2})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",value:n.delay,onChange:s=>o(i=>({...i,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:n.easing,onChange:s=>o(i=>({...i,easing:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"loop",checked:n.loop,onChange:s=>o(i=>({...i,loop:s.target.checked})),className:"mr-2"}),e.jsx("label",{htmlFor:"loop",className:"text-sm font-medium text-gray-700",children:"Loop Animation"})]})}),e.jsx("div",{className:"grid grid-cols-1 gap-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stagger Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",value:n.stagger,onChange:s=>o(i=>({...i,stagger:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Delay between multiple paths (for multiple path SVGs)"})]})}),e.jsxs("div",{className:"border-t pt-6 mt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Path Configuration"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sample Paths:"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:Object.entries(y).map(([s,i])=>e.jsx("button",{onClick:()=>o(M=>({...M,pathData:i})),className:`px-3 py-2 text-sm rounded-md transition-colors capitalize ${n.pathData===i?"bg-blue-100 border-2 border-blue-500 text-blue-700":"bg-gray-100 hover:bg-gray-200 border-2 border-transparent"}`,children:s},s))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Upload SVG File:"}),e.jsx("input",{ref:t,type:"file",accept:".svg",onChange:r,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Upload an SVG file to extract path data automatically",n.allPaths&&n.allPaths.length>1&&e.jsxs("span",{className:"text-green-600 font-medium",children:[" ","• ",n.allPaths.length," paths found"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SVG Path Data (d attribute):"}),e.jsx("textarea",{value:n.pathData,onChange:s=>o(i=>({...i,pathData:s.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm",placeholder:"M10,150 C10,150 100,25 190,150 C280,275 370,150 370,150"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter SVG path data or use the sample paths above"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(ee,{effectType:"pathdraw",config:{...n,pathDirection:n.direction}},`${n.pathData}-${((l=n.allPaths)==null?void 0:l.length)||0}-${n.duration}-${n.stagger}`)}),d&&e.jsx(K,{code:d})]})}const Be=Object.freeze(Object.defineProperty({__proto__:null,default:ve},Symbol.toStringTag,{value:"Module"}));function we(){const[n,o]=q.useState({objectIds:[],trigger:"click",particleCount:50,colors:["#ff0000","#00ff00","#0000ff","#ffff00","#ff00ff","#00ffff"],shapes:["square","circle"],duration:3,delay:0,origin:{x:.5,y:.5},spread:45,angle:90,gravity:1,startVelocity:45,scalar:1}),[d,c]=q.useState(""),t=s=>{o(i=>({...i,objectIds:s}))};q.useEffect(()=>{if(n.objectIds.length>0){const s=xe(n);c(s)}else c("")},[n]);const a=(s,i)=>{const M=[...n.colors];M[s]=i,o(m=>({...m,colors:M}))},r=()=>{o(s=>({...s,colors:[...s.colors,"#ff0000"]}))},y=s=>{o(i=>({...i,colors:i.colors.filter((M,m)=>m!==s)}))},l=s=>{o(i=>{const M=i.shapes.includes(s)?i.shapes.filter(m=>m!==s):[...i.shapes,s];return{...i,shapes:M}})};return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Confetti Effect Generator"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(te,{value:n.objectIds,onChange:t,placeholder:"e.g. button1, success-message, celebration",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:n.trigger,onChange:s=>o(i=>({...i,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"}),e.jsx("option",{value:"timeline",children:"Timeline Start"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Particle Count:"}),e.jsx("input",{type:"number",min:"1",max:"500",value:n.particleCount,onChange:s=>o(i=>({...i,particleCount:parseInt(s.target.value)||50})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.5",min:"0.5",max:"10",value:n.duration,onChange:s=>o(i=>({...i,duration:parseFloat(s.target.value)||3})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",value:n.delay,onChange:s=>o(i=>({...i,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Spread (degrees):"}),e.jsx("input",{type:"number",min:"0",max:"360",value:n.spread,onChange:s=>o(i=>({...i,spread:parseInt(s.target.value)||45})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Launch Angle (degrees):"}),e.jsx("input",{type:"number",min:"0",max:"360",value:n.angle,onChange:s=>o(i=>({...i,angle:parseInt(s.target.value)||90})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gravity:"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"2",value:n.gravity,onChange:s=>o(i=>({...i,gravity:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Velocity:"}),e.jsx("input",{type:"number",min:"1",max:"100",value:n.startVelocity,onChange:s=>o(i=>({...i,startVelocity:parseInt(s.target.value)||45})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Origin X (0-1):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"1",value:n.origin.x,onChange:s=>o(i=>({...i,origin:{...i.origin,x:parseFloat(s.target.value)||.5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Origin Y (0-1):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"1",value:n.origin.y,onChange:s=>o(i=>({...i,origin:{...i.origin,y:parseFloat(s.target.value)||.5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Particle Size:"}),e.jsx("input",{type:"number",step:"0.1",min:"0.1",max:"3",value:n.scalar,onChange:s=>o(i=>({...i,scalar:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Particle Shapes:"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-4",children:["square","circle","star","triangle","heart","plus","ribbon","squiggle","streamer"].map(s=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:n.shapes.includes(s),onChange:()=>l(s),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:s})]},s))}),e.jsx("p",{className:"text-xs text-gray-500",children:"Multiple shapes will be randomly mixed in the confetti effect. Includes basic shapes (square, circle, star) and custom shapes (triangle, heart, plus, ribbon, squiggle, streamer)."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Confetti Colors:"}),e.jsxs("div",{className:"space-y-2",children:[n.colors.map((s,i)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"color",value:s,onChange:M=>a(i,M.target.value),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),e.jsx("input",{type:"text",value:s,onChange:M=>a(i,M.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"#ff0000"}),n.colors.length>1&&e.jsx("button",{type:"button",onClick:()=>y(i),className:"px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none",children:"Remove"})]},i)),e.jsx("button",{type:"button",onClick:r,className:"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none",children:"+ Add Color"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(ee,{effectType:"confetti",config:n})}),d&&e.jsx(K,{code:d})]})}const Fe=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"}));function Ce(){const[n,o]=q.useState({objectIds:[],trigger:"click",effect:"drop",objectCount:10,objectShape:"circle",objectSize:20,gravity:1,restitution:.8,friction:.1,duration:5,delay:0,colors:["#ff0000","#00ff00","#0000ff","#ffff00","#ff00ff","#00ffff"],initialVelocity:{x:50,y:-50}}),[d,c]=q.useState(""),t=l=>{o(s=>({...s,objectIds:l}))};q.useEffect(()=>{if(n.objectIds.length>0){const l=ye(n);c(l)}else c("")},[n]);const a=()=>{o(l=>({...l,colors:[...l.colors,"#000000"]}))},r=(l,s)=>{o(i=>({...i,colors:i.colors.map((M,m)=>m===l?s:M)}))},y=l=>{o(s=>({...s,colors:s.colors.filter((i,M)=>M!==l)}))};return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Physics Effect Generator"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Create realistic physics-based animations using Matter.js. Objects will interact with gravity, bounce, and collide naturally."}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx(te,{label:"Storyline Object IDs",placeholder:"Enter object ID and press Enter",value:n.objectIds,onChange:t})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Trigger:"}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["click","hover","timeline"].map(l=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"trigger",value:l,checked:n.trigger===l,onChange:s=>o(i=>({...i,trigger:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:l})]},l))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Physics Effect:"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:["drop","bounce","explode","attract","repel"].map(l=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"effect",value:l,checked:n.effect===l,onChange:s=>o(i=>({...i,effect:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:l})]},l))}),e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Drop: Objects fall from above • Bounce: Objects bounce around • Explode: Objects burst outward • Attract: Objects pull toward center • Repel: Objects push away from center"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Object Count: ",n.objectCount]}),e.jsx("input",{type:"range",min:"1",max:"50",value:n.objectCount,onChange:l=>o(s=>({...s,objectCount:parseInt(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"1"}),e.jsx("span",{children:"50"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Object Shape:"}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["circle","rectangle","polygon"].map(l=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"objectShape",value:l,checked:n.objectShape===l,onChange:s=>o(i=>({...i,objectShape:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:l})]},l))})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Object Size: ",n.objectSize,"px"]}),e.jsx("input",{type:"range",min:"10",max:"100",value:n.objectSize,onChange:l=>o(s=>({...s,objectSize:parseInt(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"10px"}),e.jsx("span",{children:"100px"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Gravity: ",n.gravity]}),e.jsx("input",{type:"range",min:"0",max:"2",step:"0.1",value:n.gravity,onChange:l=>o(s=>({...s,gravity:parseFloat(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"0 (Float)"}),e.jsx("span",{children:"2 (Heavy)"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Bounciness: ",n.restitution]}),e.jsx("input",{type:"range",min:"0",max:"1",step:"0.1",value:n.restitution,onChange:l=>o(s=>({...s,restitution:parseFloat(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"0 (No bounce)"}),e.jsx("span",{children:"1 (Super bouncy)"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Duration: ",n.duration,"s"]}),e.jsx("input",{type:"range",min:"1",max:"30",value:n.duration,onChange:l=>o(s=>({...s,duration:parseInt(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"1s"}),e.jsx("span",{children:"30s"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Delay: ",n.delay,"s"]}),e.jsx("input",{type:"range",min:"0",max:"10",step:"0.1",value:n.delay,onChange:l=>o(s=>({...s,delay:parseFloat(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"0s"}),e.jsx("span",{children:"10s"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Friction: ",n.friction]}),e.jsx("input",{type:"range",min:"0",max:"1",step:"0.1",value:n.friction,onChange:l=>o(s=>({...s,friction:parseFloat(l.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"0 (Slippery)"}),e.jsx("span",{children:"1 (Sticky)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Object Colors:"}),e.jsxs("div",{className:"space-y-3",children:[n.colors.map((l,s)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"color",value:l,onChange:i=>r(s,i.target.value),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),e.jsx("input",{type:"text",value:l,onChange:i=>r(s,i.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"#000000"}),n.colors.length>1&&e.jsx("button",{type:"button",onClick:()=>y(s),className:"px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none",children:"Remove"})]},s)),e.jsx("button",{type:"button",onClick:a,className:"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none",children:"+ Add Color"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(ee,{effectType:"physics",config:{...n,physicsEffect:n.effect}})}),d&&e.jsx(K,{code:d})]})}const Ge=Object.freeze(Object.defineProperty({__proto__:null,default:Ce},Symbol.toStringTag,{value:"Module"}));export{K as C,ee as P,te as T,Se as a,Me as b,Oe as c,Ne as d,Ee as e,Pe as f,ke as g,Te as h,Ie as i,Le as j,De as k,Re as l,Xe as m,Ye as n,ze as o,Ae as p,Be as q,Fe as r,Ge as s};
