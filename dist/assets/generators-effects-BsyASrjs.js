import{r as l,j as e}from"./vendor-react-Ch3oStpB.js";import{i as x,T as c,P as u,C as m,j as g,k as p,l as b}from"./generators-3d-BehEtrUz.js";function f(){const[r,n]=l.useState({objectIds:[],trigger:"click",effect:"skew",duration:1,delay:0,easing:"ease",skewX:15,skewY:5,perspective:100,rotateX:15,rotateY:15,scaleX:1.2,scaleY:1.2,translateX:0,translateY:0,shapeType:"diamond",intensity:5}),[o,i]=l.useState(""),d=s=>{n(t=>({...t,objectIds:s}))};l.useEffect(()=>{if(r.objectIds.length>0){const s=x(r);i(s)}else i("")},[r]);const a=()=>{switch(r.effect){case"skew":return e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Skew X (degrees):"}),e.jsx("input",{type:"number",value:r.skewX,onChange:s=>n(t=>({...t,skewX:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-45",max:"45",step:"1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Skew Y (degrees):"}),e.jsx("input",{type:"number",value:r.skewY,onChange:s=>n(t=>({...t,skewY:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-45",max:"45",step:"1"})]})]});case"perspective":return e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Perspective (%):"}),e.jsx("input",{type:"number",value:r.perspective,onChange:s=>n(t=>({...t,perspective:parseFloat(s.target.value)||100})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"10",max:"200",step:"5"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotate X (degrees):"}),e.jsx("input",{type:"number",value:r.rotateX,onChange:s=>n(t=>({...t,rotateX:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-90",max:"90",step:"5"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotate Y (degrees):"}),e.jsx("input",{type:"number",value:r.rotateY,onChange:s=>n(t=>({...t,rotateY:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-90",max:"90",step:"5"})]})]});case"matrix":return e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scale X:"}),e.jsx("input",{type:"number",value:r.scaleX,onChange:s=>n(t=>({...t,scaleX:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"3",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scale Y:"}),e.jsx("input",{type:"number",value:r.scaleY,onChange:s=>n(t=>({...t,scaleY:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"3",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Translate X (%):"}),e.jsx("input",{type:"number",value:r.translateX,onChange:s=>n(t=>({...t,translateX:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-200",max:"200",step:"10"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Translate Y (%):"}),e.jsx("input",{type:"number",value:r.translateY,onChange:s=>n(t=>({...t,translateY:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"-200",max:"200",step:"10"})]})]});case"shape":return e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Shape Type:"}),e.jsxs("select",{value:r.shapeType,onChange:s=>n(t=>({...t,shapeType:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"diamond",children:"Diamond"}),e.jsx("option",{value:"parallelogram",children:"Parallelogram"}),e.jsx("option",{value:"trapezoid",children:"Trapezoid"}),e.jsx("option",{value:"custom",children:"Custom"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Intensity:"}),e.jsx("input",{type:"number",value:r.intensity,onChange:s=>n(t=>({...t,intensity:parseFloat(s.target.value)||5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"20",step:"1"})]})]});default:return null}};return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Morph Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:r.objectIds,onChange:d,placeholder:"e.g. card1, card2, buttonMain",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:s=>n(t=>({...t,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"Click"}),e.jsx("option",{value:"hover",children:"Hover"}),e.jsx("option",{value:"timeline",children:"Timeline"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Effect Type:"}),e.jsxs("select",{value:r.effect,onChange:s=>n(t=>({...t,effect:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"skew",children:"Skew"}),e.jsx("option",{value:"perspective",children:"Perspective"}),e.jsx("option",{value:"matrix",children:"Matrix"}),e.jsx("option",{value:"shape",children:"Shape"})]})]})]}),a(),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",value:r.duration,onChange:s=>n(t=>({...t,duration:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"5",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",value:r.delay,onChange:s=>n(t=>({...t,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"10",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:r.easing,onChange:s=>n(t=>({...t,easing:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(u,{effectType:"morph",config:r})}),o&&e.jsx(m,{code:o})]})}const C=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));function h(){const[r,n]=l.useState({objectIds:[],trigger:"click",effect:"digital",duration:1,delay:0,intensity:1,speed:5,iterations:3}),[o,i]=l.useState(""),d=a=>{n(s=>({...s,objectIds:a}))};return l.useEffect(()=>{if(r.objectIds.length>0){const a=g(r);i(a)}else i("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Glitch Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:r.objectIds,onChange:d,placeholder:"e.g. card1, card2, buttonMain",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:a=>n(s=>({...s,trigger:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"Click"}),e.jsx("option",{value:"hover",children:"Hover"}),e.jsx("option",{value:"timeline",children:"Timeline"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Effect Type:"}),e.jsxs("select",{value:r.effect,onChange:a=>n(s=>({...s,effect:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"digital",children:"Digital"}),e.jsx("option",{value:"rgb",children:"RGB Split"}),e.jsx("option",{value:"shake",children:"Shake"}),e.jsx("option",{value:"corrupt",children:"Corrupt"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",value:r.duration,onChange:a=>n(s=>({...s,duration:parseFloat(a.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"5",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",value:r.delay,onChange:a=>n(s=>({...s,delay:parseFloat(a.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"10",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Intensity:"}),e.jsx("input",{type:"number",value:r.intensity,onChange:a=>n(s=>({...s,intensity:parseFloat(a.target.value)||10})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"50",step:"1"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Speed:"}),e.jsx("input",{type:"number",value:r.speed,onChange:a=>n(s=>({...s,speed:parseFloat(a.target.value)||5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"20",step:"1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Iterations:"}),e.jsx("input",{type:"number",value:r.iterations,onChange:a=>n(s=>({...s,iterations:parseInt(a.target.value)||3})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"10",step:"1"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(u,{effectType:"glitch",config:r})}),o&&e.jsx(m,{code:o})]})}const k=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));function j(){const[r,n]=l.useState({objectIds:[],trigger:"click",effect:"glow",duration:2,delay:0,color:"#00ffff",intensity:2,blurRadius:1,spreadRadius:2}),[o,i]=l.useState(""),d=a=>{n(s=>({...s,objectIds:a}))};return l.useEffect(()=>{if(r.objectIds.length>0){const a=p(r);i(a)}else i("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Neon Glow Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:r.objectIds,onChange:d,placeholder:"e.g. card1, card2, buttonMain",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:a=>n(s=>({...s,trigger:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"Click"}),e.jsx("option",{value:"hover",children:"Hover"}),e.jsx("option",{value:"timeline",children:"Timeline"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Effect Type:"}),e.jsxs("select",{value:r.effect,onChange:a=>n(s=>({...s,effect:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"glow",children:"Static Glow"}),e.jsx("option",{value:"pulse",children:"Pulse"}),e.jsx("option",{value:"flicker",children:"Flicker"}),e.jsx("option",{value:"rainbow",children:"Rainbow"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",value:r.duration,onChange:a=>n(s=>({...s,duration:parseFloat(a.target.value)||2})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"10",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",value:r.delay,onChange:a=>n(s=>({...s,delay:parseFloat(a.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"10",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Neon Color:"}),e.jsx("input",{type:"color",value:r.color,onChange:a=>n(s=>({...s,color:a.target.value})),className:"w-full h-10 px-1 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Intensity:"}),e.jsx("input",{type:"number",value:r.intensity,onChange:a=>n(s=>({...s,intensity:parseFloat(a.target.value)||2})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.5",max:"5",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Blur Radius (%):"}),e.jsx("input",{type:"number",value:r.blurRadius,onChange:a=>n(s=>({...s,blurRadius:parseFloat(a.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0.1",max:"5",step:"0.1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Spread Radius (%):"}),e.jsx("input",{type:"number",value:r.spreadRadius,onChange:a=>n(s=>({...s,spreadRadius:parseFloat(a.target.value)||2})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"10",step:"0.1"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(u,{effectType:"neon",config:r})}),o&&e.jsx(m,{code:o})]})}const w=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));function v(){const[r,n]=l.useState({objectIds:[],strength:.3,distance:10,speed:.3,returnSpeed:.6,easing:"ease-out"}),[o,i]=l.useState(""),d=a=>{n(s=>({...s,objectIds:a}))};return l.useEffect(()=>{if(r.objectIds.length>0){const a=b(r);i(a)}else i("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Magnetic Hover Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(c,{label:"Object IDs",value:r.objectIds,onChange:d,placeholder:"Enter object IDs (e.g., button1, text2)..."})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Magnetic Strength:"}),e.jsx("input",{type:"range",min:"0.1",max:"1",step:"0.1",value:r.strength,onChange:a=>n(s=>({...s,strength:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Subtle (0.1)"}),e.jsx("span",{className:"font-medium",children:r.strength}),e.jsx("span",{children:"Strong (1.0)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Detection Distance (%):"}),e.jsx("input",{type:"range",min:"5",max:"30",step:"1",value:r.distance,onChange:a=>n(s=>({...s,distance:parseInt(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Close (5%)"}),e.jsxs("span",{className:"font-medium",children:[r.distance,"%"]}),e.jsx("span",{children:"Far (30%)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Response Speed:"}),e.jsx("input",{type:"range",min:"0.1",max:"1",step:"0.1",value:r.speed,onChange:a=>n(s=>({...s,speed:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Fast (0.1s)"}),e.jsxs("span",{className:"font-medium",children:[r.speed,"s"]}),e.jsx("span",{children:"Slow (1.0s)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Return Speed:"}),e.jsx("input",{type:"range",min:"0.1",max:"1.5",step:"0.1",value:r.returnSpeed,onChange:a=>n(s=>({...s,returnSpeed:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Fast (0.1s)"}),e.jsxs("span",{className:"font-medium",children:[r.returnSpeed,"s"]}),e.jsx("span",{children:"Slow (1.5s)"})]})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation Easing:"}),e.jsxs("select",{value:r.easing,onChange:a=>n(s=>({...s,easing:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(u,{effectType:"magnetic",config:r})}),o&&e.jsx(m,{code:o})]})}const S=Object.freeze(Object.defineProperty({__proto__:null,default:v},Symbol.toStringTag,{value:"Module"}));export{k as G,C as M,w as N,S as a};
