var Me={exports:{}},Ae={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Je;function cn(){return Je||(Je=1,function(e){function t(f,g){var y=f.length;f.push(g);e:for(;0<y;){var F=y-1>>>1,D=f[F];if(0<r(D,g))f[F]=g,f[y]=D,y=F;else break e}}function n(f){return f.length===0?null:f[0]}function s(f){if(f.length===0)return null;var g=f[0],y=f.pop();if(y!==g){f[0]=y;e:for(var F=0,D=f.length,oe=D>>>1;F<oe;){var le=2*(F+1)-1,we=f[le],L=le+1,ue=f[L];if(0>r(we,y))L<D&&0>r(ue,we)?(f[F]=ue,f[L]=y,F=L):(f[F]=we,f[le]=y,F=le);else if(L<D&&0>r(ue,y))f[F]=ue,f[L]=y,F=L;else break e}}return g}function r(f,g){var y=f.sortIndex-g.sortIndex;return y!==0?y:f.id-g.id}if(e.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,o=a.now();e.unstable_now=function(){return a.now()-o}}var l=[],u=[],h=1,c=null,d=3,v=!1,M=!1,b=!1,A=!1,k=typeof setTimeout=="function"?setTimeout:null,C=typeof clearTimeout=="function"?clearTimeout:null,S=typeof setImmediate<"u"?setImmediate:null;function x(f){for(var g=n(u);g!==null;){if(g.callback===null)s(u);else if(g.startTime<=f)s(u),g.sortIndex=g.expirationTime,t(l,g);else break;g=n(u)}}function p(f){if(b=!1,x(f),!M)if(n(l)!==null)M=!0,w||(w=!0,U());else{var g=n(u);g!==null&&ve(p,g.startTime-f)}}var w=!1,E=-1,T=5,R=-1;function K(){return A?!0:!(e.unstable_now()-R<T)}function ae(){if(A=!1,w){var f=e.unstable_now();R=f;var g=!0;try{e:{M=!1,b&&(b=!1,C(E),E=-1),v=!0;var y=d;try{t:{for(x(f),c=n(l);c!==null&&!(c.expirationTime>f&&K());){var F=c.callback;if(typeof F=="function"){c.callback=null,d=c.priorityLevel;var D=F(c.expirationTime<=f);if(f=e.unstable_now(),typeof D=="function"){c.callback=D,x(f),g=!0;break t}c===n(l)&&s(l),x(f)}else s(l);c=n(l)}if(c!==null)g=!0;else{var oe=n(u);oe!==null&&ve(p,oe.startTime-f),g=!1}}break e}finally{c=null,d=y,v=!1}g=void 0}}finally{g?U():w=!1}}}var U;if(typeof S=="function")U=function(){S(ae)};else if(typeof MessageChannel<"u"){var Qe=new MessageChannel,un=Qe.port2;Qe.port1.onmessage=ae,U=function(){un.postMessage(null)}}else U=function(){k(ae,0)};function ve(f,g){E=k(function(){f(e.unstable_now())},g)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(f){f.callback=null},e.unstable_forceFrameRate=function(f){0>f||125<f?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<f?Math.floor(1e3/f):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_next=function(f){switch(d){case 1:case 2:case 3:var g=3;break;default:g=d}var y=d;d=g;try{return f()}finally{d=y}},e.unstable_requestPaint=function(){A=!0},e.unstable_runWithPriority=function(f,g){switch(f){case 1:case 2:case 3:case 4:case 5:break;default:f=3}var y=d;d=f;try{return g()}finally{d=y}},e.unstable_scheduleCallback=function(f,g,y){var F=e.unstable_now();switch(typeof y=="object"&&y!==null?(y=y.delay,y=typeof y=="number"&&0<y?F+y:F):y=F,f){case 1:var D=-1;break;case 2:D=250;break;case 5:D=1073741823;break;case 4:D=1e4;break;default:D=5e3}return D=y+D,f={id:h++,callback:g,priorityLevel:f,startTime:y,expirationTime:D,sortIndex:-1},y>F?(f.sortIndex=y,t(u,f),n(l)===null&&f===n(u)&&(b?(C(E),E=-1):b=!0,ve(p,y-F))):(f.sortIndex=D,t(l,f),M||v||(M=!0,w||(w=!0,U()))),f},e.unstable_shouldYield=K,e.unstable_wrapCallback=function(f){var g=d;return function(){var y=d;d=g;try{return f.apply(this,arguments)}finally{d=y}}}}(Ae)),Ae}var et;function hr(){return et||(et=1,Me.exports=cn()),Me.exports}function fn(e,t){e.indexOf(t)===-1&&e.push(t)}function hn(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const G=(e,t,n)=>n>t?t:n<e?e:n;let Le=()=>{};const ee={},dn=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function At(e){return typeof e=="object"&&e!==null}const mn=e=>/^0[^.\s]+$/u.test(e);function Be(e){let t;return()=>(t===void 0&&(t=e()),t)}const z=e=>e,pn=(e,t)=>n=>t(e(n)),We=(...e)=>e.reduce(pn),St=(e,t,n)=>{const s=t-e;return s===0?1:(n-e)/s};class yn{constructor(){this.subscriptions=[]}add(t){return fn(this.subscriptions,t),()=>hn(this.subscriptions,t)}notify(t,n,s){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](t,n,s);else for(let i=0;i<r;i++){const a=this.subscriptions[i];a&&a(t,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const j=e=>e*1e3,_=e=>e/1e3;function xt(e,t){return t?e*(1e3/t):0}const kt=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,gn=1e-7,bn=12;function Tn(e,t,n,s,r){let i,a,o=0;do a=t+(n-t)/2,i=kt(a,s,r)-e,i>0?n=a:t=a;while(Math.abs(i)>gn&&++o<bn);return a}function se(e,t,n,s){if(e===t&&n===s)return z;const r=i=>Tn(i,0,1,e,n);return i=>i===0||i===1?i:kt(r(i),t,s)}const Et=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Pt=e=>t=>1-e(1-t),Ft=se(.33,1.53,.69,.99),Ye=Pt(Ft),Dt=Et(Ye),Vt=e=>(e*=2)<1?.5*Ye(e):.5*(2-Math.pow(2,-10*(e-1))),qe=e=>1-Math.sin(Math.acos(e)),vn=Pt(qe),Rt=Et(qe),wn=se(.42,0,1,1),Mn=se(0,0,.58,1),Ct=se(.42,0,.58,1),An=e=>Array.isArray(e)&&typeof e[0]!="number",Kt=e=>Array.isArray(e)&&typeof e[0]=="number",Sn={linear:z,easeIn:wn,easeInOut:Ct,easeOut:Mn,circIn:qe,circInOut:Rt,circOut:vn,backIn:Ye,backInOut:Dt,backOut:Ft,anticipate:Vt},xn=e=>typeof e=="string",tt=e=>{if(Kt(e)){Le(e.length===4);const[t,n,s,r]=e;return se(t,n,s,r)}else if(xn(e))return Sn[e];return e},ce=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],nt={value:null};function kn(e,t){let n=new Set,s=new Set,r=!1,i=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(c){a.has(c)&&(h.schedule(c),e()),l++,c(o)}const h={schedule:(c,d=!1,v=!1)=>{const b=v&&r?n:s;return d&&a.add(c),b.has(c)||b.add(c),c},cancel:c=>{s.delete(c),a.delete(c)},process:c=>{if(o=c,r){i=!0;return}r=!0,[n,s]=[s,n],n.forEach(u),t&&nt.value&&nt.value.frameloop[t].push(l),l=0,n.clear(),r=!1,i&&(i=!1,h.process(c))}};return h}const En=40;function Ot(e,t){let n=!1,s=!0;const r={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,a=ce.reduce((S,x)=>(S[x]=kn(i,t?x:void 0),S),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:v,postRender:M}=a,b=()=>{const S=ee.useManualTiming?r.timestamp:performance.now();n=!1,ee.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(S-r.timestamp,En),1)),r.timestamp=S,r.isProcessing=!0,o.process(r),l.process(r),u.process(r),h.process(r),c.process(r),d.process(r),v.process(r),M.process(r),r.isProcessing=!1,n&&t&&(s=!1,e(b))},A=()=>{n=!0,s=!0,r.isProcessing||e(b)};return{schedule:ce.reduce((S,x)=>{const p=a[x];return S[x]=(w,E=!1,T=!1)=>(n||A(),p.schedule(w,E,T)),S},{}),cancel:S=>{for(let x=0;x<ce.length;x++)a[ce[x]].cancel(S)},state:r,steps:a}}const{schedule:me,cancel:Pn,state:pe,steps:dr}=Ot(typeof requestAnimationFrame<"u"?requestAnimationFrame:z,!0);let he;function Fn(){he=void 0}const N={now:()=>(he===void 0&&N.set(pe.isProcessing||ee.useManualTiming?pe.timestamp:performance.now()),he),set:e=>{he=e,queueMicrotask(Fn)}},Nt=e=>t=>typeof t=="string"&&t.startsWith(e),mr=Nt("--"),Dn=Nt("var(--"),Ge=e=>Dn(e)?Vn.test(e.split("/*")[0].trim()):!1,Vn=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Z={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},te={...Z,transform:e=>G(0,1,e)},fe={...Z,default:1},Q=e=>Math.round(e*1e5)/1e5,Ue=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Rn(e){return e==null}const Cn=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Xe=(e,t)=>n=>!!(typeof n=="string"&&Cn.test(n)&&n.startsWith(e)||t&&!Rn(n)&&Object.prototype.hasOwnProperty.call(n,t)),_t=(e,t,n)=>s=>{if(typeof s!="string")return s;const[r,i,a,o]=s.match(Ue);return{[e]:parseFloat(r),[t]:parseFloat(i),[n]:parseFloat(a),alpha:o!==void 0?parseFloat(o):1}},Kn=e=>G(0,255,e),Se={...Z,transform:e=>Math.round(Kn(e))},B={test:Xe("rgb","red"),parse:_t("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:s=1})=>"rgba("+Se.transform(e)+", "+Se.transform(t)+", "+Se.transform(n)+", "+Q(te.transform(s))+")"};function On(e){let t="",n="",s="",r="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),s=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),s=e.substring(3,4),r=e.substring(4,5),t+=t,n+=n,s+=s,r+=r),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}}const Pe={test:Xe("#"),parse:On,transform:B.transform},re=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),I=re("deg"),$=re("%"),m=re("px"),Nn=re("vh"),_n=re("vw"),st={...$,parse:e=>$.parse(e)/100,transform:e=>$.transform(e*100)},X={test:Xe("hsl","hue"),parse:_t("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:s=1})=>"hsla("+Math.round(e)+", "+$.transform(Q(t))+", "+$.transform(Q(n))+", "+Q(te.transform(s))+")"},V={test:e=>B.test(e)||Pe.test(e)||X.test(e),parse:e=>B.test(e)?B.parse(e):X.test(e)?X.parse(e):Pe.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?B.transform(e):X.transform(e)},In=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Ln(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(Ue))==null?void 0:t.length)||0)+(((n=e.match(In))==null?void 0:n.length)||0)>0}const It="number",Lt="color",Bn="var",Wn="var(",rt="${}",Yn=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ne(e){const t=e.toString(),n=[],s={color:[],number:[],var:[]},r=[];let i=0;const o=t.replace(Yn,l=>(V.test(l)?(s.color.push(i),r.push(Lt),n.push(V.parse(l))):l.startsWith(Wn)?(s.var.push(i),r.push(Bn),n.push(l)):(s.number.push(i),r.push(It),n.push(parseFloat(l))),++i,rt)).split(rt);return{values:n,split:o,indexes:s,types:r}}function Bt(e){return ne(e).values}function Wt(e){const{split:t,types:n}=ne(e),s=t.length;return r=>{let i="";for(let a=0;a<s;a++)if(i+=t[a],r[a]!==void 0){const o=n[a];o===It?i+=Q(r[a]):o===Lt?i+=V.transform(r[a]):i+=r[a]}return i}}const qn=e=>typeof e=="number"?0:e;function Gn(e){const t=Bt(e);return Wt(e)(t.map(qn))}const ie={test:Ln,parse:Bt,createTransformer:Wt,getAnimatableNone:Gn};function xe(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Un({hue:e,saturation:t,lightness:n,alpha:s}){e/=360,t/=100,n/=100;let r=0,i=0,a=0;if(!t)r=i=a=n;else{const o=n<.5?n*(1+t):n+t-n*t,l=2*n-o;r=xe(l,o,e+1/3),i=xe(l,o,e),a=xe(l,o,e-1/3)}return{red:Math.round(r*255),green:Math.round(i*255),blue:Math.round(a*255),alpha:s}}function ye(e,t){return n=>n>0?t:e}const Te=(e,t,n)=>e+(t-e)*n,ke=(e,t,n)=>{const s=e*e,r=n*(t*t-s)+s;return r<0?0:Math.sqrt(r)},Xn=[Pe,B,X],$n=e=>Xn.find(t=>t.test(e));function it(e){const t=$n(e);if(!t)return!1;let n=t.parse(e);return t===X&&(n=Un(n)),n}const at=(e,t)=>{const n=it(e),s=it(t);if(!n||!s)return ye(e,t);const r={...n};return i=>(r.red=ke(n.red,s.red,i),r.green=ke(n.green,s.green,i),r.blue=ke(n.blue,s.blue,i),r.alpha=Te(n.alpha,s.alpha,i),B.transform(r))},Fe=new Set(["none","hidden"]);function zn(e,t){return Fe.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function jn(e,t){return n=>Te(e,t,n)}function $e(e){return typeof e=="number"?jn:typeof e=="string"?Ge(e)?ye:V.test(e)?at:Qn:Array.isArray(e)?Yt:typeof e=="object"?V.test(e)?at:Zn:ye}function Yt(e,t){const n=[...e],s=n.length,r=e.map((i,a)=>$e(i)(i,t[a]));return i=>{for(let a=0;a<s;a++)n[a]=r[a](i);return n}}function Zn(e,t){const n={...e,...t},s={};for(const r in n)e[r]!==void 0&&t[r]!==void 0&&(s[r]=$e(e[r])(e[r],t[r]));return r=>{for(const i in s)n[i]=s[i](r);return n}}function Hn(e,t){const n=[],s={color:0,var:0,number:0};for(let r=0;r<t.values.length;r++){const i=t.types[r],a=e.indexes[i][s[i]],o=e.values[a]??0;n[r]=o,s[i]++}return n}const Qn=(e,t)=>{const n=ie.createTransformer(t),s=ne(e),r=ne(t);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?Fe.has(e)&&!r.values.length||Fe.has(t)&&!s.values.length?zn(e,t):We(Yt(Hn(s,r),r.values),n):ye(e,t)};function qt(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?Te(e,t,n):$e(e)(e,t)}const Jn=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>me.update(t,n),stop:()=>Pn(t),now:()=>pe.isProcessing?pe.timestamp:N.now()}},Gt=(e,t,n=10)=>{let s="";const r=Math.max(Math.round(t/n),2);for(let i=0;i<r;i++)s+=e(i/(r-1))+", ";return`linear(${s.substring(0,s.length-2)})`},ge=2e4;function ze(e){let t=0;const n=50;let s=e.next(t);for(;!s.done&&t<ge;)t+=n,s=e.next(t);return t>=ge?1/0:t}function es(e,t=100,n){const s=n({...e,keyframes:[0,t]}),r=Math.min(ze(s),ge);return{type:"keyframes",ease:i=>s.next(r*i).value/t,duration:_(r)}}const ts=5;function Ut(e,t,n){const s=Math.max(t-ts,0);return xt(n-e(s),t-s)}const P={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ot=.001;function ns({duration:e=P.duration,bounce:t=P.bounce,velocity:n=P.velocity,mass:s=P.mass}){let r,i,a=1-t;a=G(P.minDamping,P.maxDamping,a),e=G(P.minDuration,P.maxDuration,_(e)),a<1?(r=u=>{const h=u*a,c=h*e,d=h-n,v=De(u,a),M=Math.exp(-c);return ot-d/v*M},i=u=>{const c=u*a*e,d=c*n+n,v=Math.pow(a,2)*Math.pow(u,2)*e,M=Math.exp(-c),b=De(Math.pow(u,2),a);return(-r(u)+ot>0?-1:1)*((d-v)*M)/b}):(r=u=>{const h=Math.exp(-u*e),c=(u-n)*e+1;return-.001+h*c},i=u=>{const h=Math.exp(-u*e),c=(n-u)*(e*e);return h*c});const o=5/e,l=rs(r,i,o);if(e=j(e),isNaN(l))return{stiffness:P.stiffness,damping:P.damping,duration:e};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:a*2*Math.sqrt(s*u),duration:e}}}const ss=12;function rs(e,t,n){let s=n;for(let r=1;r<ss;r++)s=s-e(s)/t(s);return s}function De(e,t){return e*Math.sqrt(1-t*t)}const is=["duration","bounce"],as=["stiffness","damping","mass"];function lt(e,t){return t.some(n=>e[n]!==void 0)}function os(e){let t={velocity:P.velocity,stiffness:P.stiffness,damping:P.damping,mass:P.mass,isResolvedFromDuration:!1,...e};if(!lt(e,as)&&lt(e,is))if(e.visualDuration){const n=e.visualDuration,s=2*Math.PI/(n*1.2),r=s*s,i=2*G(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:P.mass,stiffness:r,damping:i}}else{const n=ns(e);t={...t,...n,mass:P.mass},t.isResolvedFromDuration=!0}return t}function be(e=P.visualDuration,t=P.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:s,restDelta:r}=n;const i=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],o={done:!1,value:i},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:v}=os({...n,velocity:-_(n.velocity||0)}),M=d||0,b=u/(2*Math.sqrt(l*h)),A=a-i,k=_(Math.sqrt(l/h)),C=Math.abs(A)<5;s||(s=C?P.restSpeed.granular:P.restSpeed.default),r||(r=C?P.restDelta.granular:P.restDelta.default);let S;if(b<1){const p=De(k,b);S=w=>{const E=Math.exp(-b*k*w);return a-E*((M+b*k*A)/p*Math.sin(p*w)+A*Math.cos(p*w))}}else if(b===1)S=p=>a-Math.exp(-k*p)*(A+(M+k*A)*p);else{const p=k*Math.sqrt(b*b-1);S=w=>{const E=Math.exp(-b*k*w),T=Math.min(p*w,300);return a-E*((M+b*k*A)*Math.sinh(T)+p*A*Math.cosh(T))/p}}const x={calculatedDuration:v&&c||null,next:p=>{const w=S(p);if(v)o.done=p>=c;else{let E=p===0?M:0;b<1&&(E=p===0?j(M):Ut(S,p,w));const T=Math.abs(E)<=s,R=Math.abs(a-w)<=r;o.done=T&&R}return o.value=o.done?a:w,o},toString:()=>{const p=Math.min(ze(x),ge),w=Gt(E=>x.next(p*E).value,p,30);return p+"ms "+w},toTransition:()=>{}};return x}be.applyToOptions=e=>{const t=es(e,100,be);return e.ease=t.ease,e.duration=j(t.duration),e.type="keyframes",e};function Ve({keyframes:e,velocity:t=0,power:n=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:i=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){const c=e[0],d={done:!1,value:c},v=T=>o!==void 0&&T<o||l!==void 0&&T>l,M=T=>o===void 0?l:l===void 0||Math.abs(o-T)<Math.abs(l-T)?o:l;let b=n*t;const A=c+b,k=a===void 0?A:a(A);k!==A&&(b=k-c);const C=T=>-b*Math.exp(-T/s),S=T=>k+C(T),x=T=>{const R=C(T),K=S(T);d.done=Math.abs(R)<=u,d.value=d.done?k:K};let p,w;const E=T=>{v(d.value)&&(p=T,w=be({keyframes:[d.value,M(d.value)],velocity:Ut(S,T,d.value),damping:r,stiffness:i,restDelta:u,restSpeed:h}))};return E(0),{calculatedDuration:null,next:T=>{let R=!1;return!w&&p===void 0&&(R=!0,x(T),E(T)),p!==void 0&&T>=p?w.next(T-p):(!R&&x(T),d)}}}function ls(e,t,n){const s=[],r=n||ee.mix||qt,i=e.length-1;for(let a=0;a<i;a++){let o=r(e[a],e[a+1]);if(t){const l=Array.isArray(t)?t[a]||z:t;o=We(l,o)}s.push(o)}return s}function us(e,t,{clamp:n=!0,ease:s,mixer:r}={}){const i=e.length;if(Le(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const a=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=ls(t,s,r),l=o.length,u=h=>{if(a&&h<e[0])return t[0];let c=0;if(l>1)for(;c<e.length-2&&!(h<e[c+1]);c++);const d=St(e[c],e[c+1],h);return o[c](d)};return n?h=>u(G(e[0],e[i-1],h)):u}function cs(e,t){const n=e[e.length-1];for(let s=1;s<=t;s++){const r=St(0,t,s);e.push(Te(n,1,r))}}function fs(e){const t=[0];return cs(t,e.length-1),t}function hs(e,t){return e.map(n=>n*t)}function ds(e,t){return e.map(()=>t||Ct).splice(0,e.length-1)}function J({duration:e=300,keyframes:t,times:n,ease:s="easeInOut"}){const r=An(s)?s.map(tt):tt(s),i={done:!1,value:t[0]},a=hs(n&&n.length===t.length?n:fs(t),e),o=us(a,t,{ease:Array.isArray(r)?r:ds(t,r)});return{calculatedDuration:e,next:l=>(i.value=o(l),i.done=l>=e,i)}}const ms=e=>e!==null;function je(e,{repeat:t,repeatType:n="loop"},s,r=1){const i=e.filter(ms),o=r<0||t&&n!=="loop"&&t%2===1?0:i.length-1;return!o||s===void 0?i[o]:s}const ps={decay:Ve,inertia:Ve,tween:J,keyframes:J,spring:be};function Xt(e){typeof e.type=="string"&&(e.type=ps[e.type])}class Ze{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const ys=e=>e/100;class $t extends Ze{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,r;const{motionValue:n}=this.options;n&&n.updatedAt!==N.now()&&this.tick(N.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(r=(s=this.options).onStop)==null||r.call(s))},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;Xt(t);const{type:n=J,repeat:s=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=t;let{keyframes:o}=t;const l=n||J;l!==J&&typeof o[0]!="number"&&(this.mixKeyframes=We(ys,qt(o[0],o[1])),o=[0,100]);const u=l({...t,keyframes:o});i==="mirror"&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),u.calculatedDuration===null&&(u.calculatedDuration=ze(u));const{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+r,this.totalDuration=this.resolvedDuration*(s+1)-r,this.generator=u}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:s,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:v,type:M,onUpdate:b,finalKeyframe:A}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const k=this.currentTime-u*(this.playbackSpeed>=0?1:-1),C=this.playbackSpeed>=0?k<0:k>r;this.currentTime=Math.max(k,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=r);let S=this.currentTime,x=s;if(c){const T=Math.min(this.currentTime,r)/o;let R=Math.floor(T),K=T%1;!K&&T>=1&&(K=1),K===1&&R--,R=Math.min(R,c+1),!!(R%2)&&(d==="reverse"?(K=1-K,v&&(K-=v/o)):d==="mirror"&&(x=a)),S=G(0,1,K)*o}const p=C?{done:!1,value:h[0]}:x.next(S);i&&(p.value=i(p.value));let{done:w}=p;!C&&l!==null&&(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);const E=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return E&&M!==Ve&&(p.value=je(h,this.options,A,this.speed)),b&&b(p.value),E&&this.finish(),p}then(t,n){return this.finished.then(t,n)}get duration(){return _(this.calculatedDuration)}get time(){return _(this.currentTime)}set time(t){var n;t=j(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(N.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=_(this.currentTime))}play(){var r,i;if(this.isStopped)return;const{driver:t=Jn,startTime:n}=this.options;this.driver||(this.driver=t(a=>this.tick(a))),(i=(r=this.options).onPlay)==null||i.call(r);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(N.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(t=this.options).onComplete)==null||n.call(t)}cancel(){var t,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(t=this.options).onCancel)==null||n.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),t.observe(this)}}function gs(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const W=e=>e*180/Math.PI,Re=e=>{const t=W(Math.atan2(e[1],e[0]));return Ce(t)},bs={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Re,rotateZ:Re,skewX:e=>W(Math.atan(e[1])),skewY:e=>W(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Ce=e=>(e=e%360,e<0&&(e+=360),e),ut=Re,ct=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),ft=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Ts={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ct,scaleY:ft,scale:e=>(ct(e)+ft(e))/2,rotateX:e=>Ce(W(Math.atan2(e[6],e[5]))),rotateY:e=>Ce(W(Math.atan2(-e[2],e[0]))),rotateZ:ut,rotate:ut,skewX:e=>W(Math.atan(e[4])),skewY:e=>W(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function ht(e){return e.includes("scale")?1:0}function Ke(e,t){if(!e||e==="none")return ht(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,r;if(n)s=Ts,r=n;else{const o=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=bs,r=o}if(!r)return ht(t);const i=s[t],a=r[1].split(",").map(vs);return typeof i=="function"?i(a):a[i]}const pr=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Ke(n,t)};function vs(e){return parseFloat(e.trim())}const He=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],yr=new Set(He),dt=e=>e===Z||e===m,ws=new Set(["x","y","z"]),Ms=He.filter(e=>!ws.has(e));function As(e){const t=[];return Ms.forEach(n=>{const s=e.getValue(n);s!==void 0&&(t.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),t}const Y={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Ke(t,"x"),y:(e,{transform:t})=>Ke(t,"y")};Y.translateX=Y.x;Y.translateY=Y.y;const q=new Set;let Oe=!1,Ne=!1,_e=!1;function zt(){if(Ne){const e=Array.from(q).filter(s=>s.needsMeasurement),t=new Set(e.map(s=>s.element)),n=new Map;t.forEach(s=>{const r=As(s);r.length&&(n.set(s,r),s.render())}),e.forEach(s=>s.measureInitialState()),t.forEach(s=>{s.render();const r=n.get(s);r&&r.forEach(([i,a])=>{var o;(o=s.getValue(i))==null||o.set(a)})}),e.forEach(s=>s.measureEndState()),e.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Ne=!1,Oe=!1,q.forEach(e=>e.complete(_e)),q.clear()}function jt(){q.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Ne=!0)})}function Ss(){_e=!0,jt(),zt(),_e=!1}class Zt{constructor(t,n,s,r,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=s,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(q.add(this),Oe||(Oe=!0,me.read(jt),me.resolveKeyframes(zt))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:s,motionValue:r}=this;if(t[0]===null){const i=r==null?void 0:r.get(),a=t[t.length-1];if(i!==void 0)t[0]=i;else if(s&&n){const o=s.readValue(n,a);o!=null&&(t[0]=o)}t[0]===void 0&&(t[0]=a),r&&i===void 0&&r.set(t[0])}gs(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),q.delete(this)}cancel(){this.state==="scheduled"&&(q.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const xs=e=>e.startsWith("--");function ks(e,t,n){xs(t)?e.style.setProperty(t,n):e.style[t]=n}const Es=Be(()=>window.ScrollTimeline!==void 0),Ps={};function Fs(e,t){const n=Be(e);return()=>Ps[t]??n()}const Ht=Fs(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),H=([e,t,n,s])=>`cubic-bezier(${e}, ${t}, ${n}, ${s})`,mt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:H([0,.65,.55,1]),circOut:H([.55,0,1,.45]),backIn:H([.31,.01,.66,-.59]),backOut:H([.33,1.53,.69,.99])};function Qt(e,t){if(e)return typeof e=="function"?Ht()?Gt(e,t):"ease-out":Kt(e)?H(e):Array.isArray(e)?e.map(n=>Qt(n,t)||mt.easeOut):mt[e]}function Ds(e,t,n,{delay:s=0,duration:r=300,repeat:i=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u=void 0){const h={[t]:n};l&&(h.offset=l);const c=Qt(o,r);Array.isArray(c)&&(h.easing=c);const d={delay:s,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:a==="reverse"?"alternate":"normal"};return u&&(d.pseudoElement=u),e.animate(h,d)}function Jt(e){return typeof e=="function"&&"applyToOptions"in e}function Vs({type:e,...t}){return Jt(e)&&Ht()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Rs extends Ze{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:s,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=t,Le(typeof t.type!="string");const u=Vs(t);this.animation=Ds(n,s,r,u,i),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const h=je(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(h):ks(n,s,h),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,n;(n=(t=this.animation).finish)==null||n.call(t)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,n;this.isPseudoElement||(n=(t=this.animation).commitStyles)==null||n.call(t)}get duration(){var n,s;const t=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return _(Number(t))}get time(){return _(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=j(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&Es()?(this.animation.timeline=t,z):n(this)}}const en={anticipate:Vt,backInOut:Dt,circInOut:Rt};function Cs(e){return e in en}function Ks(e){typeof e.ease=="string"&&Cs(e.ease)&&(e.ease=en[e.ease])}const pt=10;class Os extends Rs{constructor(t){Ks(t),Xt(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:s,onComplete:r,element:i,...a}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const o=new $t({...a,autoplay:!1}),l=j(this.finishedTime??this.time);n.setWithVelocity(o.sample(l-pt).value,o.sample(l).value,pt),o.stop()}}const yt=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(ie.test(e)||e==="0")&&!e.startsWith("url("));function Ns(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function _s(e,t,n,s){const r=e[0];if(r===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],a=yt(r,t),o=yt(i,t);return!a||!o?!1:Ns(e)||(n==="spring"||Jt(n))&&s}function tn(e){return At(e)&&"offsetHeight"in e}const Is=new Set(["opacity","clipPath","filter","transform"]),Ls=Be(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Bs(e){var u;const{motionValue:t,name:n,repeatDelay:s,repeatType:r,damping:i,type:a}=e;if(!tn((u=t==null?void 0:t.owner)==null?void 0:u.current))return!1;const{onUpdate:o,transformTemplate:l}=t.owner.getProps();return Ls()&&n&&Is.has(n)&&(n!=="transform"||!l)&&!o&&!s&&r!=="mirror"&&i!==0&&a!=="inertia"}const Ws=40;class gr extends Ze{constructor({autoplay:t=!0,delay:n=0,type:s="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",keyframes:o,name:l,motionValue:u,element:h,...c}){var M;super(),this.stop=()=>{var b,A;this._animation&&(this._animation.stop(),(b=this.stopTimeline)==null||b.call(this)),(A=this.keyframeResolver)==null||A.cancel()},this.createdAt=N.now();const d={autoplay:t,delay:n,type:s,repeat:r,repeatDelay:i,repeatType:a,name:l,motionValue:u,element:h,...c},v=(h==null?void 0:h.KeyframeResolver)||Zt;this.keyframeResolver=new v(o,(b,A,k)=>this.onKeyframesResolved(b,A,d,!k),l,u,h),(M=this.keyframeResolver)==null||M.scheduleResolve()}onKeyframesResolved(t,n,s,r){this.keyframeResolver=void 0;const{name:i,type:a,velocity:o,delay:l,isHandoff:u,onUpdate:h}=s;this.resolvedAt=N.now(),_s(t,i,a,o)||((ee.instantAnimations||!l)&&(h==null||h(je(t,s,n))),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const d={startTime:r?this.resolvedAt?this.resolvedAt-this.createdAt>Ws?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:t},v=!u&&Bs(d)?new Os({...d,element:d.motionValue.owner.current}):new $t(d);v.finished.then(()=>this.notifyFinished()).catch(z),this.pendingTimeline&&(this.stopTimeline=v.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=v}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||((t=this.keyframeResolver)==null||t.resume(),Ss()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),(t=this.keyframeResolver)==null||t.cancel()}}const Ys=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function qs(e){const t=Ys.exec(e);if(!t)return[,];const[,n,s,r]=t;return[`--${n??s}`,r]}function nn(e,t,n=1){const[s,r]=qs(e);if(!s)return;const i=window.getComputedStyle(t).getPropertyValue(s);if(i){const a=i.trim();return dn(a)?parseFloat(a):a}return Ge(r)?nn(r,t,n+1):r}function br(e,t){return(e==null?void 0:e[t])??(e==null?void 0:e.default)??e}const Gs=new Set(["width","height","top","left","right","bottom",...He]),Us={test:e=>e==="auto",parse:e=>e},sn=e=>t=>t.test(e),rn=[Z,m,$,I,_n,Nn,Us],gt=e=>rn.find(sn(e));function Xs(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||mn(e):!0}const $s=new Set(["brightness","contrast","saturate","opacity"]);function zs(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[s]=n.match(Ue)||[];if(!s)return e;const r=n.replace(s,"");let i=$s.has(t)?1:0;return s!==n&&(i*=100),t+"("+i+r+")"}const js=/\b([a-z-]*)\(.*?\)/gu,Ie={...ie,getAnimatableNone:e=>{const t=e.match(js);return t?t.map(zs).join(" "):e}},bt={...Z,transform:Math.round},Zs={rotate:I,rotateX:I,rotateY:I,rotateZ:I,scale:fe,scaleX:fe,scaleY:fe,scaleZ:fe,skew:I,skewX:I,skewY:I,distance:m,translateX:m,translateY:m,translateZ:m,x:m,y:m,z:m,perspective:m,transformPerspective:m,opacity:te,originX:st,originY:st,originZ:m},Hs={borderWidth:m,borderTopWidth:m,borderRightWidth:m,borderBottomWidth:m,borderLeftWidth:m,borderRadius:m,radius:m,borderTopLeftRadius:m,borderTopRightRadius:m,borderBottomRightRadius:m,borderBottomLeftRadius:m,width:m,maxWidth:m,height:m,maxHeight:m,top:m,right:m,bottom:m,left:m,padding:m,paddingTop:m,paddingRight:m,paddingBottom:m,paddingLeft:m,margin:m,marginTop:m,marginRight:m,marginBottom:m,marginLeft:m,backgroundPositionX:m,backgroundPositionY:m,...Zs,zIndex:bt,fillOpacity:te,strokeOpacity:te,numOctaves:bt},Qs={...Hs,color:V,backgroundColor:V,outlineColor:V,fill:V,stroke:V,borderColor:V,borderTopColor:V,borderRightColor:V,borderBottomColor:V,borderLeftColor:V,filter:Ie,WebkitFilter:Ie},Js=e=>Qs[e];function er(e,t){let n=Js(e);return n!==Ie&&(n=ie),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const tr=new Set(["auto","none","0"]);function nr(e,t,n){let s=0,r;for(;s<e.length&&!r;){const i=e[s];typeof i=="string"&&!tr.has(i)&&ne(i).values.length&&(r=e[s]),s++}if(r&&n)for(const i of t)e[i]=er(n,r)}class Tr extends Zt{constructor(t,n,s,r,i){super(t,n,s,r,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Ge(u))){const h=nn(u,n.current);h!==void 0&&(t[l]=h),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Gs.has(s)||t.length!==2)return;const[r,i]=t,a=gt(r),o=gt(i);if(a!==o)if(dt(a)&&dt(o))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else Y[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,s=[];for(let r=0;r<t.length;r++)(t[r]===null||Xs(t[r]))&&s.push(r);s.length&&nr(t,s,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:s}=this;if(!t||!t.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Y[s](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const r=n[n.length-1];r!==void 0&&t.getValue(s,r).jump(r,!1)}measureEndState(){var o;const{element:t,name:n,unresolvedKeyframes:s}=this;if(!t||!t.current)return;const r=t.getValue(n);r&&r.jump(this.measuredOrigin,!1);const i=s.length-1,a=s[i];s[i]=Y[n](t.measureViewportBox(),window.getComputedStyle(t.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),(o=this.removedTransforms)!=null&&o.length&&this.removedTransforms.forEach(([l,u])=>{t.getValue(l).set(u)}),this.resolveNoneKeyframes()}}function sr(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let s=document;const r=(n==null?void 0:n[e])??s.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}const vr=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Tt=30,rr=e=>!isNaN(parseFloat(e));class ir{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,r=!0)=>{var a,o;const i=N.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((a=this.events.change)==null||a.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();r&&((o=this.events.renderRequest)==null||o.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=N.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=rr(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new yn);const s=this.events[t].add(n);return t==="change"?()=>{s(),me.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;(t=this.events.change)==null||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=N.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Tt)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Tt);return xt(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,n;(t=this.dependents)==null||t.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function wr(e,t){return new ir(e,t)}const{schedule:Mr}=Ot(queueMicrotask,!1),O={x:!1,y:!1};function an(){return O.x||O.y}function Ar(e){return e==="x"||e==="y"?O[e]?null:(O[e]=!0,()=>{O[e]=!1}):O.x||O.y?null:(O.x=O.y=!0,()=>{O.x=O.y=!1})}function on(e,t){const n=sr(e),s=new AbortController,r={passive:!0,...t,signal:s.signal};return[n,r,()=>s.abort()]}function vt(e){return!(e.pointerType==="touch"||an())}function Sr(e,t,n={}){const[s,r,i]=on(e,n),a=o=>{if(!vt(o))return;const{target:l}=o,u=t(l,o);if(typeof u!="function"||!l)return;const h=c=>{vt(c)&&(u(c),l.removeEventListener("pointerleave",h))};l.addEventListener("pointerleave",h,r)};return s.forEach(o=>{o.addEventListener("pointerenter",a,r)}),i}const ln=(e,t)=>t?e===t?!0:ln(e,t.parentElement):!1,ar=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,or=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function lr(e){return or.has(e.tagName)||e.tabIndex!==-1}const de=new WeakSet;function wt(e){return t=>{t.key==="Enter"&&e(t)}}function Ee(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const ur=(e,t)=>{const n=e.currentTarget;if(!n)return;const s=wt(()=>{if(de.has(n))return;Ee(n,"down");const r=wt(()=>{Ee(n,"up")}),i=()=>Ee(n,"cancel");n.addEventListener("keyup",r,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",s,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),t)};function Mt(e){return ar(e)&&!an()}function xr(e,t,n={}){const[s,r,i]=on(e,n),a=o=>{const l=o.currentTarget;if(!Mt(o))return;de.add(l);const u=t(l,o),h=(v,M)=>{window.removeEventListener("pointerup",c),window.removeEventListener("pointercancel",d),de.has(l)&&de.delete(l),Mt(v)&&typeof u=="function"&&u(v,{success:M})},c=v=>{h(v,l===window||l===document||n.useGlobalTarget||ln(l,v.target))},d=v=>{h(v,!1)};window.addEventListener("pointerup",c,r),window.addEventListener("pointercancel",d,r)};return s.forEach(o=>{(n.useGlobalTarget?window:o).addEventListener("pointerdown",a,r),tn(o)&&(o.addEventListener("focus",u=>ur(u,r)),!lr(o)&&!o.hasAttribute("tabindex")&&(o.tabIndex=0))}),i}function cr(e){return At(e)&&"ownerSVGElement"in e}function kr(e){return cr(e)&&e.tagName==="svg"}const Er=e=>!!(e&&e.getVelocity),fr=[...rn,V,ie],Pr=e=>fr.find(sn(e));export{gr as A,fn as B,hn as C,N as D,vn as E,cr as F,kr as G,dr as H,Sr as I,$t as J,xr as K,Zt as L,ee as M,dn as N,mn as O,Pr as P,er as Q,Tr as R,yn as S,ht as T,pr as U,Js as V,hr as W,mr as a,He as b,Er as c,wr as d,br as e,me as f,vr as g,Gs as h,tn as i,ar as j,Te as k,pe as l,Mr as m,Hs as n,We as o,m as p,Pn as q,_ as r,j as s,yr as t,St as u,G as v,Ar as w,$ as x,z as y,ie as z};
