import{r as i,j as e}from"./vendor-react-Ch3oStpB.js";import{m as d,T as x,P as m,C as u}from"./generators-3d-BehEtrUz.js";import"./vendor-BXDNmyco.js";import"./vendor-animation-BKCFbksN.js";function b(){const[l,t]=i.useState({objectIds:[],trigger:"click",effect:"material",size:"auto",color:"#3b82f6",opacity:.25,duration:.8,delay:0,origin:"click",customOrigin:{x:50,y:50},allowMultiple:!0,easing:"power2.out"}),[n,c]=i.useState(""),o=a=>{t(s=>({...s,objectIds:a}))};return i.useEffect(()=>{if(l.objectIds.length>0){const a=d(l);c(a)}else c("")},[l]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Ripple Effect Generator"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Create beautiful ripple effects inspired by Material Design. Perfect for buttons, cards, and interactive elements that need visual feedback."}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx(x,{label:"Storyline Object IDs",placeholder:"Enter object ID and press Enter",value:l.objectIds,onChange:o})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Trigger:"}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["click","hover","timeline"].map(a=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"trigger",value:a,checked:l.trigger===a,onChange:s=>t(r=>({...r,trigger:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:a})]},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Ripple Effect:"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:["material","water","pulse","shockwave"].map(a=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"effect",value:a,checked:l.effect===a,onChange:s=>t(r=>({...r,effect:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:a})]},a))}),e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Material: Classic Material Design • Water: Multiple expanding waves • Pulse: Breathing effect • Shockwave: Explosive ring with glow"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Ripple Size:"}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["small","medium","large","auto"].map(a=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"size",value:a,checked:l.size===a,onChange:s=>t(r=>({...r,size:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:a})]},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Ripple Origin:"}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["center","click","custom"].map(a=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"origin",value:a,checked:l.origin===a,onChange:s=>t(r=>({...r,origin:s.target.value})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700 capitalize",children:a})]},a))})]})]}),l.origin==="custom"&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["X Position: ",l.customOrigin.x,"%"]}),e.jsx("input",{type:"range",min:"0",max:"100",value:l.customOrigin.x,onChange:a=>t(s=>({...s,customOrigin:{...s.customOrigin,x:parseInt(a.target.value)}})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Left (0%)"}),e.jsx("span",{children:"Right (100%)"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Y Position: ",l.customOrigin.y,"%"]}),e.jsx("input",{type:"range",min:"0",max:"100",value:l.customOrigin.y,onChange:a=>t(s=>({...s,customOrigin:{...s.customOrigin,y:parseInt(a.target.value)}})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Top (0%)"}),e.jsx("span",{children:"Bottom (100%)"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ripple Color:"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"color",value:l.color,onChange:a=>t(s=>({...s,color:a.target.value})),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),e.jsx("input",{type:"text",value:l.color,onChange:a=>t(s=>({...s,color:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"#2563eb"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Opacity: ",l.opacity]}),e.jsx("input",{type:"range",min:"0.1",max:"1",step:"0.1",value:l.opacity,onChange:a=>t(s=>({...s,opacity:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Subtle (0.1)"}),e.jsx("span",{children:"Opaque (1.0)"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Duration: ",l.duration,"s"]}),e.jsx("input",{type:"range",min:"0.3",max:"3",step:"0.1",value:l.duration,onChange:a=>t(s=>({...s,duration:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"Fast (0.3s)"}),e.jsx("span",{children:"Slow (3s)"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Delay: ",l.delay,"s"]}),e.jsx("input",{type:"range",min:"0",max:"2",step:"0.1",value:l.delay,onChange:a=>t(s=>({...s,delay:parseFloat(a.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[e.jsx("span",{children:"None (0s)"}),e.jsx("span",{children:"Long (2s)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Animation Easing:"}),e.jsxs("select",{value:l.easing,onChange:a=>t(s=>({...s,easing:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"power2.out",children:"Natural (Recommended)"}),e.jsx("option",{value:"power3.out",children:"Smooth"}),e.jsx("option",{value:"back.out(1.7)",children:"Bouncy"}),e.jsx("option",{value:"elastic.out(1, 0.3)",children:"Elastic"}),e.jsx("option",{value:"ease-out",children:"Standard"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:l.allowMultiple,onChange:a=>t(s=>({...s,allowMultiple:a.target.checked})),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Allow Multiple Ripples"})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1 ml-6",children:"When enabled, multiple ripples can appear simultaneously. When disabled, new ripples will replace existing ones."})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(m,{effectType:"ripple",config:{...l,rippleEffect:l.effect,rippleOrigin:l.origin}})}),n&&e.jsx(u,{code:n})]})}export{b as default};
