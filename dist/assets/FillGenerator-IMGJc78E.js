import{r as l,j as e}from"./vendor-react-Ch3oStpB.js";import{o as c,T as d,P as u,C as g}from"./generators-3d-BehEtrUz.js";import"./vendor-BXDNmyco.js";import"./vendor-animation-BKCFbksN.js";function b(){const[s,t]=l.useState({objectIds:[],trigger:"timeline",origin:"bottom-right",direction:"reveal",duration:1,delay:0,easing:"power2.out",stagger:.1,color:"#3b82f6",shape:"rectangle",borderRadius:12}),[n,a]=l.useState(""),i=r=>{t(o=>({...o,objectIds:r}))};return l.useEffect(()=>{if(s.objectIds.length>0){const r=c(s);a(r)}else a("")},[s]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Fill Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{value:s.objectIds,onChange:i,placeholder:"e.g. content1, text2, image3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:s.trigger,onChange:r=>t(o=>({...o,trigger:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Origin Point:"}),e.jsxs("select",{value:s.origin,onChange:r=>t(o=>({...o,origin:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"bottom-right",children:"Bottom Right"}),e.jsx("option",{value:"bottom-left",children:"Bottom Left"}),e.jsx("option",{value:"top-right",children:"Top Right"}),e.jsx("option",{value:"top-left",children:"Top Left"}),e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"right",children:"Right"}),e.jsx("option",{value:"top",children:"Top"}),e.jsx("option",{value:"bottom",children:"Bottom"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Direction:"}),e.jsxs("select",{value:s.direction,onChange:r=>t(o=>({...o,direction:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"reveal",children:"Reveal"}),e.jsx("option",{value:"conceal",children:"Conceal"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Shape:"}),e.jsxs("select",{value:s.shape,onChange:r=>t(o=>({...o,shape:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"rectangle",children:"Rectangle"}),e.jsx("option",{value:"circle",children:"Circle"}),e.jsx("option",{value:"rounded-rectangle",children:"Rounded Rectangle"}),e.jsx("option",{value:"ellipse",children:"Ellipse"})]})]})]}),s.shape==="rounded-rectangle"&&e.jsxs("div",{className:"mt-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Border Radius (px):"}),e.jsx("input",{type:"number",min:"0",max:"50",step:"1",value:s.borderRadius,onChange:r=>t(o=>({...o,borderRadius:parseInt(r.target.value)||12})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Match this value to your object's corner radius in Storyline"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0.1",max:"10",value:s.duration,onChange:r=>t(o=>({...o,duration:parseFloat(r.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:s.delay,onChange:r=>t(o=>({...o,delay:parseFloat(r.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:s.easing,onChange:r=>t(o=>({...o,easing:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"power2.out",children:"Power2 Out"}),e.jsx("option",{value:"power3.out",children:"Power3 Out"}),e.jsx("option",{value:"back.out(1.7)",children:"Back Out"}),e.jsx("option",{value:"elastic.out(1, 0.3)",children:"Elastic Out"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stagger (seconds):"}),e.jsx("input",{type:"number",step:"0.05",min:"0",max:"2",value:s.stagger,onChange:r=>t(o=>({...o,stagger:parseFloat(r.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Time between each element (for multiple objects)"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Fill Color:"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"color",value:s.color,onChange:r=>t(o=>({...o,color:r.target.value})),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),e.jsx("input",{type:"text",value:s.color,onChange:r=>t(o=>({...o,color:r.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"#3b82f6"})]})]})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-md",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Effect Preview"}),e.jsxs("p",{className:"text-sm text-blue-700",children:[e.jsx("strong",{children:"Origin:"})," ",s.origin.replace("-"," ")," |"," ",e.jsx("strong",{children:"Direction:"})," ",s.direction," |"," ",e.jsx("strong",{children:"Duration:"})," ",s.duration,"s |"," ",e.jsx("strong",{children:"Trigger:"})," ",s.trigger," |"," ",e.jsx("strong",{children:"Shape:"})," ",s.shape.replace("-"," ")]}),e.jsxs("div",{className:"flex items-center gap-2 mt-2",children:[e.jsx("strong",{className:"text-sm text-blue-900",children:"Fill Color:"}),e.jsx("div",{className:"w-4 h-4 rounded border border-gray-300",style:{backgroundColor:s.color}}),e.jsx("span",{className:"text-sm text-blue-700",children:s.color})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(u,{effectType:"fill",config:s})}),n&&e.jsx(g,{code:n})]})}export{b as default};
