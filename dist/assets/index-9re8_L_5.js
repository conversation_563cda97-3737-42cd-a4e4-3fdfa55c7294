const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/generators-advanced-CbhKRPKZ.js","assets/vendor-react-Ch3oStpB.js","assets/vendor-BXDNmyco.js","assets/generators-3d-BehEtrUz.js","assets/vendor-animation-BKCFbksN.js","assets/generators-basic-CnfFKxYV.js","assets/generators-effects-BsyASrjs.js","assets/RippleGenerator-BsArBt1w.js","assets/TypewriterGenerator-DrsWyP_1.js","assets/FillGenerator-IMGJc78E.js"])))=>i.map(i=>d[i]);
import{r as i,j as e,c as j}from"./vendor-react-Ch3oStpB.js";import{m as u,A as y}from"./vendor-animation-BKCFbksN.js";import"./vendor-BXDNmyco.js";(function(){const m=document.createElement("link").relList;if(m&&m.supports&&m.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))x(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&x(l)}).observe(document,{childList:!0,subtree:!0});function o(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function x(n){if(n.ep)return;n.ep=!0;const r=o(n);fetch(n.href,r)}})();const E="modulepreload",w=function(t){return"/staging/TAM_Development/tam_storyline_tool/dist/"+t},_={},s=function(m,o,x){let n=Promise.resolve();if(o&&o.length>0){let l=function(c){return Promise.all(c.map(p=>Promise.resolve(p).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),d=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));n=l(o.map(c=>{if(c=w(c),c in _)return;_[c]=!0;const p=c.endsWith(".css"),f=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${f}`))return;const h=document.createElement("link");if(h.rel=p?"stylesheet":E,p||(h.as="script"),h.crossOrigin="",h.href=c,d&&h.setAttribute("nonce",d),document.head.appendChild(h),p)return new Promise((v,b)=>{h.addEventListener("load",v),h.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${c}`)))})}))}function r(l){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l}return n.then(l=>{for(const a of l||[])a.status==="rejected"&&r(a.reason);return m().catch(r)})},g=[{name:"Basic",effects:[{id:"fade",name:"Fade"},{id:"slide",name:"Slide"},{id:"bounce",name:"Bounce"},{id:"zoom",name:"Zoom"},{id:"fill",name:"Fill"}]},{name:"Motion",effects:[{id:"pulse",name:"Pulse"},{id:"shake",name:"Shake"},{id:"tilt",name:"Tilt"},{id:"rotate",name:"Rotate"}]},{name:"Advanced",effects:[{id:"morph",name:"Morph"},{id:"glitch",name:"Glitch"},{id:"neon",name:"Neon"},{id:"magnetic",name:"Magnetic"},{id:"transform3d",name:"3D Transform"}]},{name:"Interactive",effects:[{id:"pathdraw",name:"Path Draw"},{id:"confetti",name:"Confetti"},{id:"physics",name:"Physics"},{id:"ripple",name:"Ripple"},{id:"typewriter",name:"Typewriter"}]}];function P({activeGenerator:t,onGeneratorChange:m}){const[o,x]=i.useState(!1),[n,r]=i.useState(!1),l={expanded:{opacity:1,transition:{delay:.1,duration:.2}},collapsed:{opacity:0,transition:{duration:.1}}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:e.jsx("button",{onClick:()=>r(!n),className:"p-3 bg-white rounded-lg shadow-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Toggle menu",children:e.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:n?"M6 18L18 6M6 6l12 12":"M4 6h16M4 12h16M4 18h16"})})})}),n&&e.jsx("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>r(!1)}),e.jsxs(u.div,{className:`bg-white border-r border-gray-200 shadow-lg flex flex-col h-full z-40 lg:relative lg:translate-x-0 fixed inset-y-0 left-0 transition-transform duration-300 ease-in-out w-[280px] lg:w-auto ${n?"translate-x-0":"-translate-x-full lg:translate-x-0"}`,variants:{expanded:{width:"280px",transition:{duration:.3,ease:"easeInOut"}},collapsed:{width:"60px",transition:{duration:.3,ease:"easeInOut"}}},animate:o?"collapsed":"expanded",initial:"expanded",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200 flex items-center justify-between",children:[e.jsx(y,{children:!o&&e.jsx(u.h2,{className:"text-lg font-semibold text-gray-900",variants:l,initial:"expanded",animate:"expanded",exit:"collapsed",children:"Effects"})}),e.jsx("button",{onClick:()=>x(!o),className:"hidden lg:block p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":o?"Expand sidebar":"Collapse sidebar",children:e.jsx(u.div,{animate:{rotate:o?180:0},transition:{duration:.3},children:e.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto",children:[e.jsx(y,{children:!o&&e.jsx(u.div,{className:"p-4 space-y-6",variants:l,initial:"expanded",animate:"expanded",exit:"collapsed",children:g.map(a=>e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500 uppercase tracking-wider mb-3",children:a.name}),e.jsx("div",{className:"space-y-1",children:a.effects.map(d=>e.jsx("button",{onClick:()=>{m(d.id),r(!1)},className:`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${t===d.id?"bg-blue-500 text-white shadow-md":"text-gray-700 hover:bg-gray-100"}`,children:d.name},d.id))})]},a.name))})}),o&&e.jsx("div",{className:"hidden lg:block p-2",children:g.map(a=>a.effects.map(d=>e.jsx("button",{onClick:()=>{m(d.id),r(!1)},className:`w-full mb-1 px-2 py-2 rounded-md text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${t===d.id?"bg-blue-500 text-white":"text-gray-400 hover:bg-gray-100 hover:text-gray-700"}`,title:d.name,children:d.name.charAt(0).toUpperCase()},d.id)))})]})]})]})}const L=i.lazy(()=>s(()=>import("./generators-advanced-CbhKRPKZ.js").then(t=>t.T),__vite__mapDeps([0,1,2,3,4]))),N=i.lazy(()=>s(()=>import("./generators-advanced-CbhKRPKZ.js").then(t=>t.R),__vite__mapDeps([0,1,2,3,4]))),A=i.lazy(()=>s(()=>import("./generators-basic-CnfFKxYV.js").then(t=>t.F),__vite__mapDeps([5,1,2,3,4]))),T=i.lazy(()=>s(()=>import("./generators-basic-CnfFKxYV.js").then(t=>t.S),__vite__mapDeps([5,1,2,3,4]))),O=i.lazy(()=>s(()=>import("./generators-advanced-CbhKRPKZ.js").then(t=>t.P),__vite__mapDeps([0,1,2,3,4]))),I=i.lazy(()=>s(()=>import("./generators-advanced-CbhKRPKZ.js").then(t=>t.S),__vite__mapDeps([0,1,2,3,4]))),R=i.lazy(()=>s(()=>import("./generators-basic-CnfFKxYV.js").then(t=>t.B),__vite__mapDeps([5,1,2,3,4]))),G=i.lazy(()=>s(()=>import("./generators-basic-CnfFKxYV.js").then(t=>t.Z),__vite__mapDeps([5,1,2,3,4]))),V=i.lazy(()=>s(()=>import("./generators-effects-BsyASrjs.js").then(t=>t.M),__vite__mapDeps([6,1,2,3,4]))),z=i.lazy(()=>s(()=>import("./generators-effects-BsyASrjs.js").then(t=>t.G),__vite__mapDeps([6,1,2,3,4]))),C=i.lazy(()=>s(()=>import("./generators-effects-BsyASrjs.js").then(t=>t.N),__vite__mapDeps([6,1,2,3,4]))),D=i.lazy(()=>s(()=>import("./generators-effects-BsyASrjs.js").then(t=>t.a),__vite__mapDeps([6,1,2,3,4]))),k=i.lazy(()=>s(()=>import("./generators-3d-BehEtrUz.js").then(t=>t.p),__vite__mapDeps([3,1,2,4]))),S=i.lazy(()=>s(()=>import("./generators-3d-BehEtrUz.js").then(t=>t.q),__vite__mapDeps([3,1,2,4]))),M=i.lazy(()=>s(()=>import("./generators-3d-BehEtrUz.js").then(t=>t.r),__vite__mapDeps([3,1,2,4]))),B=i.lazy(()=>s(()=>import("./generators-3d-BehEtrUz.js").then(t=>t.s),__vite__mapDeps([3,1,2,4]))),F=i.lazy(()=>s(()=>import("./RippleGenerator-BsArBt1w.js"),__vite__mapDeps([7,1,2,3,4]))),$=i.lazy(()=>s(()=>import("./TypewriterGenerator-DrsWyP_1.js"),__vite__mapDeps([8,1,2,3,4]))),q=i.lazy(()=>s(()=>import("./FillGenerator-IMGJc78E.js"),__vite__mapDeps([9,1,2,3,4]))),U=()=>e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Loading generator..."})]});function W(){const[t,m]=i.useState("fade"),[o,x]=i.useState(!0),n=p=>{m(p)},r={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0,delayChildren:0}}},l={hidden:{opacity:0,y:-200},visible:{opacity:1,y:0,transition:{type:"spring",damping:15,stiffness:120,mass:1.5,delay:.2}}},a={hidden:{opacity:0,y:-200},visible:{opacity:1,y:0,transition:{type:"spring",damping:15,stiffness:120,mass:1.5,delay:.4}}},d=()=>({hidden:{opacity:0,scale:.95},visible:{opacity:1,scale:1,transition:{duration:.3,ease:[.25,.46,.45,.94]}},exit:{opacity:0,scale:.95,transition:{duration:.2,ease:[.25,.46,.45,.94]}}}),c={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.25,.46,.45,.94],delay:.8}}};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",style:{fontFamily:"Quicksand, sans-serif"},children:e.jsxs(u.div,{className:"flex h-screen",variants:r,initial:"hidden",animate:"visible",children:[e.jsx(P,{activeGenerator:t,onGeneratorChange:n}),e.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[e.jsxs(u.header,{className:"text-center py-8 px-4 lg:px-8 pt-20 lg:pt-8",children:[e.jsx(u.h1,{className:"text-4xl lg:text-6xl font-medium text-gray-900 mb-4 leading-tight",variants:l,children:"TAM Storyline Tool"}),e.jsx(u.p,{className:"text-lg lg:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed",variants:a,children:"Create custom effects for Articulate Storyline projects"})]}),e.jsx("div",{className:"flex-1 overflow-y-auto px-4 lg:px-8 pb-8",children:e.jsx("div",{className:"relative overflow-hidden rounded-2xl",children:e.jsx(y,{mode:"wait",children:e.jsx(u.div,{className:"bg-white/95 rounded-2xl p-8 shadow-xl backdrop-blur-sm border border-gray-50/30",variants:o?c:d(),initial:"hidden",animate:"visible",exit:"exit",onAnimationComplete:()=>x(!1),children:e.jsxs(i.Suspense,{fallback:e.jsx(U,{}),children:[t==="fade"&&e.jsx(A,{}),t==="slide"&&e.jsx(T,{}),t==="bounce"&&e.jsx(R,{}),t==="zoom"&&e.jsx(G,{}),t==="pulse"&&e.jsx(O,{}),t==="shake"&&e.jsx(I,{}),t==="tilt"&&e.jsx(L,{}),t==="rotate"&&e.jsx(N,{}),t==="morph"&&e.jsx(V,{}),t==="glitch"&&e.jsx(z,{}),t==="neon"&&e.jsx(C,{}),t==="magnetic"&&e.jsx(D,{}),t==="transform3d"&&e.jsx(k,{}),t==="pathdraw"&&e.jsx(S,{}),t==="confetti"&&e.jsx(M,{}),t==="physics"&&e.jsx(B,{}),t==="ripple"&&e.jsx(F,{}),t==="typewriter"&&e.jsx($,{}),t==="fill"&&e.jsx(q,{})]})},t)})})})]})]})})}j.createRoot(document.getElementById("root")).render(e.jsx(i.StrictMode,{children:e.jsx(W,{})}));
