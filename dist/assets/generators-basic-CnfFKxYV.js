import{r as t,j as e}from"./vendor-react-Ch3oStpB.js";import{d as g,T as d,P as c,C as u,e as m,f as x,h as b}from"./generators-3d-BehEtrUz.js";function f(){const[r,o]=t.useState({objectIds:[],trigger:"timeline",effect:"fadeIn",duration:1,delay:0,easing:"ease",initialOpacity:0}),[a,l]=t.useState(""),i=s=>{o(n=>({...n,objectIds:s}))};return t.useEffect(()=>{if(r.objectIds.length>0){const s=g(r);l(s)}else l("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Fade Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{value:r.objectIds,onChange:i,placeholder:"e.g. content1, text2, image3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:s=>o(n=>({...n,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Effect Type:"}),e.jsxs("select",{value:r.effect,onChange:s=>o(n=>({...n,effect:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"fadeIn",children:"Fade In"}),e.jsx("option",{value:"fadeOut",children:"Fade Out"}),e.jsx("option",{value:"fadeToggle",children:"Fade Toggle"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:r.easing,onChange:s=>o(n=>({...n,easing:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.duration,onChange:s=>o(n=>({...n,duration:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.delay,onChange:s=>o(n=>({...n,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Initial Opacity (%):"}),e.jsx("input",{type:"number",min:"0",max:"100",value:r.initialOpacity,onChange:s=>o(n=>({...n,initialOpacity:parseInt(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(c,{effectType:"fade",config:r})}),a&&e.jsx(u,{code:a})]})}const N=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));function p(){const[r,o]=t.useState({objectIds:[],trigger:"timeline",direction:"left",distance:10,duration:1,delay:0,easing:"ease"}),[a,l]=t.useState(""),i=s=>{o(n=>({...n,objectIds:s}))};return t.useEffect(()=>{if(r.objectIds.length>0){const s=m(r);l(s)}else l("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Slide In Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{value:r.objectIds,onChange:i,placeholder:"e.g. panel1, card2, button3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:s=>o(n=>({...n,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Direction:"}),e.jsxs("select",{value:r.direction,onChange:s=>o(n=>({...n,direction:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"left",children:"From Left"}),e.jsx("option",{value:"right",children:"From Right"}),e.jsx("option",{value:"up",children:"From Top"}),e.jsx("option",{value:"down",children:"From Bottom"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Distance (%):"}),e.jsx("input",{type:"number",value:r.distance,onChange:s=>o(n=>({...n,distance:parseInt(s.target.value)||10})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:r.easing,onChange:s=>o(n=>({...n,easing:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.duration,onChange:s=>o(n=>({...n,duration:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.delay,onChange:s=>o(n=>({...n,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(c,{effectType:"slide",config:r})}),a&&e.jsx(u,{code:a})]})}const C=Object.freeze(Object.defineProperty({__proto__:null,default:p},Symbol.toStringTag,{value:"Module"}));function h(){const[r,o]=t.useState({objectIds:[],trigger:"click",height:5,bounces:3,duration:1,delay:0}),[a,l]=t.useState(""),i=s=>{o(n=>({...n,objectIds:s}))};return t.useEffect(()=>{if(r.objectIds.length>0){const s=x(r);l(s)}else l("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Bounce Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{value:r.objectIds,onChange:i,placeholder:"e.g. button1, icon2, element3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:s=>o(n=>({...n,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"}),e.jsx("option",{value:"timeline",children:"Timeline Start"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bounce Height (%):"}),e.jsx("input",{type:"number",value:r.height,onChange:s=>o(n=>({...n,height:parseInt(s.target.value)||5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Bounces:"}),e.jsx("input",{type:"number",min:"1",max:"10",value:r.bounces,onChange:s=>o(n=>({...n,bounces:parseInt(s.target.value)||3})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.duration,onChange:s=>o(n=>({...n,duration:parseFloat(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.delay,onChange:s=>o(n=>({...n,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(c,{effectType:"bounce",config:r})}),a&&e.jsx(u,{code:a})]})}const I=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));function j(){const[r,o]=t.useState({objectIds:[],trigger:"click",effect:"zoomIn",scale:1.5,duration:.5,delay:0,easing:"ease",transformOrigin:"center"}),[a,l]=t.useState(""),i=s=>{o(n=>({...n,objectIds:s}))};return t.useEffect(()=>{if(r.objectIds.length>0){const s=b(r);l(s)}else l("")},[r]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Zoom Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{value:r.objectIds,onChange:i,placeholder:"e.g. image1, content2, popup3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:r.trigger,onChange:s=>o(n=>({...n,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"}),e.jsx("option",{value:"timeline",children:"Timeline Start"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Effect Type:"}),e.jsxs("select",{value:r.effect,onChange:s=>o(n=>({...n,effect:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"zoomIn",children:"Zoom In"}),e.jsx("option",{value:"zoomOut",children:"Zoom Out"}),e.jsx("option",{value:"zoomToggle",children:"Zoom Toggle"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scale Factor:"}),e.jsx("input",{type:"number",step:"0.1",value:r.scale,onChange:s=>o(n=>({...n,scale:parseFloat(s.target.value)||1.5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transform Origin:"}),e.jsxs("select",{value:r.transformOrigin,onChange:s=>o(n=>({...n,transformOrigin:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"top",children:"Top"}),e.jsx("option",{value:"bottom",children:"Bottom"}),e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"right",children:"Right"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.duration,onChange:s=>o(n=>({...n,duration:parseFloat(s.target.value)||.5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delay (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:r.delay,onChange:s=>o(n=>({...n,delay:parseFloat(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:r.easing,onChange:s=>o(n=>({...n,easing:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(c,{effectType:"zoom",config:r})}),a&&e.jsx(u,{code:a})]})}const O=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));export{I as B,N as F,C as S,O as Z};
