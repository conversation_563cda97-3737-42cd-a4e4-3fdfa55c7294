import{r as o,j as e}from"./vendor-react-Ch3oStpB.js";import{n as x,T as p,P as b,C as f}from"./generators-3d-BehEtrUz.js";import"./vendor-BXDNmyco.js";import"./vendor-animation-BKCFbksN.js";function N(){const[t,a]=o.useState({objectIds:[],trigger:"timeline",strings:["Hello World!","This is a typewriter effect."],typeSpeed:50,backSpeed:30,startDelay:0,backDelay:700,loop:!1,loopCount:1,showCursor:!0,cursorChar:"|",smartBackspace:!0,shuffle:!1,fadeOut:!1,fadeOutDelay:500}),[c,i]=o.useState(""),d=s=>{a(r=>({...r,objectIds:s}))},u=(s,r)=>{const n=[...t.strings];n[s]=r,a(l=>({...l,strings:n}))},m=()=>{a(s=>({...s,strings:[...s.strings,"New string..."]}))},g=s=>{if(t.strings.length>1){const r=t.strings.filter((n,l)=>l!==s);a(n=>({...n,strings:r}))}};return o.useEffect(()=>{if(t.objectIds.length>0){const s=x(t);i(s)}else i("")},[t]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Typewriter Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(p,{value:t.objectIds,onChange:d,placeholder:"e.g. text1, text2, heading3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:t.trigger,onChange:s=>a(r=>({...r,trigger:s.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"timeline",children:"Timeline"}),e.jsx("option",{value:"click",children:"Click"}),e.jsx("option",{value:"hover",children:"Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type Speed (ms):"}),e.jsx("input",{type:"number",value:t.typeSpeed,onChange:s=>a(r=>({...r,typeSpeed:parseInt(s.target.value)||50})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"1000"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Back Speed (ms):"}),e.jsx("input",{type:"number",value:t.backSpeed,onChange:s=>a(r=>({...r,backSpeed:parseInt(s.target.value)||30})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"1",max:"1000"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Start Delay (ms):"}),e.jsx("input",{type:"number",value:t.startDelay,onChange:s=>a(r=>({...r,startDelay:parseInt(s.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Back Delay (ms):"}),e.jsx("input",{type:"number",value:t.backDelay,onChange:s=>a(r=>({...r,backDelay:parseInt(s.target.value)||700})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cursor Character:"}),e.jsx("input",{type:"text",value:t.cursorChar,onChange:s=>a(r=>({...r,cursorChar:s.target.value||"|"})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",maxLength:3})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Text Strings:"}),e.jsxs("div",{className:"space-y-2",children:[t.strings.map((s,r)=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("input",{type:"text",value:s,onChange:n=>u(r,n.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:`String ${r+1}...`}),t.strings.length>1&&e.jsx("button",{type:"button",onClick:()=>g(r),className:"px-3 py-2 text-red-600 hover:text-red-800 focus:outline-none",children:"Remove"})]},r)),e.jsx("button",{type:"button",onClick:m,className:"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 focus:outline-none",children:"+ Add String"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.loop,onChange:s=>a(r=>({...r,loop:s.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Loop"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.showCursor,onChange:s=>a(r=>({...r,showCursor:s.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Show Cursor"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.smartBackspace,onChange:s=>a(r=>({...r,smartBackspace:s.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Smart Backspace"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.shuffle,onChange:s=>a(r=>({...r,shuffle:s.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Shuffle"})]})]}),t.loop&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Loop Count (0 = infinite):"}),e.jsx("input",{type:"number",value:t.loopCount,onChange:s=>a(r=>({...r,loopCount:parseInt(s.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0"})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(b,{effectType:"typewriter",config:t})}),c&&e.jsx(f,{code:c})]})}export{N as default};
