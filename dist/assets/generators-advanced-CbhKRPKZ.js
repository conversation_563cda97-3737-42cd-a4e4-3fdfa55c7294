import{r as o,j as e}from"./vendor-react-Ch3oStpB.js";import{g,T as c,P as d,C as u,a as m,b,c as x}from"./generators-3d-BehEtrUz.js";function f(){const[t,n]=o.useState({objectIds:[],maxTilt:15,speed:400,glare:!0,scale:1.02}),[a,l]=o.useState(""),i=r=>{n(s=>({...s,objectIds:r}))};return o.useEffect(()=>{if(t.objectIds.length>0){const r=g(t);l(r)}else l("")},[t]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Tilt Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:t.objectIds,onChange:i,placeholder:"e.g. card1, card2, buttonMain",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Tilt (degrees):"}),e.jsx("input",{type:"number",value:t.maxTilt,onChange:r=>n(s=>({...s,maxTilt:parseInt(r.target.value)||15})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Speed (ms):"}),e.jsx("input",{type:"number",value:t.speed,onChange:r=>n(s=>({...s,speed:parseInt(r.target.value)||400})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scale Factor:"}),e.jsx("input",{type:"number",step:"0.01",value:t.scale,onChange:r=>n(s=>({...s,scale:parseFloat(r.target.value)||1.02})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Glare Effect:"}),e.jsxs("select",{value:t.glare.toString(),onChange:r=>n(s=>({...s,glare:r.target.value==="true"})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"true",children:"Enabled"}),e.jsx("option",{value:"false",children:"Disabled"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(d,{effectType:"tilt",config:t})}),a&&e.jsx(u,{code:a})]})}const N=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));function p(){const[t,n]=o.useState({objectIds:[],axis:"Z",degrees:90,duration:.4,easing:"ease"}),[a,l]=o.useState(""),i=r=>{n(s=>({...s,objectIds:r}))};return o.useEffect(()=>{if(t.objectIds.length>0){const r=m(t);l(r)}else l("")},[t]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Rotate on Click Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:t.objectIds,onChange:i,placeholder:"e.g. icon1, gear2",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Rotation Axis:"}),e.jsxs("select",{value:t.axis,onChange:r=>n(s=>({...s,axis:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"Z",children:"Z"}),e.jsx("option",{value:"X",children:"X"}),e.jsx("option",{value:"Y",children:"Y"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Degrees per Click:"}),e.jsx("input",{type:"number",value:t.degrees,onChange:r=>n(s=>({...s,degrees:parseInt(r.target.value)||90})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:t.duration,onChange:r=>n(s=>({...s,duration:parseFloat(r.target.value)||.4})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Easing:"}),e.jsxs("select",{value:t.easing,onChange:r=>n(s=>({...s,easing:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"ease",children:"Ease"}),e.jsx("option",{value:"ease-in",children:"Ease In"}),e.jsx("option",{value:"ease-out",children:"Ease Out"}),e.jsx("option",{value:"ease-in-out",children:"Ease In-Out"}),e.jsx("option",{value:"linear",children:"Linear"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(d,{effectType:"rotate",config:t})}),a&&e.jsx(u,{code:a})]})}const C=Object.freeze(Object.defineProperty({__proto__:null,default:p},Symbol.toStringTag,{value:"Module"}));function j(){const[t,n]=o.useState({objectIds:[],trigger:"continuous",scale:1.1,duration:1,iterations:3,transformOrigin:"center"}),[a,l]=o.useState(""),i=r=>{n(s=>({...s,objectIds:r}))};return o.useEffect(()=>{if(t.objectIds.length>0){const r=b(t);l(r)}else l("")},[t]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Pulse Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:t.objectIds,onChange:i,placeholder:"e.g. alert1, notification2, cta3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:t.trigger,onChange:r=>n(s=>({...s,trigger:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"continuous",children:"Continuous"}),e.jsx("option",{value:"timeline",children:"Timeline Start"}),e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Scale Factor:"}),e.jsx("input",{type:"number",step:"0.1",value:t.scale,onChange:r=>n(s=>({...s,scale:parseFloat(r.target.value)||1.1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:t.duration,onChange:r=>n(s=>({...s,duration:parseFloat(r.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),t.trigger!=="continuous"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Iterations:"}),e.jsx("input",{type:"number",value:t.iterations,onChange:r=>n(s=>({...s,iterations:parseInt(r.target.value)||3})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transform Origin:"}),e.jsxs("select",{value:t.transformOrigin,onChange:r=>n(s=>({...s,transformOrigin:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"center",children:"Center"}),e.jsx("option",{value:"top",children:"Top"}),e.jsx("option",{value:"bottom",children:"Bottom"}),e.jsx("option",{value:"left",children:"Left"}),e.jsx("option",{value:"right",children:"Right"})]})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(d,{effectType:"pulse",config:t})}),a&&e.jsx(u,{code:a})]})}const I=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));function h(){const[t,n]=o.useState({objectIds:[],trigger:"click",intensity:1,duration:.5,direction:"both",frequency:10}),[a,l]=o.useState(""),i=r=>{n(s=>({...s,objectIds:r}))};return o.useEffect(()=>{if(t.objectIds.length>0){const r=x(t);l(r)}else l("")},[t]),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Shake Effect Generator"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{value:t.objectIds,onChange:i,placeholder:"e.g. error1, warning2, feedback3",label:"Object IDs"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Trigger:"}),e.jsxs("select",{value:t.trigger,onChange:r=>n(s=>({...s,trigger:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"click",children:"On Click"}),e.jsx("option",{value:"hover",children:"On Hover"}),e.jsx("option",{value:"timeline",children:"Timeline Start"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Direction:"}),e.jsxs("select",{value:t.direction,onChange:r=>n(s=>({...s,direction:r.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"both",children:"Both Directions"}),e.jsx("option",{value:"horizontal",children:"Horizontal Only"}),e.jsx("option",{value:"vertical",children:"Vertical Only"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Intensity (%):"}),e.jsx("input",{type:"number",value:t.intensity,onChange:r=>n(s=>({...s,intensity:parseInt(r.target.value)||1})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Duration (seconds):"}),e.jsx("input",{type:"number",step:"0.1",value:t.duration,onChange:r=>n(s=>({...s,duration:parseFloat(r.target.value)||.5})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Frequency (shakes/sec):"}),e.jsx("input",{type:"number",value:t.frequency,onChange:r=>n(s=>({...s,frequency:parseInt(r.target.value)||10})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsx("div",{className:"mt-8",children:e.jsx(d,{effectType:"shake",config:t})}),a&&e.jsx(u,{code:a})]})}const k=Object.freeze(Object.defineProperty({__proto__:null,default:h},Symbol.toStringTag,{value:"Module"}));export{I as P,C as R,k as S,N as T};
