import{r as b,j as Dt}from"./vendor-react-Ch3oStpB.js";import{i as qo,m as un,a as ln,t as Te,b as hn,g as tr,n as er,c as Z,p as Ae,d as de,M as ii,e as cn,s as Ke,f as X,J as Ko,A as Zo,h as Qo,j as ir,k as Y,l as at,o as nr,q as Jt,r as Jo,u as Bi,v as sr,w as ta,x as ni,y as le,z as Fi,B as rr,C as or,D as fn,E as ea,S as ar,F as ia,G as na,H as xi,I as sa,K as ra,L as oa,N as aa,O as ua,P as la,Q as ha,R as ca,T as fa,U as da,V as pa}from"./vendor-BXDNmyco.js";const dn=b.createContext({});function pn(n){const t=b.useRef(null);return t.current===null&&(t.current=n()),t.current}const mn=typeof window<"u",ur=mn?b.useLayoutEffect:b.useEffect,di=b.createContext(null),gn=b.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});class ma extends b.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const i=e.offsetParent,s=qo(i)&&i.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function ga({children:n,isPresent:t,anchorX:e}){const i=b.useId(),s=b.useRef(null),r=b.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=b.useContext(gn);return b.useInsertionEffect(()=>{const{width:a,height:u,top:h,left:l,right:c}=r.current;if(t||!s.current||!a||!u)return;const f=e==="left"?`left: ${l}`:`right: ${c}`;s.current.dataset.motionPopId=i;const d=document.createElement("style");return o&&(d.nonce=o),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${u}px !important;
            ${f}px !important;
            top: ${h}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[t]),Dt.jsx(ma,{isPresent:t,childRef:s,sizeRef:r,children:b.cloneElement(n,{ref:s})})}const _a=({children:n,initial:t,isPresent:e,onExitComplete:i,custom:s,presenceAffectsLayout:r,mode:o,anchorX:a})=>{const u=pn(ya),h=b.useId();let l=!0,c=b.useMemo(()=>(l=!1,{id:h,initial:t,isPresent:e,custom:s,onExitComplete:f=>{u.set(f,!0);for(const d of u.values())if(!d)return;i&&i()},register:f=>(u.set(f,!1),()=>u.delete(f))}),[e,u,i]);return r&&l&&(c={...c}),b.useMemo(()=>{u.forEach((f,d)=>u.set(d,!1))},[e]),b.useEffect(()=>{!e&&!u.size&&i&&i()},[e]),o==="popLayout"&&(n=Dt.jsx(ga,{isPresent:e,anchorX:a,children:n})),Dt.jsx(di.Provider,{value:c,children:n})};function ya(){return new Map}function lr(n=!0){const t=b.useContext(di);if(t===null)return[!0,null];const{isPresent:e,onExitComplete:i,register:s}=t,r=b.useId();b.useEffect(()=>{if(n)return s(r)},[n]);const o=b.useCallback(()=>n&&i&&i(r),[r,i,n]);return!e&&i?[!1,o]:[!0]}const Xe=n=>n.key||"";function $n(n){const t=[];return b.Children.forEach(n,e=>{b.isValidElement(e)&&t.push(e)}),t}const vc=({children:n,custom:t,initial:e=!0,onExitComplete:i,presenceAffectsLayout:s=!0,mode:r="sync",propagate:o=!1,anchorX:a="left"})=>{const[u,h]=lr(o),l=b.useMemo(()=>$n(n),[n]),c=o&&!u?[]:l.map(Xe),f=b.useRef(!0),d=b.useRef(l),m=pn(()=>new Map),[p,g]=b.useState(l),[_,y]=b.useState(l);ur(()=>{f.current=!1,d.current=l;for(let x=0;x<_.length;x++){const P=Xe(_[x]);c.includes(P)?m.delete(P):m.get(P)!==!0&&m.set(P,!1)}},[_,c.length,c.join("-")]);const v=[];if(l!==p){let x=[...l];for(let P=0;P<_.length;P++){const w=_[P],C=Xe(w);c.includes(C)||(x.splice(P,0,w),v.push(w))}return r==="wait"&&v.length&&(x=v),y($n(x)),g(l),null}const{forceRender:T}=b.useContext(dn);return Dt.jsx(Dt.Fragment,{children:_.map(x=>{const P=Xe(x),w=o&&!u?!1:l===_||c.includes(P),C=()=>{if(m.has(P))m.set(P,!0);else return;let S=!0;m.forEach(A=>{A||(S=!1)}),S&&(T==null||T(),y(d.current),o&&(h==null||h()),i&&i())};return Dt.jsx(_a,{isPresent:w,initial:!f.current||e?void 0:!1,custom:t,presenceAffectsLayout:s,mode:r,onExitComplete:w?void 0:C,anchorX:a,children:x},P)})})},hr=b.createContext({strict:!1}),Yn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},pe={};for(const n in Yn)pe[n]={isEnabled:t=>Yn[n].some(e=>!!t[e])};function va(n){for(const t in n)pe[t]={...pe[t],...n[t]}}const xa=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function si(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||xa.has(n)}let cr=n=>!si(n);function Ta(n){n&&(cr=t=>t.startsWith("on")?!si(t):n(t))}try{Ta(require("@emotion/is-prop-valid").default)}catch{}function Pa(n,t,e){const i={};for(const s in n)s==="values"&&typeof n.values=="object"||(cr(s)||e===!0&&si(s)||!t&&!si(s)||n.draggable&&s.startsWith("onDrag"))&&(i[s]=n[s]);return i}function Sa(n){if(typeof Proxy>"u")return n;const t=new Map,e=(...i)=>n(...i);return new Proxy(e,{get:(i,s)=>s==="create"?n:(t.has(s)||t.set(s,n(s)),t.get(s))})}const pi=b.createContext({});function mi(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function Ee(n){return typeof n=="string"||Array.isArray(n)}const _n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],yn=["initial",..._n];function gi(n){return mi(n.animate)||yn.some(t=>Ee(n[t]))}function fr(n){return!!(gi(n)||n.variants)}function wa(n,t){if(gi(n)){const{initial:e,animate:i}=n;return{initial:e===!1||Ee(e)?e:void 0,animate:Ee(i)?i:void 0}}return n.inherit!==!1?t:{}}function Ca(n){const{initial:t,animate:e}=wa(n,b.useContext(pi));return b.useMemo(()=>({initial:t,animate:e}),[Xn(t),Xn(e)])}function Xn(n){return Array.isArray(n)?n.join(" "):n}const ba=Symbol.for("motionComponentSymbol");function ne(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function Aa(n,t,e){return b.useCallback(i=>{i&&n.onMount&&n.onMount(i),t&&(i?t.mount(i):t.unmount()),e&&(typeof e=="function"?e(i):ne(e)&&(e.current=i))},[t])}const vn=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Da="framerAppearId",dr="data-"+vn(Da),pr=b.createContext({});function Ma(n,t,e,i,s){var p,g;const{visualElement:r}=b.useContext(pi),o=b.useContext(hr),a=b.useContext(di),u=b.useContext(gn).reducedMotion,h=b.useRef(null);i=i||o.renderer,!h.current&&i&&(h.current=i(n,{visualState:t,parent:r,props:e,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:u}));const l=h.current,c=b.useContext(pr);l&&!l.projection&&s&&(l.type==="html"||l.type==="svg")&&Va(h.current,e,s,c);const f=b.useRef(!1);b.useInsertionEffect(()=>{l&&f.current&&l.update(e,a)});const d=e[dr],m=b.useRef(!!d&&!((p=window.MotionHandoffIsComplete)!=null&&p.call(window,d))&&((g=window.MotionHasOptimisedAnimation)==null?void 0:g.call(window,d)));return ur(()=>{l&&(f.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),un.render(l.render),m.current&&l.animationState&&l.animationState.animateChanges())}),b.useEffect(()=>{l&&(!m.current&&l.animationState&&l.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{var _;(_=window.MotionHandoffMarkAsComplete)==null||_.call(window,d)}),m.current=!1))}),l}function Va(n,t,e,i){const{layoutId:s,layout:r,drag:o,dragConstraints:a,layoutScroll:u,layoutRoot:h,layoutCrossfade:l}=t;n.projection=new e(n.latestValues,t["data-framer-portal-id"]?void 0:mr(n.parent)),n.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!o||a&&ne(a),visualElement:n,animationType:typeof r=="string"?r:"both",initialPromotionConfig:i,crossfade:l,layoutScroll:u,layoutRoot:h})}function mr(n){if(n)return n.options.allowProjection!==!1?n.projection:mr(n.parent)}function Ra({preloadedFeatures:n,createVisualElement:t,useRender:e,useVisualState:i,Component:s}){n&&va(n);function r(a,u){let h;const l={...b.useContext(gn),...a,layoutId:Oa(a)},{isStatic:c}=l,f=Ca(a),d=i(a,c);if(!c&&mn){ka();const m=Ea(l);h=m.MeasureLayout,f.visualElement=Ma(s,d,l,t,m.ProjectionNode)}return Dt.jsxs(pi.Provider,{value:f,children:[h&&f.visualElement?Dt.jsx(h,{visualElement:f.visualElement,...l}):null,e(s,a,Aa(d,f.visualElement,u),d,c,f.visualElement)]})}r.displayName=`motion.${typeof s=="string"?s:`create(${s.displayName??s.name??""})`}`;const o=b.forwardRef(r);return o[ba]=s,o}function Oa({layoutId:n}){const t=b.useContext(dn).id;return t&&n!==void 0?t+"-"+n:n}function ka(n,t){b.useContext(hr).strict}function Ea(n){const{drag:t,layout:e}=pe;if(!t&&!e)return{};const i={...t,...e};return{MeasureLayout:t!=null&&t.isEnabled(n)||e!=null&&e.isEnabled(n)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}const Le={};function La(n){for(const t in n)Le[t]=n[t],ln(t)&&(Le[t].isCSSVariable=!0)}function gr(n,{layout:t,layoutId:e}){return Te.has(n)||n.startsWith("origin")||(t||e!==void 0)&&(!!Le[n]||n==="opacity")}const Ba={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Fa=hn.length;function Ia(n,t,e){let i="",s=!0;for(let r=0;r<Fa;r++){const o=hn[r],a=n[o];if(a===void 0)continue;let u=!0;if(typeof a=="number"?u=a===(o.startsWith("scale")?1:0):u=parseFloat(a)===0,!u||e){const h=tr(a,er[o]);if(!u){s=!1;const l=Ba[o]||o;i+=`${l}(${h}) `}e&&(t[o]=h)}}return i=i.trim(),e?i=e(t,s?"":i):s&&(i="none"),i}function xn(n,t,e){const{style:i,vars:s,transformOrigin:r}=n;let o=!1,a=!1;for(const u in t){const h=t[u];if(Te.has(u)){o=!0;continue}else if(ln(u)){s[u]=h;continue}else{const l=tr(h,er[u]);u.startsWith("origin")?(a=!0,r[u]=l):i[u]=l}}if(t.transform||(o||e?i.transform=Ia(t,n.transform,e):i.transform&&(i.transform="none")),a){const{originX:u="50%",originY:h="50%",originZ:l=0}=r;i.transformOrigin=`${u} ${h} ${l}`}}const Tn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function _r(n,t,e){for(const i in t)!Z(t[i])&&!gr(i,e)&&(n[i]=t[i])}function ja({transformTemplate:n},t){return b.useMemo(()=>{const e=Tn();return xn(e,t,n),Object.assign({},e.vars,e.style)},[t])}function za(n,t){const e=n.style||{},i={};return _r(i,e,n),Object.assign(i,ja(n,t)),i}function Ua(n,t){const e={},i=za(n,t);return n.drag&&n.dragListener!==!1&&(e.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(e.tabIndex=0),e.style=i,e}const Na={offset:"stroke-dashoffset",array:"stroke-dasharray"},Wa={offset:"strokeDashoffset",array:"strokeDasharray"};function Ga(n,t,e=1,i=0,s=!0){n.pathLength=1;const r=s?Na:Wa;n[r.offset]=Ae.transform(-i);const o=Ae.transform(t),a=Ae.transform(e);n[r.array]=`${o} ${a}`}function yr(n,{attrX:t,attrY:e,attrScale:i,pathLength:s,pathSpacing:r=1,pathOffset:o=0,...a},u,h,l){if(xn(n,a,h),u){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:c,style:f}=n;c.transform&&(f.transform=c.transform,delete c.transform),(f.transform||c.transformOrigin)&&(f.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),f.transform&&(f.transformBox=(l==null?void 0:l.transformBox)??"fill-box",delete c.transformBox),t!==void 0&&(c.x=t),e!==void 0&&(c.y=e),i!==void 0&&(c.scale=i),s!==void 0&&Ga(c,s,r,o,!1)}const vr=()=>({...Tn(),attrs:{}}),xr=n=>typeof n=="string"&&n.toLowerCase()==="svg";function $a(n,t,e,i){const s=b.useMemo(()=>{const r=vr();return yr(r,t,xr(i),n.transformTemplate,n.style),{...r.attrs,style:{...r.style}}},[t]);if(n.style){const r={};_r(r,n.style,n),s.style={...r,...s.style}}return s}const Ya=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Pn(n){return typeof n!="string"||n.includes("-")?!1:!!(Ya.indexOf(n)>-1||/[A-Z]/u.test(n))}function Xa(n=!1){return(e,i,s,{latestValues:r},o)=>{const u=(Pn(e)?$a:Ua)(i,r,o,e),h=Pa(i,typeof e=="string",n),l=e!==b.Fragment?{...h,...u,ref:s}:{},{children:c}=i,f=b.useMemo(()=>Z(c)?c.get():c,[c]);return b.createElement(e,{...l,children:f})}}function Hn(n){const t=[{},{}];return n==null||n.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function Sn(n,t,e,i){if(typeof t=="function"){const[s,r]=Hn(i);t=t(e!==void 0?e:n.custom,s,r)}if(typeof t=="string"&&(t=n.variants&&n.variants[t]),typeof t=="function"){const[s,r]=Hn(i);t=t(e!==void 0?e:n.custom,s,r)}return t}function Ze(n){return Z(n)?n.get():n}function Ha({scrapeMotionValuesFromProps:n,createRenderState:t},e,i,s){return{latestValues:qa(e,i,s,n),renderState:t()}}const Tr=n=>(t,e)=>{const i=b.useContext(pi),s=b.useContext(di),r=()=>Ha(n,t,i,s);return e?r():pn(r)};function qa(n,t,e,i){const s={},r=i(n,{});for(const f in r)s[f]=Ze(r[f]);let{initial:o,animate:a}=n;const u=gi(n),h=fr(n);t&&h&&!u&&n.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let l=e?e.initial===!1:!1;l=l||o===!1;const c=l?a:o;if(c&&typeof c!="boolean"&&!mi(c)){const f=Array.isArray(c)?c:[c];for(let d=0;d<f.length;d++){const m=Sn(n,f[d]);if(m){const{transitionEnd:p,transition:g,..._}=m;for(const y in _){let v=_[y];if(Array.isArray(v)){const T=l?v.length-1:0;v=v[T]}v!==null&&(s[y]=v)}for(const y in p)s[y]=p[y]}}}return s}function wn(n,t,e){var r;const{style:i}=n,s={};for(const o in i)(Z(i[o])||t.style&&Z(t.style[o])||gr(o,n)||((r=e==null?void 0:e.getValue(o))==null?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}const Ka={useVisualState:Tr({scrapeMotionValuesFromProps:wn,createRenderState:Tn})};function Pr(n,t,e){const i=wn(n,t,e);for(const s in n)if(Z(n[s])||Z(t[s])){const r=hn.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;i[r]=n[s]}return i}const Za={useVisualState:Tr({scrapeMotionValuesFromProps:Pr,createRenderState:vr})};function Qa(n,t){return function(i,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...Pn(i)?Za:Ka,preloadedFeatures:n,useRender:Xa(s),createVisualElement:t,Component:i};return Ra(o)}}function Be(n,t,e){const i=n.getProps();return Sn(i,t,e!==void 0?e:i.custom,n)}const Ii=n=>Array.isArray(n);function Ja(n,t,e){n.hasValue(t)?n.getValue(t).set(e):n.addValue(t,de(e))}function tu(n){return Ii(n)?n[n.length-1]||0:n}function eu(n,t){const e=Be(n,t);let{transitionEnd:i={},transition:s={},...r}=e||{};r={...r,...i};for(const o in r){const a=tu(r[o]);Ja(n,o,a)}}function iu(n){return!!(Z(n)&&n.add)}function ji(n,t){const e=n.getValue("willChange");if(iu(e))return e.add(t);if(!e&&ii.WillChange){const i=new ii.WillChange("auto");n.addValue("willChange",i),i.add(t)}}function Sr(n){return n.props[dr]}const nu=n=>n!==null;function su(n,{repeat:t,repeatType:e="loop"},i){const s=n.filter(nu),r=t&&e!=="loop"&&t%2===1?0:s.length-1;return s[r]}const ru={type:"spring",stiffness:500,damping:25,restSpeed:10},ou=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),au={type:"keyframes",duration:.8},uu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},lu=(n,{keyframes:t})=>t.length>2?au:Te.has(n)?n.startsWith("scale")?ou(t[1]):ru:uu;function hu({when:n,delay:t,delayChildren:e,staggerChildren:i,staggerDirection:s,repeat:r,repeatType:o,repeatDelay:a,from:u,elapsed:h,...l}){return!!Object.keys(l).length}const Cn=(n,t,e,i={},s,r)=>o=>{const a=cn(i,n)||{},u=a.delay||i.delay||0;let{elapsed:h=0}=i;h=h-Ke(u);const l={keyframes:Array.isArray(e)?e:[null,e],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-h,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:n,motionValue:t,element:r?void 0:s};hu(a)||Object.assign(l,lu(n,l)),l.duration&&(l.duration=Ke(l.duration)),l.repeatDelay&&(l.repeatDelay=Ke(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let c=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(l.duration=0,l.delay===0&&(c=!0)),(ii.instantAnimations||ii.skipAnimations)&&(c=!0,l.duration=0,l.delay=0),l.allowFlatten=!a.type&&!a.ease,c&&!r&&t.get()!==void 0){const f=su(l.keyframes,a);if(f!==void 0){X.update(()=>{l.onUpdate(f),l.onComplete()});return}}return a.isSync?new Ko(l):new Zo(l)};function cu({protectedKeys:n,needsAnimating:t},e){const i=n.hasOwnProperty(e)&&t[e]!==!0;return t[e]=!1,i}function wr(n,t,{delay:e=0,transitionOverride:i,type:s}={}){let{transition:r=n.getDefaultTransition(),transitionEnd:o,...a}=t;i&&(r=i);const u=[],h=s&&n.animationState&&n.animationState.getState()[s];for(const l in a){const c=n.getValue(l,n.latestValues[l]??null),f=a[l];if(f===void 0||h&&cu(h,l))continue;const d={delay:e,...cn(r||{},l)},m=c.get();if(m!==void 0&&!c.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const _=Sr(n);if(_){const y=window.MotionHandoffAnimation(_,l,X);y!==null&&(d.startTime=y,p=!0)}}ji(n,l),c.start(Cn(l,c,f,n.shouldReduceMotion&&Qo.has(l)?{type:!1}:d,n,p));const g=c.animation;g&&u.push(g)}return o&&Promise.all(u).then(()=>{X.update(()=>{o&&eu(n,o)})}),u}function zi(n,t,e={}){var u;const i=Be(n,t,e.type==="exit"?(u=n.presenceContext)==null?void 0:u.custom:void 0);let{transition:s=n.getDefaultTransition()||{}}=i||{};e.transitionOverride&&(s=e.transitionOverride);const r=i?()=>Promise.all(wr(n,i,e)):()=>Promise.resolve(),o=n.variantChildren&&n.variantChildren.size?(h=0)=>{const{delayChildren:l=0,staggerChildren:c,staggerDirection:f}=s;return fu(n,t,l+h,c,f,e)}:()=>Promise.resolve(),{when:a}=s;if(a){const[h,l]=a==="beforeChildren"?[r,o]:[o,r];return h().then(()=>l())}else return Promise.all([r(),o(e.delay)])}function fu(n,t,e=0,i=0,s=1,r){const o=[],a=(n.variantChildren.size-1)*i,u=s===1?(h=0)=>h*i:(h=0)=>a-h*i;return Array.from(n.variantChildren).sort(du).forEach((h,l)=>{h.notify("AnimationStart",t),o.push(zi(h,t,{...r,delay:e+u(l)}).then(()=>h.notify("AnimationComplete",t)))}),Promise.all(o)}function du(n,t){return n.sortNodePosition(t)}function pu(n,t,e={}){n.notify("AnimationStart",t);let i;if(Array.isArray(t)){const s=t.map(r=>zi(n,r,e));i=Promise.all(s)}else if(typeof t=="string")i=zi(n,t,e);else{const s=typeof t=="function"?Be(n,t,e.custom):t;i=Promise.all(wr(n,s,e))}return i.then(()=>{n.notify("AnimationComplete",t)})}function Cr(n,t){if(!Array.isArray(t))return!1;const e=t.length;if(e!==n.length)return!1;for(let i=0;i<e;i++)if(t[i]!==n[i])return!1;return!0}const mu=yn.length;function br(n){if(!n)return;if(!n.isControllingVariants){const e=n.parent?br(n.parent)||{}:{};return n.props.initial!==void 0&&(e.initial=n.props.initial),e}const t={};for(let e=0;e<mu;e++){const i=yn[e],s=n.props[i];(Ee(s)||s===!1)&&(t[i]=s)}return t}const gu=[..._n].reverse(),_u=_n.length;function yu(n){return t=>Promise.all(t.map(({animation:e,options:i})=>pu(n,e,i)))}function vu(n){let t=yu(n),e=qn(),i=!0;const s=u=>(h,l)=>{var f;const c=Be(n,l,u==="exit"?(f=n.presenceContext)==null?void 0:f.custom:void 0);if(c){const{transition:d,transitionEnd:m,...p}=c;h={...h,...p,...m}}return h};function r(u){t=u(n)}function o(u){const{props:h}=n,l=br(n.parent)||{},c=[],f=new Set;let d={},m=1/0;for(let g=0;g<_u;g++){const _=gu[g],y=e[_],v=h[_]!==void 0?h[_]:l[_],T=Ee(v),x=_===u?y.isActive:null;x===!1&&(m=g);let P=v===l[_]&&v!==h[_]&&T;if(P&&i&&n.manuallyAnimateOnMount&&(P=!1),y.protectedKeys={...d},!y.isActive&&x===null||!v&&!y.prevProp||mi(v)||typeof v=="boolean")continue;const w=xu(y.prevProp,v);let C=w||_===u&&y.isActive&&!P&&T||g>m&&T,S=!1;const A=Array.isArray(v)?v:[v];let V=A.reduce(s(_),{});x===!1&&(V={});const{prevResolvedValues:R={}}=y,O={...R,...V},N=M=>{C=!0,f.has(M)&&(S=!0,f.delete(M)),y.needsAnimating[M]=!0;const j=n.getValue(M);j&&(j.liveStyle=!1)};for(const M in O){const j=V[M],Rt=R[M];if(d.hasOwnProperty(M))continue;let wt=!1;Ii(j)&&Ii(Rt)?wt=!Cr(j,Rt):wt=j!==Rt,wt?j!=null?N(M):f.add(M):j!==void 0&&f.has(M)?N(M):y.protectedKeys[M]=!0}y.prevProp=v,y.prevResolvedValues=V,y.isActive&&(d={...d,...V}),i&&n.blockInitialAnimation&&(C=!1),C&&(!(P&&w)||S)&&c.push(...A.map(M=>({animation:M,options:{type:_}})))}if(f.size){const g={};if(typeof h.initial!="boolean"){const _=Be(n,Array.isArray(h.initial)?h.initial[0]:h.initial);_&&_.transition&&(g.transition=_.transition)}f.forEach(_=>{const y=n.getBaseTarget(_),v=n.getValue(_);v&&(v.liveStyle=!0),g[_]=y??null}),c.push({animation:g})}let p=!!c.length;return i&&(h.initial===!1||h.initial===h.animate)&&!n.manuallyAnimateOnMount&&(p=!1),i=!1,p?t(c):Promise.resolve()}function a(u,h){var c;if(e[u].isActive===h)return Promise.resolve();(c=n.variantChildren)==null||c.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(u,h)}),e[u].isActive=h;const l=o(u);for(const f in e)e[f].protectedKeys={};return l}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>e,reset:()=>{e=qn(),i=!0}}}function xu(n,t){return typeof t=="string"?t!==n:Array.isArray(t)?!Cr(t,n):!1}function Wt(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function qn(){return{animate:Wt(!0),whileInView:Wt(),whileHover:Wt(),whileTap:Wt(),whileDrag:Wt(),whileFocus:Wt(),exit:Wt()}}class jt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Tu extends jt{constructor(t){super(t),t.animationState||(t.animationState=vu(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();mi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)==null||t.call(this)}}let Pu=0;class Su extends jt{constructor(){super(...arguments),this.id=Pu++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const wu={animation:{Feature:Tu},exit:{Feature:Su}};function Fe(n,t,e,i={passive:!0}){return n.addEventListener(t,e,i),()=>n.removeEventListener(t,e)}function $e(n){return{point:{x:n.pageX,y:n.pageY}}}const Cu=n=>t=>ir(t)&&n(t,$e(t));function De(n,t,e,i){return Fe(n,t,Cu(e),i)}function Ar({top:n,left:t,right:e,bottom:i}){return{x:{min:t,max:e},y:{min:n,max:i}}}function bu({x:n,y:t}){return{top:t.min,right:n.max,bottom:t.max,left:n.min}}function Au(n,t){if(!t)return n;const e=t({x:n.left,y:n.top}),i=t({x:n.right,y:n.bottom});return{top:e.y,left:e.x,bottom:i.y,right:i.x}}const Dr=1e-4,Du=1-Dr,Mu=1+Dr,Mr=.01,Vu=0-Mr,Ru=0+Mr;function et(n){return n.max-n.min}function Ou(n,t,e){return Math.abs(n-t)<=e}function Kn(n,t,e,i=.5){n.origin=i,n.originPoint=Y(t.min,t.max,n.origin),n.scale=et(e)/et(t),n.translate=Y(e.min,e.max,n.origin)-n.originPoint,(n.scale>=Du&&n.scale<=Mu||isNaN(n.scale))&&(n.scale=1),(n.translate>=Vu&&n.translate<=Ru||isNaN(n.translate))&&(n.translate=0)}function Me(n,t,e,i){Kn(n.x,t.x,e.x,i?i.originX:void 0),Kn(n.y,t.y,e.y,i?i.originY:void 0)}function Zn(n,t,e){n.min=e.min+t.min,n.max=n.min+et(t)}function ku(n,t,e){Zn(n.x,t.x,e.x),Zn(n.y,t.y,e.y)}function Qn(n,t,e){n.min=t.min-e.min,n.max=n.min+et(t)}function Ve(n,t,e){Qn(n.x,t.x,e.x),Qn(n.y,t.y,e.y)}const Jn=()=>({translate:0,scale:1,origin:0,originPoint:0}),se=()=>({x:Jn(),y:Jn()}),ts=()=>({min:0,max:0}),z=()=>({x:ts(),y:ts()});function mt(n){return[n("x"),n("y")]}function Ti(n){return n===void 0||n===1}function Ui({scale:n,scaleX:t,scaleY:e}){return!Ti(n)||!Ti(t)||!Ti(e)}function Yt(n){return Ui(n)||Vr(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function Vr(n){return es(n.x)||es(n.y)}function es(n){return n&&n!=="0%"}function ri(n,t,e){const i=n-e,s=t*i;return e+s}function is(n,t,e,i,s){return s!==void 0&&(n=ri(n,s,i)),ri(n,e,i)+t}function Ni(n,t=0,e=1,i,s){n.min=is(n.min,t,e,i,s),n.max=is(n.max,t,e,i,s)}function Rr(n,{x:t,y:e}){Ni(n.x,t.translate,t.scale,t.originPoint),Ni(n.y,e.translate,e.scale,e.originPoint)}const ns=.999999999999,ss=1.0000000000001;function Eu(n,t,e,i=!1){const s=e.length;if(!s)return;t.x=t.y=1;let r,o;for(let a=0;a<s;a++){r=e[a],o=r.projectionDelta;const{visualElement:u}=r.options;u&&u.props.style&&u.props.style.display==="contents"||(i&&r.options.layoutScroll&&r.scroll&&r!==r.root&&oe(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Rr(n,o)),i&&Yt(r.latestValues)&&oe(n,r.latestValues))}t.x<ss&&t.x>ns&&(t.x=1),t.y<ss&&t.y>ns&&(t.y=1)}function re(n,t){n.min=n.min+t,n.max=n.max+t}function rs(n,t,e,i,s=.5){const r=Y(n.min,n.max,s);Ni(n,t,e,r,i)}function oe(n,t){rs(n.x,t.x,t.scaleX,t.scale,t.originX),rs(n.y,t.y,t.scaleY,t.scale,t.originY)}function Or(n,t){return Ar(Au(n.getBoundingClientRect(),t))}function Lu(n,t,e){const i=Or(n,e),{scroll:s}=t;return s&&(re(i.x,s.offset.x),re(i.y,s.offset.y)),i}const kr=({current:n})=>n?n.ownerDocument.defaultView:null,os=(n,t)=>Math.abs(n-t);function Bu(n,t){const e=os(n.x,t.x),i=os(n.y,t.y);return Math.sqrt(e**2+i**2)}class Er{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=Si(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=Bu(c.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=c,{timestamp:p}=at;this.history.push({...m,timestamp:p});const{onStart:g,onMove:_}=this.handlers;f||(g&&g(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),_&&_(this.lastMoveEvent,c)},this.handlePointerMove=(c,f)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=Pi(f,this.transformPagePoint),X.update(this.updatePoint,!0)},this.handlePointerUp=(c,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=Si(c.type==="pointercancel"?this.lastMoveEventInfo:Pi(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(c,g),m&&m(c,g)},!ir(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;const o=$e(t),a=Pi(o,this.transformPagePoint),{point:u}=a,{timestamp:h}=at;this.history=[{...u,timestamp:h}];const{onSessionStart:l}=e;l&&l(t,Si(a,this.history)),this.removeListeners=nr(De(this.contextWindow,"pointermove",this.handlePointerMove),De(this.contextWindow,"pointerup",this.handlePointerUp),De(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Jt(this.updatePoint)}}function Pi(n,t){return t?{point:t(n.point)}:n}function as(n,t){return{x:n.x-t.x,y:n.y-t.y}}function Si({point:n},t){return{point:n,delta:as(n,Lr(t)),offset:as(n,Fu(t)),velocity:Iu(t,.1)}}function Fu(n){return n[0]}function Lr(n){return n[n.length-1]}function Iu(n,t){if(n.length<2)return{x:0,y:0};let e=n.length-1,i=null;const s=Lr(n);for(;e>=0&&(i=n[e],!(s.timestamp-i.timestamp>Ke(t)));)e--;if(!i)return{x:0,y:0};const r=Jo(s.timestamp-i.timestamp);if(r===0)return{x:0,y:0};const o={x:(s.x-i.x)/r,y:(s.y-i.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function ju(n,{min:t,max:e},i){return t!==void 0&&n<t?n=i?Y(t,n,i.min):Math.max(n,t):e!==void 0&&n>e&&(n=i?Y(e,n,i.max):Math.min(n,e)),n}function us(n,t,e){return{min:t!==void 0?n.min+t:void 0,max:e!==void 0?n.max+e-(n.max-n.min):void 0}}function zu(n,{top:t,left:e,bottom:i,right:s}){return{x:us(n.x,e,s),y:us(n.y,t,i)}}function ls(n,t){let e=t.min-n.min,i=t.max-n.max;return t.max-t.min<n.max-n.min&&([e,i]=[i,e]),{min:e,max:i}}function Uu(n,t){return{x:ls(n.x,t.x),y:ls(n.y,t.y)}}function Nu(n,t){let e=.5;const i=et(n),s=et(t);return s>i?e=Bi(t.min,t.max-i,n.min):i>s&&(e=Bi(n.min,n.max-s,t.min)),sr(0,1,e)}function Wu(n,t){const e={};return t.min!==void 0&&(e.min=t.min-n.min),t.max!==void 0&&(e.max=t.max-n.min),e}const Wi=.35;function Gu(n=Wi){return n===!1?n=0:n===!0&&(n=Wi),{x:hs(n,"left","right"),y:hs(n,"top","bottom")}}function hs(n,t,e){return{min:cs(n,t),max:cs(n,e)}}function cs(n,t){return typeof n=="number"?n:n[t]||0}const $u=new WeakMap;class Yu{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=z(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const s=l=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor($e(l).point)},r=(l,c)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ta(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),mt(g=>{let _=this.getAxisMotionValue(g).get()||0;if(ni.test(_)){const{projection:y}=this.visualElement;if(y&&y.layout){const v=y.layout.layoutBox[g];v&&(_=et(v)*(parseFloat(_)/100))}}this.originPoint[g]=_}),m&&X.postRender(()=>m(l,c)),ji(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(l,c)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=c;if(d&&this.currentDirection===null){this.currentDirection=Xu(g),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",c.point,g),this.updateAxis("y",c.point,g),this.visualElement.render(),p&&p(l,c)},a=(l,c)=>this.stop(l,c),u=()=>mt(l=>{var c;return this.getAnimationState(l)==="paused"&&((c=this.getAxisMotionValue(l).animation)==null?void 0:c.play())}),{dragSnapToOrigin:h}=this.getProps();this.panSession=new Er(t,{onSessionStart:s,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:h,contextWindow:kr(this.visualElement)})}stop(t,e){const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:s}=e;this.startAnimation(s);const{onDragEnd:r}=this.getProps();r&&X.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){const{drag:s}=this.getProps();if(!i||!He(t,s,this.currentDirection))return;const r=this.getAxisMotionValue(t);let o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=ju(o,this.constraints[t],this.elastic[t])),r.set(o)}resolveConstraints(){var r;const{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(r=this.visualElement.projection)==null?void 0:r.layout,s=this.constraints;t&&ne(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=zu(i.layoutBox,t):this.constraints=!1,this.elastic=Gu(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&mt(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Wu(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!ne(t))return!1;const i=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const r=Lu(i,s.root,this.visualElement.getTransformPagePoint());let o=Uu(s.layout.layoutBox,r);if(e){const a=e(bu(o));this.hasMutatedConstraints=!!a,a&&(o=Ar(a))}return o}startAnimation(t){const{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),u=this.constraints||{},h=mt(l=>{if(!He(l,e,this.currentDirection))return;let c=u&&u[l]||{};o&&(c={min:0,max:0});const f=s?200:1e6,d=s?40:1e7,m={type:"inertia",velocity:i?t[l]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...c};return this.startAxisValueAnimation(l,m)});return Promise.all(h).then(a)}startAxisValueAnimation(t,e){const i=this.getAxisMotionValue(t);return ji(this.visualElement,t),i.start(Cn(t,i,0,e,this.visualElement,!1))}stopAnimation(){mt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){mt(t=>{var e;return(e=this.getAxisMotionValue(t).animation)==null?void 0:e.pause()})}getAnimationState(t){var e;return(e=this.getAxisMotionValue(t).animation)==null?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps(),s=i[e];return s||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){mt(e=>{const{drag:i}=this.getProps();if(!He(e,i,this.currentDirection))return;const{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[e];r.set(t[e]-Y(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ne(e)||!i||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};mt(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const u=a.get();s[o]=Nu({min:u,max:u},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),mt(o=>{if(!He(o,t,null))return;const a=this.getAxisMotionValue(o),{min:u,max:h}=this.constraints[o];a.set(Y(u,h,s[o]))})}addListeners(){if(!this.visualElement.current)return;$u.set(this.visualElement,this);const t=this.visualElement.current,e=De(t,"pointerdown",u=>{const{drag:h,dragListener:l=!0}=this.getProps();h&&l&&this.start(u)}),i=()=>{const{dragConstraints:u}=this.getProps();ne(u)&&u.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,r=s.addEventListener("measure",i);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),X.read(i);const o=Fe(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h})=>{this.isDragging&&h&&(mt(l=>{const c=this.getAxisMotionValue(l);c&&(this.originPoint[l]+=u[l].translate,c.set(c.get()+u[l].translate))}),this.visualElement.render())});return()=>{o(),e(),r(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:o=Wi,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function He(n,t,e){return(t===!0||t===n)&&(e===null||e===n)}function Xu(n,t=10){let e=null;return Math.abs(n.y)>t?e="y":Math.abs(n.x)>t&&(e="x"),e}class Hu extends jt{constructor(t){super(t),this.removeGroupControls=le,this.removeListeners=le,this.controls=new Yu(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||le}unmount(){this.removeGroupControls(),this.removeListeners()}}const fs=n=>(t,e)=>{n&&X.postRender(()=>n(t,e))};class qu extends jt{constructor(){super(...arguments),this.removePointerDownListener=le}onPointerDown(t){this.session=new Er(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:kr(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:fs(t),onStart:fs(e),onMove:i,onEnd:(r,o)=>{delete this.session,s&&X.postRender(()=>s(r,o))}}}mount(){this.removePointerDownListener=De(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Qe={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ds(n,t){return t.max===t.min?0:n/(t.max-t.min)*100}const Se={correct:(n,t)=>{if(!t.target)return n;if(typeof n=="string")if(Ae.test(n))n=parseFloat(n);else return n;const e=ds(n,t.target.x),i=ds(n,t.target.y);return`${e}% ${i}%`}},Ku={correct:(n,{treeScale:t,projectionDelta:e})=>{const i=n,s=Fi.parse(n);if(s.length>5)return i;const r=Fi.createTransformer(n),o=typeof s[0]!="number"?1:0,a=e.x.scale*t.x,u=e.y.scale*t.y;s[0+o]/=a,s[1+o]/=u;const h=Y(a,u,.5);return typeof s[2+o]=="number"&&(s[2+o]/=h),typeof s[3+o]=="number"&&(s[3+o]/=h),r(s)}};class Zu extends b.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;La(Qu),r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),Qe.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,{projection:o}=i;return o&&(o.isPresent=r,s||t.layoutDependency!==e||e===void 0||t.isPresent!==r?o.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?o.promote():o.relegate()||X.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),un.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Br(n){const[t,e]=lr(),i=b.useContext(dn);return Dt.jsx(Zu,{...n,layoutGroup:i,switchLayoutGroup:b.useContext(pr),isPresent:t,safeToRemove:e})}const Qu={borderRadius:{...Se,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Se,borderTopRightRadius:Se,borderBottomLeftRadius:Se,borderBottomRightRadius:Se,boxShadow:Ku};function Ju(n,t,e){const i=Z(n)?n:de(n);return i.start(Cn("",i,t,e)),i.animation}const tl=(n,t)=>n.depth-t.depth;class el{constructor(){this.children=[],this.isDirty=!1}add(t){rr(this.children,t),this.isDirty=!0}remove(t){or(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tl),this.isDirty=!1,this.children.forEach(t)}}function il(n,t){const e=fn.now(),i=({timestamp:s})=>{const r=s-e;r>=t&&(Jt(i),n(r-t))};return X.setup(i,!0),()=>Jt(i)}const Fr=["TopLeft","TopRight","BottomLeft","BottomRight"],nl=Fr.length,ps=n=>typeof n=="string"?parseFloat(n):n,ms=n=>typeof n=="number"||Ae.test(n);function sl(n,t,e,i,s,r){s?(n.opacity=Y(0,e.opacity??1,rl(i)),n.opacityExit=Y(t.opacity??1,0,ol(i))):r&&(n.opacity=Y(t.opacity??1,e.opacity??1,i));for(let o=0;o<nl;o++){const a=`border${Fr[o]}Radius`;let u=gs(t,a),h=gs(e,a);if(u===void 0&&h===void 0)continue;u||(u=0),h||(h=0),u===0||h===0||ms(u)===ms(h)?(n[a]=Math.max(Y(ps(u),ps(h),i),0),(ni.test(h)||ni.test(u))&&(n[a]+="%")):n[a]=h}(t.rotate||e.rotate)&&(n.rotate=Y(t.rotate||0,e.rotate||0,i))}function gs(n,t){return n[t]!==void 0?n[t]:n.borderRadius}const rl=Ir(0,.5,ea),ol=Ir(.5,.95,le);function Ir(n,t,e){return i=>i<n?0:i>t?1:e(Bi(n,t,i))}function _s(n,t){n.min=t.min,n.max=t.max}function pt(n,t){_s(n.x,t.x),_s(n.y,t.y)}function ys(n,t){n.translate=t.translate,n.scale=t.scale,n.originPoint=t.originPoint,n.origin=t.origin}function vs(n,t,e,i,s){return n-=t,n=ri(n,1/e,i),s!==void 0&&(n=ri(n,1/s,i)),n}function al(n,t=0,e=1,i=.5,s,r=n,o=n){if(ni.test(t)&&(t=parseFloat(t),t=Y(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=Y(r.min,r.max,i);n===r&&(a-=t),n.min=vs(n.min,t,e,a,s),n.max=vs(n.max,t,e,a,s)}function xs(n,t,[e,i,s],r,o){al(n,t[e],t[i],t[s],t.scale,r,o)}const ul=["x","scaleX","originX"],ll=["y","scaleY","originY"];function Ts(n,t,e,i){xs(n.x,t,ul,e?e.x:void 0,i?i.x:void 0),xs(n.y,t,ll,e?e.y:void 0,i?i.y:void 0)}function Ps(n){return n.translate===0&&n.scale===1}function jr(n){return Ps(n.x)&&Ps(n.y)}function Ss(n,t){return n.min===t.min&&n.max===t.max}function hl(n,t){return Ss(n.x,t.x)&&Ss(n.y,t.y)}function ws(n,t){return Math.round(n.min)===Math.round(t.min)&&Math.round(n.max)===Math.round(t.max)}function zr(n,t){return ws(n.x,t.x)&&ws(n.y,t.y)}function Cs(n){return et(n.x)/et(n.y)}function bs(n,t){return n.translate===t.translate&&n.scale===t.scale&&n.originPoint===t.originPoint}class cl{constructor(){this.members=[]}add(t){rr(this.members,t),t.scheduleRender()}remove(t){if(or(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(t){const e=this.members.findIndex(s=>t===s);if(e===0)return!1;let i;for(let s=e;s>=0;s--){const r=this.members[s];if(r.isPresent!==!1){i=r;break}}return i?(this.promote(i),!0):!1}promote(t,e){const i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function fl(n,t,e){let i="";const s=n.x.translate/t.x,r=n.y.translate/t.y,o=(e==null?void 0:e.z)||0;if((s||r||o)&&(i=`translate3d(${s}px, ${r}px, ${o}px) `),(t.x!==1||t.y!==1)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),e){const{transformPerspective:h,rotate:l,rotateX:c,rotateY:f,skewX:d,skewY:m}=e;h&&(i=`perspective(${h}px) ${i}`),l&&(i+=`rotate(${l}deg) `),c&&(i+=`rotateX(${c}deg) `),f&&(i+=`rotateY(${f}deg) `),d&&(i+=`skewX(${d}deg) `),m&&(i+=`skewY(${m}deg) `)}const a=n.x.scale*t.x,u=n.y.scale*t.y;return(a!==1||u!==1)&&(i+=`scale(${a}, ${u})`),i||"none"}const wi=["","X","Y","Z"],dl={visibility:"hidden"},pl=1e3;let ml=0;function Ci(n,t,e,i){const{latestValues:s}=t;s[n]&&(e[n]=s[n],t.setStaticValue(n,0),i&&(i[n]=0))}function Ur(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:t}=n.options;if(!t)return;const e=Sr(t);if(window.MotionHasOptimisedAnimation(e,"transform")){const{layout:s,layoutId:r}=n.options;window.MotionCancelOptimisedAnimation(e,"transform",X,!(s||r))}const{parent:i}=n;i&&!i.hasCheckedOptimisedAppear&&Ur(i)}function Nr({attachResizeListener:n,defaultParent:t,measureScroll:e,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=ml++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(yl),this.nodes.forEach(Sl),this.nodes.forEach(wl),this.nodes.forEach(vl)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new el)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new ar),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const u=this.eventHandlers.get(o);u&&u.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=ia(o)&&!na(o),this.instance=o;const{layoutId:a,layout:u,visualElement:h}=this.options;if(h&&!h.current&&h.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(u||a)&&(this.isLayoutDirty=!0),n){let l;const c=()=>this.root.updateBlockedByResize=!1;n(o,()=>{this.root.updateBlockedByResize=!0,l&&l(),l=il(c,250),Qe.hasAnimatedSinceResize&&(Qe.hasAnimatedSinceResize=!1,this.nodes.forEach(Ds))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&h&&(a||u)&&this.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||h.getDefaultTransition()||Ml,{onLayoutAnimationStart:p,onLayoutAnimationComplete:g}=h.getProps(),_=!this.targetLayout||!zr(this.targetLayout,d),y=!c&&f;if(this.options.layoutRoot||this.resumeFrom||y||c&&(_||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...cn(m,"layout"),onPlay:p,onComplete:g};(h.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(l,y)}else c||Ds(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Jt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Cl),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ur(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){const c=this.path[l];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:a,layout:u}=this.options;if(a===void 0&&!u)return;const h=this.getTransformTemplate();this.prevTransformTemplateValue=h?h(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(As);return}this.isUpdating||this.nodes.forEach(Tl),this.isUpdating=!1,this.nodes.forEach(Pl),this.nodes.forEach(gl),this.nodes.forEach(_l),this.clearAllSnapshots();const a=fn.now();at.delta=sr(0,1e3/60,a-at.timestamp),at.timestamp=a,at.isProcessing=!0,xi.update.process(at),xi.preRender.process(at),xi.render.process(at),at.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,un.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(xl),this.sharedNodes.forEach(bl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,X.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){X.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!et(this.snapshot.measuredBox.x)&&!et(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=z(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&this.instance){const u=i(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:u,offset:e(this.instance),wasRoot:this.scroll?this.scroll.isRoot:u}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!jr(this.projectionDelta),u=this.getTransformTemplate(),h=u?u(this.latestValues,""):void 0,l=h!==this.prevTransformTemplateValue;o&&this.instance&&(a||Yt(this.latestValues)||l)&&(s(this.instance,h),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let u=this.removeElementScroll(a);return o&&(u=this.removeTransform(u)),Vl(u),{animationId:this.root.animationId,measuredBox:a,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){var h;const{visualElement:o}=this.options;if(!o)return z();const a=o.measureViewportBox();if(!(((h=this.scroll)==null?void 0:h.wasRoot)||this.path.some(Rl))){const{scroll:l}=this.root;l&&(re(a.x,l.offset.x),re(a.y,l.offset.y))}return a}removeElementScroll(o){var u;const a=z();if(pt(a,o),(u=this.scroll)!=null&&u.wasRoot)return a;for(let h=0;h<this.path.length;h++){const l=this.path[h],{scroll:c,options:f}=l;l!==this.root&&c&&f.layoutScroll&&(c.wasRoot&&pt(a,o),re(a.x,c.offset.x),re(a.y,c.offset.y))}return a}applyTransform(o,a=!1){const u=z();pt(u,o);for(let h=0;h<this.path.length;h++){const l=this.path[h];!a&&l.options.layoutScroll&&l.scroll&&l!==l.root&&oe(u,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),Yt(l.latestValues)&&oe(u,l.latestValues)}return Yt(this.latestValues)&&oe(u,this.latestValues),u}removeTransform(o){const a=z();pt(a,o);for(let u=0;u<this.path.length;u++){const h=this.path[u];if(!h.instance||!Yt(h.latestValues))continue;Ui(h.latestValues)&&h.updateSnapshot();const l=z(),c=h.measurePageBox();pt(l,c),Ts(a,h.latestValues,h.snapshot?h.snapshot.layoutBox:void 0,l)}return Yt(this.latestValues)&&Ts(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==at.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:l,layoutId:c}=this.options;if(!(!this.layout||!(l||c))){if(this.resolvedRelativeTargetAt=at.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=z(),this.relativeTargetOrigin=z(),Ve(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),pt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=z(),this.targetWithTransforms=z()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ku(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):pt(this.target,this.layout.layoutBox),Rr(this.target,this.targetDelta)):pt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=z(),this.relativeTargetOrigin=z(),Ve(this.relativeTargetOrigin,this.target,d.target),pt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ui(this.parent.latestValues)||Vr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var m;const o=this.getLead(),a=!!this.resumingFrom||this!==o;let u=!0;if((this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===at.timestamp&&(u=!1),u)return;const{layout:h,layoutId:l}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(h||l))return;pt(this.layoutCorrected,this.layout.layoutBox);const c=this.treeScale.x,f=this.treeScale.y;Eu(this.layoutCorrected,this.treeScale,this.path,a),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=z());const{target:d}=o;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ys(this.prevProjectionDelta.x,this.projectionDelta.x),ys(this.prevProjectionDelta.y,this.projectionDelta.y)),Me(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==c||this.treeScale.y!==f||!bs(this.projectionDelta.x,this.prevProjectionDelta.x)||!bs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),o){const u=this.getStack();u&&u.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=se(),this.projectionDelta=se(),this.projectionDeltaWithTransform=se()}setAnimationOrigin(o,a=!1){const u=this.snapshot,h=u?u.latestValues:{},l={...this.latestValues},c=se();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=z(),d=u?u.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,g=this.getStack(),_=!g||g.members.length<=1,y=!!(p&&!_&&this.options.crossfade===!0&&!this.path.some(Dl));this.animationProgress=0;let v;this.mixTargetDelta=T=>{const x=T/1e3;Ms(c.x,o.x,x),Ms(c.y,o.y,x),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ve(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Al(this.relativeTarget,this.relativeTargetOrigin,f,x),v&&hl(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=z()),pt(v,this.relativeTarget)),p&&(this.animationValues=l,sl(l,h,this.latestValues,x,y,_)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var a,u,h;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(h=(u=this.resumingFrom)==null?void 0:u.currentAnimation)==null||h.stop(),this.pendingAnimation&&(Jt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=X.update(()=>{Qe.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=de(0)),this.currentAnimation=Ju(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(pl),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:u,layout:h,latestValues:l}=o;if(!(!a||!u||!h)){if(this!==o&&this.layout&&h&&Wr(this.options.animationType,this.layout.layoutBox,h.layoutBox)){u=this.target||z();const c=et(this.layout.layoutBox.x);u.x.min=o.target.x.min,u.x.max=u.x.min+c;const f=et(this.layout.layoutBox.y);u.y.min=o.target.y.min,u.y.max=u.y.min+f}pt(a,u),oe(a,l),Me(this.projectionDeltaWithTransform,this.layoutCorrected,a,l)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new cl),this.sharedNodes.get(o).add(a);const h=a.options.initialPromotionConfig;a.promote({transition:h?h.transition:void 0,preserveFollowOpacity:h&&h.shouldPreserveFollowOpacity?h.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var a;const{layoutId:o}=this.options;return o?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:o}=this.options;return o?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:u}={}){const h=this.getStack();h&&h.promote(this,u),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:u}=o;if((u.z||u.rotate||u.rotateX||u.rotateY||u.rotateZ||u.skewX||u.skewY)&&(a=!0),!a)return;const h={};u.z&&Ci("z",o,h,this.animationValues);for(let l=0;l<wi.length;l++)Ci(`rotate${wi[l]}`,o,h,this.animationValues),Ci(`skew${wi[l]}`,o,h,this.animationValues);o.render();for(const l in h)o.setStaticValue(l,h[l]),this.animationValues&&(this.animationValues[l]=h[l]);o.scheduleRender()}getProjectionStyles(o){if(!this.instance||this.isSVG)return;if(!this.isVisible)return dl;const a={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Ze(o==null?void 0:o.pointerEvents)||"",a.transform=u?u(this.latestValues,""):"none",a;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=Ze(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Yt(this.latestValues)&&(d.transform=u?u({},""):"none",this.hasProjected=!1),d}const l=h.animationValues||h.latestValues;this.applyTransformsToTarget(),a.transform=fl(this.projectionDeltaWithTransform,this.treeScale,l),u&&(a.transform=u(l,a.transform));const{x:c,y:f}=this.projectionDelta;a.transformOrigin=`${c.origin*100}% ${f.origin*100}% 0`,h.animationValues?a.opacity=h===this?l.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:l.opacityExit:a.opacity=h===this?l.opacity!==void 0?l.opacity:"":l.opacityExit!==void 0?l.opacityExit:0;for(const d in Le){if(l[d]===void 0)continue;const{correct:m,applyTo:p,isCSSVariable:g}=Le[d],_=a.transform==="none"?l[d]:m(l[d],h);if(p){const y=p.length;for(let v=0;v<y;v++)a[p[v]]=_}else g?this.options.visualElement.renderState.vars[d]=_:a[d]=_}return this.options.layoutId&&(a.pointerEvents=h===this?Ze(o==null?void 0:o.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(As),this.root.sharedNodes.clear()}}}function gl(n){n.updateLayout()}function _l(n){var e;const t=((e=n.resumeFrom)==null?void 0:e.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&t&&n.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:s}=n.layout,{animationType:r}=n.options,o=t.source!==n.layout.source;r==="size"?mt(c=>{const f=o?t.measuredBox[c]:t.layoutBox[c],d=et(f);f.min=i[c].min,f.max=f.min+d}):Wr(r,t.layoutBox,i)&&mt(c=>{const f=o?t.measuredBox[c]:t.layoutBox[c],d=et(i[c]);f.max=f.min+d,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[c].max=n.relativeTarget[c].min+d)});const a=se();Me(a,i,t.layoutBox);const u=se();o?Me(u,n.applyTransform(s,!0),t.measuredBox):Me(u,i,t.layoutBox);const h=!jr(a);let l=!1;if(!n.resumeFrom){const c=n.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:f,layout:d}=c;if(f&&d){const m=z();Ve(m,t.layoutBox,f.layoutBox);const p=z();Ve(p,i,d.layoutBox),zr(m,p)||(l=!0),c.options.layoutRoot&&(n.relativeTarget=p,n.relativeTargetOrigin=m,n.relativeParent=c)}}}n.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:u,layoutDelta:a,hasLayoutChanged:h,hasRelativeLayoutChanged:l})}else if(n.isLead()){const{onExitComplete:i}=n.options;i&&i()}n.options.transition=void 0}function yl(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function vl(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function xl(n){n.clearSnapshot()}function As(n){n.clearMeasurements()}function Tl(n){n.isLayoutDirty=!1}function Pl(n){const{visualElement:t}=n.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),n.resetTransform()}function Ds(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function Sl(n){n.resolveTargetDelta()}function wl(n){n.calcProjection()}function Cl(n){n.resetSkewAndRotation()}function bl(n){n.removeLeadSnapshot()}function Ms(n,t,e){n.translate=Y(t.translate,0,e),n.scale=Y(t.scale,1,e),n.origin=t.origin,n.originPoint=t.originPoint}function Vs(n,t,e,i){n.min=Y(t.min,e.min,i),n.max=Y(t.max,e.max,i)}function Al(n,t,e,i){Vs(n.x,t.x,e.x,i),Vs(n.y,t.y,e.y,i)}function Dl(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const Ml={duration:.45,ease:[.4,0,.1,1]},Rs=n=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),Os=Rs("applewebkit/")&&!Rs("chrome/")?Math.round:le;function ks(n){n.min=Os(n.min),n.max=Os(n.max)}function Vl(n){ks(n.x),ks(n.y)}function Wr(n,t,e){return n==="position"||n==="preserve-aspect"&&!Ou(Cs(t),Cs(e),.2)}function Rl(n){var t;return n!==n.root&&((t=n.scroll)==null?void 0:t.wasRoot)}const Ol=Nr({attachResizeListener:(n,t)=>Fe(n,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),bi={current:void 0},Gr=Nr({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!bi.current){const n=new Ol({});n.mount(window),n.setOptions({layoutScroll:!0}),bi.current=n}return bi.current},resetTransform:(n,t)=>{n.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),kl={pan:{Feature:qu},drag:{Feature:Hu,ProjectionNode:Gr,MeasureLayout:Br}};function Es(n,t,e){const{props:i}=n;n.animationState&&i.whileHover&&n.animationState.setActive("whileHover",e==="Start");const s="onHover"+e,r=i[s];r&&X.postRender(()=>r(t,$e(t)))}class El extends jt{mount(){const{current:t}=this.node;t&&(this.unmount=sa(t,(e,i)=>(Es(this.node,i,"Start"),s=>Es(this.node,s,"End"))))}unmount(){}}class Ll extends jt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=nr(Fe(this.node.current,"focus",()=>this.onFocus()),Fe(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ls(n,t,e){const{props:i}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&i.whileTap&&n.animationState.setActive("whileTap",e==="Start");const s="onTap"+(e==="End"?"":e),r=i[s];r&&X.postRender(()=>r(t,$e(t)))}class Bl extends jt{mount(){const{current:t}=this.node;t&&(this.unmount=ra(t,(e,i)=>(Ls(this.node,i,"Start"),(s,{success:r})=>Ls(this.node,s,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Gi=new WeakMap,Ai=new WeakMap,Fl=n=>{const t=Gi.get(n.target);t&&t(n)},Il=n=>{n.forEach(Fl)};function jl({root:n,...t}){const e=n||document;Ai.has(e)||Ai.set(e,{});const i=Ai.get(e),s=JSON.stringify(t);return i[s]||(i[s]=new IntersectionObserver(Il,{root:n,...t})),i[s]}function zl(n,t,e){const i=jl(t);return Gi.set(n,e),i.observe(n),()=>{Gi.delete(n),i.unobserve(n)}}const Ul={some:0,all:1};class Nl extends jt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:typeof s=="number"?s:Ul[s]},a=u=>{const{isIntersecting:h}=u;if(this.isInView===h||(this.isInView=h,r&&!h&&this.hasEnteredView))return;h&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",h);const{onViewportEnter:l,onViewportLeave:c}=this.node.getProps(),f=h?l:c;f&&f(u)};return zl(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(Wl(t,e))&&this.startObserver()}unmount(){}}function Wl({viewport:n={}},{viewport:t={}}={}){return e=>n[e]!==t[e]}const Gl={inView:{Feature:Nl},tap:{Feature:Bl},focus:{Feature:Ll},hover:{Feature:El}},$l={layout:{ProjectionNode:Gr,MeasureLayout:Br}},$i={current:null},$r={current:!1};function Yl(){if($r.current=!0,!!mn)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),t=()=>$i.current=n.matches;n.addListener(t),t()}else $i.current=!1}const Xl=new WeakMap;function Hl(n,t,e){for(const i in t){const s=t[i],r=e[i];if(Z(s))n.addValue(i,s);else if(Z(r))n.addValue(i,de(s,{owner:n}));else if(r!==s)if(n.hasValue(i)){const o=n.getValue(i);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=n.getStaticValue(i);n.addValue(i,de(o!==void 0?o:s,{owner:n}))}}for(const i in e)t[i]===void 0&&n.removeValue(i);return t}const Bs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ql{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=oa,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=fn.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,X.render(this.render,!1,!0))};const{latestValues:u,renderState:h}=o;this.latestValues=u,this.baseTarget={...u},this.initialValues=e.initial?{...u}:{},this.renderState=h,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=gi(e),this.isVariantNode=fr(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:l,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const f in c){const d=c[f];u[f]!==void 0&&Z(d)&&d.set(u[f],!1)}}mount(t){this.current=t,Xl.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,i)=>this.bindToMotionValue(i,e)),$r.current||Yl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:$i.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Jt(this.notifyUpdate),Jt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const i=Te.has(t);i&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&X.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in pe){const e=pe[t];if(!e)continue;const{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){const r=this.features[t];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):z()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let i=0;i<Bs.length;i++){const s=Bs[i];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const r="on"+s,o=t[r];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=Hl(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return i===void 0&&e!==void 0&&(i=de(e===null?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(aa(i)||ua(i))?i=parseFloat(i):!la(i)&&Fi.test(e)&&(i=ha(t,e)),this.setBaseTarget(t,Z(i)?i.get():i)),Z(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var r;const{initial:e}=this.props;let i;if(typeof e=="string"||typeof e=="object"){const o=Sn(this.props,e,(r=this.presenceContext)==null?void 0:r.custom);o&&(i=o[t])}if(e&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!Z(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,e){return this.events[t]||(this.events[t]=new ar),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Yr extends ql{constructor(){super(...arguments),this.KeyframeResolver=ca}sortInstanceNodePosition(t,e){return t.compareDocumentPosition(e)&2?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Z(t)&&(this.childSubscription=t.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function Xr(n,{style:t,vars:e},i,s){Object.assign(n.style,t,s&&s.getProjectionStyles(i));for(const r in e)n.style.setProperty(r,e[r])}function Kl(n){return window.getComputedStyle(n)}class Zl extends Yr{constructor(){super(...arguments),this.type="html",this.renderInstance=Xr}readValueFromInstance(t,e){var i;if(Te.has(e))return(i=this.projection)!=null&&i.isProjecting?fa(e):da(t,e);{const s=Kl(t),r=(ln(e)?s.getPropertyValue(e):s[e])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return Or(t,e)}build(t,e,i){xn(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return wn(t,e,i)}}const Hr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ql(n,t,e,i){Xr(n,t,void 0,i);for(const s in t.attrs)n.setAttribute(Hr.has(s)?s:vn(s),t.attrs[s])}class Jl extends Yr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=z}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Te.has(e)){const i=pa(e);return i&&i.default||0}return e=Hr.has(e)?e:vn(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return Pr(t,e,i)}build(t,e,i){yr(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){Ql(t,e,i,s)}mount(t){this.isSVGTag=xr(t.tagName),super.mount(t)}}const th=(n,t)=>Pn(n)?new Jl(t):new Zl(t,{allowProjection:n!==b.Fragment}),eh=Qa({...wu,...Gl,...kl,...$l},th),Tc=Sa(eh);function bt(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function qr(n,t){n.prototype=Object.create(t.prototype),n.prototype.constructor=n,n.__proto__=t}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var ct={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},me={duration:.5,overwrite:!1,delay:0},bn,q,E,Pt=1e8,Q=1/Pt,Yi=Math.PI*2,ih=Yi/4,nh=0,Kr=Math.sqrt,sh=Math.cos,rh=Math.sin,H=function(t){return typeof t=="string"},F=function(t){return typeof t=="function"},Mt=function(t){return typeof t=="number"},An=function(t){return typeof t>"u"},St=function(t){return typeof t=="object"},it=function(t){return t!==!1},Dn=function(){return typeof window<"u"},qe=function(t){return F(t)||H(t)},Zr=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},J=Array.isArray,Xi=/(?:-?\.?\d|\.)+/gi,Qr=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,ae=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Di=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,Jr=/[+-]=-?[.\d]+/,to=/[^,'"\[\]\s]+/gi,oh=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,L,vt,Hi,Mn,ft={},oi={},eo,io=function(t){return(oi=ge(t,ft))&&ot},Vn=function(t,e){return console.warn("Invalid property",t,"set to",e,"Missing plugin? gsap.registerPlugin()")},Ie=function(t,e){return!e&&console.warn(t)},no=function(t,e){return t&&(ft[t]=e)&&oi&&(oi[t]=e)||ft},je=function(){return 0},ah={suppressEvents:!0,isStart:!0,kill:!1},Je={suppressEvents:!0,kill:!1},uh={suppressEvents:!0},Rn={},Lt=[],qi={},so,ut={},Mi={},Fs=30,ti=[],On="",kn=function(t){var e=t[0],i,s;if(St(e)||F(e)||(t=[t]),!(i=(e._gsap||{}).harness)){for(s=ti.length;s--&&!ti[s].targetTest(e););i=ti[s]}for(s=t.length;s--;)t[s]&&(t[s]._gsap||(t[s]._gsap=new Mo(t[s],i)))||t.splice(s,1);return t},qt=function(t){return t._gsap||kn(_t(t))[0]._gsap},ro=function(t,e,i){return(i=t[e])&&F(i)?t[e]():An(i)&&t.getAttribute&&t.getAttribute(e)||i},nt=function(t,e){return(t=t.split(",")).forEach(e)||t},U=function(t){return Math.round(t*1e5)/1e5||0},G=function(t){return Math.round(t*1e7)/1e7||0},he=function(t,e){var i=e.charAt(0),s=parseFloat(e.substr(2));return t=parseFloat(t),i==="+"?t+s:i==="-"?t-s:i==="*"?t*s:t/s},lh=function(t,e){for(var i=e.length,s=0;t.indexOf(e[s])<0&&++s<i;);return s<i},ai=function(){var t=Lt.length,e=Lt.slice(0),i,s;for(qi={},Lt.length=0,i=0;i<t;i++)s=e[i],s&&s._lazy&&(s.render(s._lazy[0],s._lazy[1],!0)._lazy=0)},En=function(t){return!!(t._initted||t._startAt||t.add)},oo=function(t,e,i,s){Lt.length&&!q&&ai(),t.render(e,i,!!(q&&e<0&&En(t))),Lt.length&&!q&&ai()},ao=function(t){var e=parseFloat(t);return(e||e===0)&&(t+"").match(to).length<2?e:H(t)?t.trim():t},uo=function(t){return t},dt=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},hh=function(t){return function(e,i){for(var s in i)s in e||s==="duration"&&t||s==="ease"||(e[s]=i[s])}},ge=function(t,e){for(var i in e)t[i]=e[i];return t},Is=function n(t,e){for(var i in e)i!=="__proto__"&&i!=="constructor"&&i!=="prototype"&&(t[i]=St(e[i])?n(t[i]||(t[i]={}),e[i]):e[i]);return t},ui=function(t,e){var i={},s;for(s in t)s in e||(i[s]=t[s]);return i},Re=function(t){var e=t.parent||L,i=t.keyframes?hh(J(t.keyframes)):dt;if(it(t.inherit))for(;e;)i(t,e.vars.defaults),e=e.parent||e._dp;return t},ch=function(t,e){for(var i=t.length,s=i===e.length;s&&i--&&t[i]===e[i];);return i<0},lo=function(t,e,i,s,r){var o=t[s],a;if(r)for(a=e[r];o&&o[r]>a;)o=o._prev;return o?(e._next=o._next,o._next=e):(e._next=t[i],t[i]=e),e._next?e._next._prev=e:t[s]=e,e._prev=o,e.parent=e._dp=t,e},_i=function(t,e,i,s){i===void 0&&(i="_first"),s===void 0&&(s="_last");var r=e._prev,o=e._next;r?r._next=o:t[i]===e&&(t[i]=o),o?o._prev=r:t[s]===e&&(t[s]=r),e._next=e._prev=e.parent=null},Ft=function(t,e){t.parent&&(!e||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},Kt=function(t,e){if(t&&(!e||e._end>t._dur||e._start<0))for(var i=t;i;)i._dirty=1,i=i.parent;return t},fh=function(t){for(var e=t.parent;e&&e.parent;)e._dirty=1,e.totalDuration(),e=e.parent;return t},Ki=function(t,e,i,s){return t._startAt&&(q?t._startAt.revert(Je):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(e,!0,s))},dh=function n(t){return!t||t._ts&&n(t.parent)},js=function(t){return t._repeat?_e(t._tTime,t=t.duration()+t._rDelay)*t:0},_e=function(t,e){var i=Math.floor(t=G(t/e));return t&&i===t?i-1:i},li=function(t,e){return(t-e._start)*e._ts+(e._ts>=0?0:e._dirty?e.totalDuration():e._tDur)},yi=function(t){return t._end=G(t._start+(t._tDur/Math.abs(t._ts||t._rts||Q)||0))},vi=function(t,e){var i=t._dp;return i&&i.smoothChildTiming&&t._ts&&(t._start=G(i._time-(t._ts>0?e/t._ts:((t._dirty?t.totalDuration():t._tDur)-e)/-t._ts)),yi(t),i._dirty||Kt(i,t)),t},ho=function(t,e){var i;if((e._time||!e._dur&&e._initted||e._start<t._time&&(e._dur||!e.add))&&(i=li(t.rawTime(),e),(!e._dur||Ye(0,e.totalDuration(),i)-e._tTime>Q)&&e.render(i,!0)),Kt(t,e)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(i=t;i._dp;)i.rawTime()>=0&&i.totalTime(i._tTime),i=i._dp;t._zTime=-1e-8}},xt=function(t,e,i,s){return e.parent&&Ft(e),e._start=G((Mt(i)?i:i||t!==L?gt(t,i,e):t._time)+e._delay),e._end=G(e._start+(e.totalDuration()/Math.abs(e.timeScale())||0)),lo(t,e,"_first","_last",t._sort?"_start":0),Zi(e)||(t._recent=e),s||ho(t,e),t._ts<0&&vi(t,t._tTime),t},co=function(t,e){return(ft.ScrollTrigger||Vn("scrollTrigger",e))&&ft.ScrollTrigger.create(e,t)},fo=function(t,e,i,s,r){if(Bn(t,e,r),!t._initted)return 1;if(!i&&t._pt&&!q&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&so!==lt.frame)return Lt.push(t),t._lazy=[r,s],1},ph=function n(t){var e=t.parent;return e&&e._ts&&e._initted&&!e._lock&&(e.rawTime()<0||n(e))},Zi=function(t){var e=t.data;return e==="isFromStart"||e==="isStart"},mh=function(t,e,i,s){var r=t.ratio,o=e<0||!e&&(!t._start&&ph(t)&&!(!t._initted&&Zi(t))||(t._ts<0||t._dp._ts<0)&&!Zi(t))?0:1,a=t._rDelay,u=0,h,l,c;if(a&&t._repeat&&(u=Ye(0,t._tDur,e),l=_e(u,a),t._yoyo&&l&1&&(o=1-o),l!==_e(t._tTime,a)&&(r=1-o,t.vars.repeatRefresh&&t._initted&&t.invalidate())),o!==r||q||s||t._zTime===Q||!e&&t._zTime){if(!t._initted&&fo(t,e,s,i,u))return;for(c=t._zTime,t._zTime=e||(i?Q:0),i||(i=e&&!c),t.ratio=o,t._from&&(o=1-o),t._time=0,t._tTime=u,h=t._pt;h;)h.r(o,h.d),h=h._next;e<0&&Ki(t,e,i,!0),t._onUpdate&&!i&&ht(t,"onUpdate"),u&&t._repeat&&!i&&t.parent&&ht(t,"onRepeat"),(e>=t._tDur||e<0)&&t.ratio===o&&(o&&Ft(t,1),!i&&!q&&(ht(t,o?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=e)},gh=function(t,e,i){var s;if(i>e)for(s=t._first;s&&s._start<=i;){if(s.data==="isPause"&&s._start>e)return s;s=s._next}else for(s=t._last;s&&s._start>=i;){if(s.data==="isPause"&&s._start<e)return s;s=s._prev}},ye=function(t,e,i,s){var r=t._repeat,o=G(e)||0,a=t._tTime/t._tDur;return a&&!s&&(t._time*=o/t._dur),t._dur=o,t._tDur=r?r<0?1e10:G(o*(r+1)+t._rDelay*r):o,a>0&&!s&&vi(t,t._tTime=t._tDur*a),t.parent&&yi(t),i||Kt(t.parent,t),t},zs=function(t){return t instanceof tt?Kt(t):ye(t,t._dur)},_h={_start:0,endTime:je,totalDuration:je},gt=function n(t,e,i){var s=t.labels,r=t._recent||_h,o=t.duration()>=Pt?r.endTime(!1):t._dur,a,u,h;return H(e)&&(isNaN(e)||e in s)?(u=e.charAt(0),h=e.substr(-1)==="%",a=e.indexOf("="),u==="<"||u===">"?(a>=0&&(e=e.replace(/=/,"")),(u==="<"?r._start:r.endTime(r._repeat>=0))+(parseFloat(e.substr(1))||0)*(h?(a<0?r:i).totalDuration()/100:1)):a<0?(e in s||(s[e]=o),s[e]):(u=parseFloat(e.charAt(a-1)+e.substr(a+1)),h&&i&&(u=u/100*(J(i)?i[0]:i).totalDuration()),a>1?n(t,e.substr(0,a-1),i)+u:o+u)):e==null?o:+e},Oe=function(t,e,i){var s=Mt(e[1]),r=(s?2:1)+(t<2?0:1),o=e[r],a,u;if(s&&(o.duration=e[1]),o.parent=i,t){for(a=o,u=i;u&&!("immediateRender"in a);)a=u.vars.defaults||{},u=it(u.vars.inherit)&&u.parent;o.immediateRender=it(a.immediateRender),t<2?o.runBackwards=1:o.startAt=e[r-1]}return new W(e[0],o,e[r+1])},zt=function(t,e){return t||t===0?e(t):e},Ye=function(t,e,i){return i<t?t:i>e?e:i},K=function(t,e){return!H(t)||!(e=oh.exec(t))?"":e[1]},yh=function(t,e,i){return zt(i,function(s){return Ye(t,e,s)})},Qi=[].slice,po=function(t,e){return t&&St(t)&&"length"in t&&(!e&&!t.length||t.length-1 in t&&St(t[0]))&&!t.nodeType&&t!==vt},vh=function(t,e,i){return i===void 0&&(i=[]),t.forEach(function(s){var r;return H(s)&&!e||po(s,1)?(r=i).push.apply(r,_t(s)):i.push(s)})||i},_t=function(t,e,i){return E&&!e&&E.selector?E.selector(t):H(t)&&!i&&(Hi||!ve())?Qi.call((e||Mn).querySelectorAll(t),0):J(t)?vh(t,i):po(t)?Qi.call(t,0):t?[t]:[]},Ji=function(t){return t=_t(t)[0]||Ie("Invalid scope")||{},function(e){var i=t.current||t.nativeElement||t;return _t(e,i.querySelectorAll?i:i===t?Ie("Invalid scope")||Mn.createElement("div"):t)}},mo=function(t){return t.sort(function(){return .5-Math.random()})},go=function(t){if(F(t))return t;var e=St(t)?t:{each:t},i=Zt(e.ease),s=e.from||0,r=parseFloat(e.base)||0,o={},a=s>0&&s<1,u=isNaN(s)||a,h=e.axis,l=s,c=s;return H(s)?l=c={center:.5,edges:.5,end:1}[s]||0:!a&&u&&(l=s[0],c=s[1]),function(f,d,m){var p=(m||e).length,g=o[p],_,y,v,T,x,P,w,C,S;if(!g){if(S=e.grid==="auto"?0:(e.grid||[1,Pt])[1],!S){for(w=-1e8;w<(w=m[S++].getBoundingClientRect().left)&&S<p;);S<p&&S--}for(g=o[p]=[],_=u?Math.min(S,p)*l-.5:s%S,y=S===Pt?0:u?p*c/S-.5:s/S|0,w=0,C=Pt,P=0;P<p;P++)v=P%S-_,T=y-(P/S|0),g[P]=x=h?Math.abs(h==="y"?T:v):Kr(v*v+T*T),x>w&&(w=x),x<C&&(C=x);s==="random"&&mo(g),g.max=w-C,g.min=C,g.v=p=(parseFloat(e.amount)||parseFloat(e.each)*(S>p?p-1:h?h==="y"?p/S:S:Math.max(S,p/S))||0)*(s==="edges"?-1:1),g.b=p<0?r-p:r,g.u=K(e.amount||e.each)||0,i=i&&p<0?bo(i):i}return p=(g[f]-g.min)/g.max||0,G(g.b+(i?i(p):p)*g.v)+g.u}},tn=function(t){var e=Math.pow(10,((t+"").split(".")[1]||"").length);return function(i){var s=G(Math.round(parseFloat(i)/t)*t*e);return(s-s%1)/e+(Mt(i)?0:K(i))}},_o=function(t,e){var i=J(t),s,r;return!i&&St(t)&&(s=i=t.radius||Pt,t.values?(t=_t(t.values),(r=!Mt(t[0]))&&(s*=s)):t=tn(t.increment)),zt(e,i?F(t)?function(o){return r=t(o),Math.abs(r-o)<=s?r:o}:function(o){for(var a=parseFloat(r?o.x:o),u=parseFloat(r?o.y:0),h=Pt,l=0,c=t.length,f,d;c--;)r?(f=t[c].x-a,d=t[c].y-u,f=f*f+d*d):f=Math.abs(t[c]-a),f<h&&(h=f,l=c);return l=!s||h<=s?t[l]:o,r||l===o||Mt(o)?l:l+K(o)}:tn(t))},yo=function(t,e,i,s){return zt(J(t)?!e:i===!0?!!(i=0):!s,function(){return J(t)?t[~~(Math.random()*t.length)]:(i=i||1e-5)&&(s=i<1?Math.pow(10,(i+"").length-2):1)&&Math.floor(Math.round((t-i/2+Math.random()*(e-t+i*.99))/i)*i*s)/s})},xh=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(s){return e.reduce(function(r,o){return o(r)},s)}},Th=function(t,e){return function(i){return t(parseFloat(i))+(e||K(i))}},Ph=function(t,e,i){return xo(t,e,0,1,i)},vo=function(t,e,i){return zt(i,function(s){return t[~~e(s)]})},Sh=function n(t,e,i){var s=e-t;return J(t)?vo(t,n(0,t.length),e):zt(i,function(r){return(s+(r-t)%s)%s+t})},wh=function n(t,e,i){var s=e-t,r=s*2;return J(t)?vo(t,n(0,t.length-1),e):zt(i,function(o){return o=(r+(o-t)%r)%r||0,t+(o>s?r-o:o)})},ze=function(t){for(var e=0,i="",s,r,o,a;~(s=t.indexOf("random(",e));)o=t.indexOf(")",s),a=t.charAt(s+7)==="[",r=t.substr(s+7,o-s-7).match(a?to:Xi),i+=t.substr(e,s-e)+yo(a?r:+r[0],a?0:+r[1],+r[2]||1e-5),e=o+1;return i+t.substr(e,t.length-e)},xo=function(t,e,i,s,r){var o=e-t,a=s-i;return zt(r,function(u){return i+((u-t)/o*a||0)})},Ch=function n(t,e,i,s){var r=isNaN(t+e)?0:function(d){return(1-d)*t+d*e};if(!r){var o=H(t),a={},u,h,l,c,f;if(i===!0&&(s=1)&&(i=null),o)t={p:t},e={p:e};else if(J(t)&&!J(e)){for(l=[],c=t.length,f=c-2,h=1;h<c;h++)l.push(n(t[h-1],t[h]));c--,r=function(m){m*=c;var p=Math.min(f,~~m);return l[p](m-p)},i=e}else s||(t=ge(J(t)?[]:{},t));if(!l){for(u in e)Ln.call(a,t,u,"get",e[u]);r=function(m){return jn(m,a)||(o?t.p:t)}}}return zt(i,r)},Us=function(t,e,i){var s=t.labels,r=Pt,o,a,u;for(o in s)a=s[o]-e,a<0==!!i&&a&&r>(a=Math.abs(a))&&(u=o,r=a);return u},ht=function(t,e,i){var s=t.vars,r=s[e],o=E,a=t._ctx,u,h,l;if(r)return u=s[e+"Params"],h=s.callbackScope||t,i&&Lt.length&&ai(),a&&(E=a),l=u?r.apply(h,u):r.call(h),E=o,l},Ce=function(t){return Ft(t),t.scrollTrigger&&t.scrollTrigger.kill(!!q),t.progress()<1&&ht(t,"onInterrupt"),t},ue,To=[],Po=function(t){if(t)if(t=!t.name&&t.default||t,Dn()||t.headless){var e=t.name,i=F(t),s=e&&!i&&t.init?function(){this._props=[]}:t,r={init:je,render:jn,add:Ln,kill:Uh,modifier:zh,rawVars:0},o={targetTest:0,get:0,getSetter:In,aliases:{},register:0};if(ve(),t!==s){if(ut[e])return;dt(s,dt(ui(t,r),o)),ge(s.prototype,ge(r,ui(t,o))),ut[s.prop=e]=s,t.targetTest&&(ti.push(s),Rn[e]=1),e=(e==="css"?"CSS":e.charAt(0).toUpperCase()+e.substr(1))+"Plugin"}no(e,s),t.register&&t.register(ot,s,st)}else To.push(t)},k=255,be={aqua:[0,k,k],lime:[0,k,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,k],navy:[0,0,128],white:[k,k,k],olive:[128,128,0],yellow:[k,k,0],orange:[k,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[k,0,0],pink:[k,192,203],cyan:[0,k,k],transparent:[k,k,k,0]},Vi=function(t,e,i){return t+=t<0?1:t>1?-1:0,(t*6<1?e+(i-e)*t*6:t<.5?i:t*3<2?e+(i-e)*(2/3-t)*6:e)*k+.5|0},So=function(t,e,i){var s=t?Mt(t)?[t>>16,t>>8&k,t&k]:0:be.black,r,o,a,u,h,l,c,f,d,m;if(!s){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),be[t])s=be[t];else if(t.charAt(0)==="#"){if(t.length<6&&(r=t.charAt(1),o=t.charAt(2),a=t.charAt(3),t="#"+r+r+o+o+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return s=parseInt(t.substr(1,6),16),[s>>16,s>>8&k,s&k,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),s=[t>>16,t>>8&k,t&k]}else if(t.substr(0,3)==="hsl"){if(s=m=t.match(Xi),!e)u=+s[0]%360/360,h=+s[1]/100,l=+s[2]/100,o=l<=.5?l*(h+1):l+h-l*h,r=l*2-o,s.length>3&&(s[3]*=1),s[0]=Vi(u+1/3,r,o),s[1]=Vi(u,r,o),s[2]=Vi(u-1/3,r,o);else if(~t.indexOf("="))return s=t.match(Qr),i&&s.length<4&&(s[3]=1),s}else s=t.match(Xi)||be.transparent;s=s.map(Number)}return e&&!m&&(r=s[0]/k,o=s[1]/k,a=s[2]/k,c=Math.max(r,o,a),f=Math.min(r,o,a),l=(c+f)/2,c===f?u=h=0:(d=c-f,h=l>.5?d/(2-c-f):d/(c+f),u=c===r?(o-a)/d+(o<a?6:0):c===o?(a-r)/d+2:(r-o)/d+4,u*=60),s[0]=~~(u+.5),s[1]=~~(h*100+.5),s[2]=~~(l*100+.5)),i&&s.length<4&&(s[3]=1),s},wo=function(t){var e=[],i=[],s=-1;return t.split(Bt).forEach(function(r){var o=r.match(ae)||[];e.push.apply(e,o),i.push(s+=o.length+1)}),e.c=i,e},Ns=function(t,e,i){var s="",r=(t+s).match(Bt),o=e?"hsla(":"rgba(",a=0,u,h,l,c;if(!r)return t;if(r=r.map(function(f){return(f=So(f,e,1))&&o+(e?f[0]+","+f[1]+"%,"+f[2]+"%,"+f[3]:f.join(","))+")"}),i&&(l=wo(t),u=i.c,u.join(s)!==l.c.join(s)))for(h=t.replace(Bt,"1").split(ae),c=h.length-1;a<c;a++)s+=h[a]+(~u.indexOf(a)?r.shift()||o+"0,0,0,0)":(l.length?l:r.length?r:i).shift());if(!h)for(h=t.split(Bt),c=h.length-1;a<c;a++)s+=h[a]+r[a];return s+h[c]},Bt=function(){var n="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in be)n+="|"+t+"\\b";return new RegExp(n+")","gi")}(),bh=/hsl[a]?\(/,Co=function(t){var e=t.join(" "),i;if(Bt.lastIndex=0,Bt.test(e))return i=bh.test(e),t[1]=Ns(t[1],i),t[0]=Ns(t[0],i,wo(t[1])),!0},Ue,lt=function(){var n=Date.now,t=500,e=33,i=n(),s=i,r=1e3/240,o=r,a=[],u,h,l,c,f,d,m=function p(g){var _=n()-s,y=g===!0,v,T,x,P;if((_>t||_<0)&&(i+=_-e),s+=_,x=s-i,v=x-o,(v>0||y)&&(P=++c.frame,f=x-c.time*1e3,c.time=x=x/1e3,o+=v+(v>=r?4:r-v),T=1),y||(u=h(p)),T)for(d=0;d<a.length;d++)a[d](x,f,P,g)};return c={time:0,frame:0,tick:function(){m(!0)},deltaRatio:function(g){return f/(1e3/(g||60))},wake:function(){eo&&(!Hi&&Dn()&&(vt=Hi=window,Mn=vt.document||{},ft.gsap=ot,(vt.gsapVersions||(vt.gsapVersions=[])).push(ot.version),io(oi||vt.GreenSockGlobals||!vt.gsap&&vt||{}),To.forEach(Po)),l=typeof requestAnimationFrame<"u"&&requestAnimationFrame,u&&c.sleep(),h=l||function(g){return setTimeout(g,o-c.time*1e3+1|0)},Ue=1,m(2))},sleep:function(){(l?cancelAnimationFrame:clearTimeout)(u),Ue=0,h=je},lagSmoothing:function(g,_){t=g||1/0,e=Math.min(_||33,t)},fps:function(g){r=1e3/(g||240),o=c.time*1e3+r},add:function(g,_,y){var v=_?function(T,x,P,w){g(T,x,P,w),c.remove(v)}:g;return c.remove(g),a[y?"unshift":"push"](v),ve(),v},remove:function(g,_){~(_=a.indexOf(g))&&a.splice(_,1)&&d>=_&&d--},_listeners:a},c}(),ve=function(){return!Ue&&lt.wake()},D={},Ah=/^[\d.\-M][\d.\-,\s]/,Dh=/["']/g,Mh=function(t){for(var e={},i=t.substr(1,t.length-3).split(":"),s=i[0],r=1,o=i.length,a,u,h;r<o;r++)u=i[r],a=r!==o-1?u.lastIndexOf(","):u.length,h=u.substr(0,a),e[s]=isNaN(h)?h.replace(Dh,"").trim():+h,s=u.substr(a+1).trim();return e},Vh=function(t){var e=t.indexOf("(")+1,i=t.indexOf(")"),s=t.indexOf("(",e);return t.substring(e,~s&&s<i?t.indexOf(")",i+1):i)},Rh=function(t){var e=(t+"").split("("),i=D[e[0]];return i&&e.length>1&&i.config?i.config.apply(null,~t.indexOf("{")?[Mh(e[1])]:Vh(t).split(",").map(ao)):D._CE&&Ah.test(t)?D._CE("",t):i},bo=function(t){return function(e){return 1-t(1-e)}},Ao=function n(t,e){for(var i=t._first,s;i;)i instanceof tt?n(i,e):i.vars.yoyoEase&&(!i._yoyo||!i._repeat)&&i._yoyo!==e&&(i.timeline?n(i.timeline,e):(s=i._ease,i._ease=i._yEase,i._yEase=s,i._yoyo=e)),i=i._next},Zt=function(t,e){return t&&(F(t)?t:D[t]||Rh(t))||e},ee=function(t,e,i,s){i===void 0&&(i=function(u){return 1-e(1-u)}),s===void 0&&(s=function(u){return u<.5?e(u*2)/2:1-e((1-u)*2)/2});var r={easeIn:e,easeOut:i,easeInOut:s},o;return nt(t,function(a){D[a]=ft[a]=r,D[o=a.toLowerCase()]=i;for(var u in r)D[o+(u==="easeIn"?".in":u==="easeOut"?".out":".inOut")]=D[a+"."+u]=r[u]}),r},Do=function(t){return function(e){return e<.5?(1-t(1-e*2))/2:.5+t((e-.5)*2)/2}},Ri=function n(t,e,i){var s=e>=1?e:1,r=(i||(t?.3:.45))/(e<1?e:1),o=r/Yi*(Math.asin(1/s)||0),a=function(l){return l===1?1:s*Math.pow(2,-10*l)*rh((l-o)*r)+1},u=t==="out"?a:t==="in"?function(h){return 1-a(1-h)}:Do(a);return r=Yi/r,u.config=function(h,l){return n(t,h,l)},u},Oi=function n(t,e){e===void 0&&(e=1.70158);var i=function(o){return o?--o*o*((e+1)*o+e)+1:0},s=t==="out"?i:t==="in"?function(r){return 1-i(1-r)}:Do(i);return s.config=function(r){return n(t,r)},s};nt("Linear,Quad,Cubic,Quart,Quint,Strong",function(n,t){var e=t<5?t+1:t;ee(n+",Power"+(e-1),t?function(i){return Math.pow(i,e)}:function(i){return i},function(i){return 1-Math.pow(1-i,e)},function(i){return i<.5?Math.pow(i*2,e)/2:1-Math.pow((1-i)*2,e)/2})});D.Linear.easeNone=D.none=D.Linear.easeIn;ee("Elastic",Ri("in"),Ri("out"),Ri());(function(n,t){var e=1/t,i=2*e,s=2.5*e,r=function(a){return a<e?n*a*a:a<i?n*Math.pow(a-1.5/t,2)+.75:a<s?n*(a-=2.25/t)*a+.9375:n*Math.pow(a-2.625/t,2)+.984375};ee("Bounce",function(o){return 1-r(1-o)},r)})(7.5625,2.75);ee("Expo",function(n){return Math.pow(2,10*(n-1))*n+n*n*n*n*n*n*(1-n)});ee("Circ",function(n){return-(Kr(1-n*n)-1)});ee("Sine",function(n){return n===1?1:-sh(n*ih)+1});ee("Back",Oi("in"),Oi("out"),Oi());D.SteppedEase=D.steps=ft.SteppedEase={config:function(t,e){t===void 0&&(t=1);var i=1/t,s=t+(e?0:1),r=e?1:0,o=1-Q;return function(a){return((s*Ye(0,o,a)|0)+r)*i}}};me.ease=D["quad.out"];nt("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(n){return On+=n+","+n+"Params,"});var Mo=function(t,e){this.id=nh++,t._gsap=this,this.target=t,this.harness=e,this.get=e?e.get:ro,this.set=e?e.getSetter:In},Ne=function(){function n(e){this.vars=e,this._delay=+e.delay||0,(this._repeat=e.repeat===1/0?-2:e.repeat||0)&&(this._rDelay=e.repeatDelay||0,this._yoyo=!!e.yoyo||!!e.yoyoEase),this._ts=1,ye(this,+e.duration,1,1),this.data=e.data,E&&(this._ctx=E,E.data.push(this)),Ue||lt.wake()}var t=n.prototype;return t.delay=function(i){return i||i===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+i-this._delay),this._delay=i,this):this._delay},t.duration=function(i){return arguments.length?this.totalDuration(this._repeat>0?i+(i+this._rDelay)*this._repeat:i):this.totalDuration()&&this._dur},t.totalDuration=function(i){return arguments.length?(this._dirty=0,ye(this,this._repeat<0?i:(i-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(i,s){if(ve(),!arguments.length)return this._tTime;var r=this._dp;if(r&&r.smoothChildTiming&&this._ts){for(vi(this,i),!r._dp||r.parent||ho(r,this);r&&r.parent;)r.parent._time!==r._start+(r._ts>=0?r._tTime/r._ts:(r.totalDuration()-r._tTime)/-r._ts)&&r.totalTime(r._tTime,!0),r=r.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&i<this._tDur||this._ts<0&&i>0||!this._tDur&&!i)&&xt(this._dp,this,this._start-this._delay)}return(this._tTime!==i||!this._dur&&!s||this._initted&&Math.abs(this._zTime)===Q||!i&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=i),oo(this,i,s)),this},t.time=function(i,s){return arguments.length?this.totalTime(Math.min(this.totalDuration(),i+js(this))%(this._dur+this._rDelay)||(i?this._dur:0),s):this._time},t.totalProgress=function(i,s){return arguments.length?this.totalTime(this.totalDuration()*i,s):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},t.progress=function(i,s){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-i:i)+js(this),s):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(i,s){var r=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(i-1)*r,s):this._repeat?_e(this._tTime,r)+1:1},t.timeScale=function(i,s){if(!arguments.length)return this._rts===-1e-8?0:this._rts;if(this._rts===i)return this;var r=this.parent&&this._ts?li(this.parent._time,this):this._tTime;return this._rts=+i||0,this._ts=this._ps||i===-1e-8?0:this._rts,this.totalTime(Ye(-Math.abs(this._delay),this.totalDuration(),r),s!==!1),yi(this),fh(this)},t.paused=function(i){return arguments.length?(this._ps!==i&&(this._ps=i,i?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(ve(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==Q&&(this._tTime-=Q)))),this):this._ps},t.startTime=function(i){if(arguments.length){this._start=i;var s=this.parent||this._dp;return s&&(s._sort||!this.parent)&&xt(s,this,i-this._delay),this}return this._start},t.endTime=function(i){return this._start+(it(i)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(i){var s=this.parent||this._dp;return s?i&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?li(s.rawTime(i),this):this._tTime:this._tTime},t.revert=function(i){i===void 0&&(i=uh);var s=q;return q=i,En(this)&&(this.timeline&&this.timeline.revert(i),this.totalTime(-.01,i.suppressEvents)),this.data!=="nested"&&i.kill!==!1&&this.kill(),q=s,this},t.globalTime=function(i){for(var s=this,r=arguments.length?i:s.rawTime();s;)r=s._start+r/(Math.abs(s._ts)||1),s=s._dp;return!this.parent&&this._sat?this._sat.globalTime(i):r},t.repeat=function(i){return arguments.length?(this._repeat=i===1/0?-2:i,zs(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(i){if(arguments.length){var s=this._time;return this._rDelay=i,zs(this),s?this.time(s):this}return this._rDelay},t.yoyo=function(i){return arguments.length?(this._yoyo=i,this):this._yoyo},t.seek=function(i,s){return this.totalTime(gt(this,i),it(s))},t.restart=function(i,s){return this.play().totalTime(i?-this._delay:0,it(s)),this._dur||(this._zTime=-1e-8),this},t.play=function(i,s){return i!=null&&this.seek(i,s),this.reversed(!1).paused(!1)},t.reverse=function(i,s){return i!=null&&this.seek(i||this.totalDuration(),s),this.reversed(!0).paused(!1)},t.pause=function(i,s){return i!=null&&this.seek(i,s),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(i){return arguments.length?(!!i!==this.reversed()&&this.timeScale(-this._rts||(i?-1e-8:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-1e-8,this},t.isActive=function(){var i=this.parent||this._dp,s=this._start,r;return!!(!i||this._ts&&this._initted&&i.isActive()&&(r=i.rawTime(!0))>=s&&r<this.endTime(!0)-Q)},t.eventCallback=function(i,s,r){var o=this.vars;return arguments.length>1?(s?(o[i]=s,r&&(o[i+"Params"]=r),i==="onUpdate"&&(this._onUpdate=s)):delete o[i],this):o[i]},t.then=function(i){var s=this;return new Promise(function(r){var o=F(i)?i:uo,a=function(){var h=s.then;s.then=null,F(o)&&(o=o(s))&&(o.then||o===s)&&(s.then=h),r(o),s.then=h};s._initted&&s.totalProgress()===1&&s._ts>=0||!s._tTime&&s._ts<0?a():s._prom=a})},t.kill=function(){Ce(this)},n}();dt(Ne.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-1e-8,_prom:0,_ps:!1,_rts:1});var tt=function(n){qr(t,n);function t(i,s){var r;return i===void 0&&(i={}),r=n.call(this,i)||this,r.labels={},r.smoothChildTiming=!!i.smoothChildTiming,r.autoRemoveChildren=!!i.autoRemoveChildren,r._sort=it(i.sortChildren),L&&xt(i.parent||L,bt(r),s),i.reversed&&r.reverse(),i.paused&&r.paused(!0),i.scrollTrigger&&co(bt(r),i.scrollTrigger),r}var e=t.prototype;return e.to=function(s,r,o){return Oe(0,arguments,this),this},e.from=function(s,r,o){return Oe(1,arguments,this),this},e.fromTo=function(s,r,o,a){return Oe(2,arguments,this),this},e.set=function(s,r,o){return r.duration=0,r.parent=this,Re(r).repeatDelay||(r.repeat=0),r.immediateRender=!!r.immediateRender,new W(s,r,gt(this,o),1),this},e.call=function(s,r,o){return xt(this,W.delayedCall(0,s,r),o)},e.staggerTo=function(s,r,o,a,u,h,l){return o.duration=r,o.stagger=o.stagger||a,o.onComplete=h,o.onCompleteParams=l,o.parent=this,new W(s,o,gt(this,u)),this},e.staggerFrom=function(s,r,o,a,u,h,l){return o.runBackwards=1,Re(o).immediateRender=it(o.immediateRender),this.staggerTo(s,r,o,a,u,h,l)},e.staggerFromTo=function(s,r,o,a,u,h,l,c){return a.startAt=o,Re(a).immediateRender=it(a.immediateRender),this.staggerTo(s,r,a,u,h,l,c)},e.render=function(s,r,o){var a=this._time,u=this._dirty?this.totalDuration():this._tDur,h=this._dur,l=s<=0?0:G(s),c=this._zTime<0!=s<0&&(this._initted||!h),f,d,m,p,g,_,y,v,T,x,P,w;if(this!==L&&l>u&&s>=0&&(l=u),l!==this._tTime||o||c){if(a!==this._time&&h&&(l+=this._time-a,s+=this._time-a),f=l,T=this._start,v=this._ts,_=!v,c&&(h||(a=this._zTime),(s||!r)&&(this._zTime=s)),this._repeat){if(P=this._yoyo,g=h+this._rDelay,this._repeat<-1&&s<0)return this.totalTime(g*100+s,r,o);if(f=G(l%g),l===u?(p=this._repeat,f=h):(x=G(l/g),p=~~x,p&&p===x&&(f=h,p--),f>h&&(f=h)),x=_e(this._tTime,g),!a&&this._tTime&&x!==p&&this._tTime-x*g-this._dur<=0&&(x=p),P&&p&1&&(f=h-f,w=1),p!==x&&!this._lock){var C=P&&x&1,S=C===(P&&p&1);if(p<x&&(C=!C),a=C?0:l%h?h:l,this._lock=1,this.render(a||(w?0:G(p*g)),r,!h)._lock=0,this._tTime=l,!r&&this.parent&&ht(this,"onRepeat"),this.vars.repeatRefresh&&!w&&(this.invalidate()._lock=1),a&&a!==this._time||_!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(h=this._dur,u=this._tDur,S&&(this._lock=2,a=C?h:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!w&&this.invalidate()),this._lock=0,!this._ts&&!_)return this;Ao(this,w)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(y=gh(this,G(a),G(f)),y&&(l-=f-(f=y._start))),this._tTime=l,this._time=f,this._act=!v,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=s,a=0),!a&&l&&!r&&!x&&(ht(this,"onStart"),this._tTime!==l))return this;if(f>=a&&s>=0)for(d=this._first;d;){if(m=d._next,(d._act||f>=d._start)&&d._ts&&y!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(f-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(f-d._start)*d._ts,r,o),f!==this._time||!this._ts&&!_){y=0,m&&(l+=this._zTime=-1e-8);break}}d=m}else{d=this._last;for(var A=s<0?s:f;d;){if(m=d._prev,(d._act||A<=d._end)&&d._ts&&y!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(A-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(A-d._start)*d._ts,r,o||q&&En(d)),f!==this._time||!this._ts&&!_){y=0,m&&(l+=this._zTime=A?-1e-8:Q);break}}d=m}}if(y&&!r&&(this.pause(),y.render(f>=a?0:-1e-8)._zTime=f>=a?1:-1,this._ts))return this._start=T,yi(this),this.render(s,r,o);this._onUpdate&&!r&&ht(this,"onUpdate",!0),(l===u&&this._tTime>=this.totalDuration()||!l&&a)&&(T===this._start||Math.abs(v)!==Math.abs(this._ts))&&(this._lock||((s||!h)&&(l===u&&this._ts>0||!l&&this._ts<0)&&Ft(this,1),!r&&!(s<0&&!a)&&(l||a||!u)&&(ht(this,l===u&&s>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(l<u&&this.timeScale()>0)&&this._prom())))}return this},e.add=function(s,r){var o=this;if(Mt(r)||(r=gt(this,r,s)),!(s instanceof Ne)){if(J(s))return s.forEach(function(a){return o.add(a,r)}),this;if(H(s))return this.addLabel(s,r);if(F(s))s=W.delayedCall(0,s);else return this}return this!==s?xt(this,s,r):this},e.getChildren=function(s,r,o,a){s===void 0&&(s=!0),r===void 0&&(r=!0),o===void 0&&(o=!0),a===void 0&&(a=-1e8);for(var u=[],h=this._first;h;)h._start>=a&&(h instanceof W?r&&u.push(h):(o&&u.push(h),s&&u.push.apply(u,h.getChildren(!0,r,o)))),h=h._next;return u},e.getById=function(s){for(var r=this.getChildren(1,1,1),o=r.length;o--;)if(r[o].vars.id===s)return r[o]},e.remove=function(s){return H(s)?this.removeLabel(s):F(s)?this.killTweensOf(s):(s.parent===this&&_i(this,s),s===this._recent&&(this._recent=this._last),Kt(this))},e.totalTime=function(s,r){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=G(lt.time-(this._ts>0?s/this._ts:(this.totalDuration()-s)/-this._ts))),n.prototype.totalTime.call(this,s,r),this._forcing=0,this):this._tTime},e.addLabel=function(s,r){return this.labels[s]=gt(this,r),this},e.removeLabel=function(s){return delete this.labels[s],this},e.addPause=function(s,r,o){var a=W.delayedCall(0,r||je,o);return a.data="isPause",this._hasPause=1,xt(this,a,gt(this,s))},e.removePause=function(s){var r=this._first;for(s=gt(this,s);r;)r._start===s&&r.data==="isPause"&&Ft(r),r=r._next},e.killTweensOf=function(s,r,o){for(var a=this.getTweensOf(s,o),u=a.length;u--;)Ot!==a[u]&&a[u].kill(s,r);return this},e.getTweensOf=function(s,r){for(var o=[],a=_t(s),u=this._first,h=Mt(r),l;u;)u instanceof W?lh(u._targets,a)&&(h?(!Ot||u._initted&&u._ts)&&u.globalTime(0)<=r&&u.globalTime(u.totalDuration())>r:!r||u.isActive())&&o.push(u):(l=u.getTweensOf(a,r)).length&&o.push.apply(o,l),u=u._next;return o},e.tweenTo=function(s,r){r=r||{};var o=this,a=gt(o,s),u=r,h=u.startAt,l=u.onStart,c=u.onStartParams,f=u.immediateRender,d,m=W.to(o,dt({ease:r.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:r.duration||Math.abs((a-(h&&"time"in h?h.time:o._time))/o.timeScale())||Q,onStart:function(){if(o.pause(),!d){var g=r.duration||Math.abs((a-(h&&"time"in h?h.time:o._time))/o.timeScale());m._dur!==g&&ye(m,g,0,1).render(m._time,!0,!0),d=1}l&&l.apply(m,c||[])}},r));return f?m.render(0):m},e.tweenFromTo=function(s,r,o){return this.tweenTo(r,dt({startAt:{time:gt(this,s)}},o))},e.recent=function(){return this._recent},e.nextLabel=function(s){return s===void 0&&(s=this._time),Us(this,gt(this,s))},e.previousLabel=function(s){return s===void 0&&(s=this._time),Us(this,gt(this,s),1)},e.currentLabel=function(s){return arguments.length?this.seek(s,!0):this.previousLabel(this._time+Q)},e.shiftChildren=function(s,r,o){o===void 0&&(o=0);for(var a=this._first,u=this.labels,h;a;)a._start>=o&&(a._start+=s,a._end+=s),a=a._next;if(r)for(h in u)u[h]>=o&&(u[h]+=s);return Kt(this)},e.invalidate=function(s){var r=this._first;for(this._lock=0;r;)r.invalidate(s),r=r._next;return n.prototype.invalidate.call(this,s)},e.clear=function(s){s===void 0&&(s=!0);for(var r=this._first,o;r;)o=r._next,this.remove(r),r=o;return this._dp&&(this._time=this._tTime=this._pTime=0),s&&(this.labels={}),Kt(this)},e.totalDuration=function(s){var r=0,o=this,a=o._last,u=Pt,h,l,c;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-s:s));if(o._dirty){for(c=o.parent;a;)h=a._prev,a._dirty&&a.totalDuration(),l=a._start,l>u&&o._sort&&a._ts&&!o._lock?(o._lock=1,xt(o,a,l-a._delay,1)._lock=0):u=l,l<0&&a._ts&&(r-=l,(!c&&!o._dp||c&&c.smoothChildTiming)&&(o._start+=l/o._ts,o._time-=l,o._tTime-=l),o.shiftChildren(-l,!1,-1/0),u=0),a._end>r&&a._ts&&(r=a._end),a=h;ye(o,o===L&&o._time>r?o._time:r,1,1),o._dirty=0}return o._tDur},t.updateRoot=function(s){if(L._ts&&(oo(L,li(s,L)),so=lt.frame),lt.frame>=Fs){Fs+=ct.autoSleep||120;var r=L._first;if((!r||!r._ts)&&ct.autoSleep&&lt._listeners.length<2){for(;r&&!r._ts;)r=r._next;r||lt.sleep()}}},t}(Ne);dt(tt.prototype,{_lock:0,_hasPause:0,_forcing:0});var Oh=function(t,e,i,s,r,o,a){var u=new st(this._pt,t,e,0,1,Lo,null,r),h=0,l=0,c,f,d,m,p,g,_,y;for(u.b=i,u.e=s,i+="",s+="",(_=~s.indexOf("random("))&&(s=ze(s)),o&&(y=[i,s],o(y,t,e),i=y[0],s=y[1]),f=i.match(Di)||[];c=Di.exec(s);)m=c[0],p=s.substring(h,c.index),d?d=(d+1)%5:p.substr(-5)==="rgba("&&(d=1),m!==f[l++]&&(g=parseFloat(f[l-1])||0,u._pt={_next:u._pt,p:p||l===1?p:",",s:g,c:m.charAt(1)==="="?he(g,m)-g:parseFloat(m)-g,m:d&&d<4?Math.round:0},h=Di.lastIndex);return u.c=h<s.length?s.substring(h,s.length):"",u.fp=a,(Jr.test(s)||_)&&(u.e=0),this._pt=u,u},Ln=function(t,e,i,s,r,o,a,u,h,l){F(s)&&(s=s(r||0,t,o));var c=t[e],f=i!=="get"?i:F(c)?h?t[e.indexOf("set")||!F(t["get"+e.substr(3)])?e:"get"+e.substr(3)](h):t[e]():c,d=F(c)?h?Fh:ko:Fn,m;if(H(s)&&(~s.indexOf("random(")&&(s=ze(s)),s.charAt(1)==="="&&(m=he(f,s)+(K(f)||0),(m||m===0)&&(s=m))),!l||f!==s||en)return!isNaN(f*s)&&s!==""?(m=new st(this._pt,t,e,+f||0,s-(f||0),typeof c=="boolean"?jh:Eo,0,d),h&&(m.fp=h),a&&m.modifier(a,this,t),this._pt=m):(!c&&!(e in t)&&Vn(e,s),Oh.call(this,t,e,f,s,d,u||ct.stringFilter,h))},kh=function(t,e,i,s,r){if(F(t)&&(t=ke(t,r,e,i,s)),!St(t)||t.style&&t.nodeType||J(t)||Zr(t))return H(t)?ke(t,r,e,i,s):t;var o={},a;for(a in t)o[a]=ke(t[a],r,e,i,s);return o},Vo=function(t,e,i,s,r,o){var a,u,h,l;if(ut[t]&&(a=new ut[t]).init(r,a.rawVars?e[t]:kh(e[t],s,r,o,i),i,s,o)!==!1&&(i._pt=u=new st(i._pt,r,t,0,1,a.render,a,0,a.priority),i!==ue))for(h=i._ptLookup[i._targets.indexOf(r)],l=a._props.length;l--;)h[a._props[l]]=u;return a},Ot,en,Bn=function n(t,e,i){var s=t.vars,r=s.ease,o=s.startAt,a=s.immediateRender,u=s.lazy,h=s.onUpdate,l=s.runBackwards,c=s.yoyoEase,f=s.keyframes,d=s.autoRevert,m=t._dur,p=t._startAt,g=t._targets,_=t.parent,y=_&&_.data==="nested"?_.vars.targets:g,v=t._overwrite==="auto"&&!bn,T=t.timeline,x,P,w,C,S,A,V,R,O,N,$,I,M;if(T&&(!f||!r)&&(r="none"),t._ease=Zt(r,me.ease),t._yEase=c?bo(Zt(c===!0?r:c,me.ease)):0,c&&t._yoyo&&!t._repeat&&(c=t._yEase,t._yEase=t._ease,t._ease=c),t._from=!T&&!!s.runBackwards,!T||f&&!s.stagger){if(R=g[0]?qt(g[0]).harness:0,I=R&&s[R.prop],x=ui(s,Rn),p&&(p._zTime<0&&p.progress(1),e<0&&l&&a&&!d?p.render(-1,!0):p.revert(l&&m?Je:ah),p._lazy=0),o){if(Ft(t._startAt=W.set(g,dt({data:"isStart",overwrite:!1,parent:_,immediateRender:!0,lazy:!p&&it(u),startAt:null,delay:0,onUpdate:h&&function(){return ht(t,"onUpdate")},stagger:0},o))),t._startAt._dp=0,t._startAt._sat=t,e<0&&(q||!a&&!d)&&t._startAt.revert(Je),a&&m&&e<=0&&i<=0){e&&(t._zTime=e);return}}else if(l&&m&&!p){if(e&&(a=!1),w=dt({overwrite:!1,data:"isFromStart",lazy:a&&!p&&it(u),immediateRender:a,stagger:0,parent:_},x),I&&(w[R.prop]=I),Ft(t._startAt=W.set(g,w)),t._startAt._dp=0,t._startAt._sat=t,e<0&&(q?t._startAt.revert(Je):t._startAt.render(-1,!0)),t._zTime=e,!a)n(t._startAt,Q,Q);else if(!e)return}for(t._pt=t._ptCache=0,u=m&&it(u)||u&&!m,P=0;P<g.length;P++){if(S=g[P],V=S._gsap||kn(g)[P]._gsap,t._ptLookup[P]=N={},qi[V.id]&&Lt.length&&ai(),$=y===g?P:y.indexOf(S),R&&(O=new R).init(S,I||x,t,$,y)!==!1&&(t._pt=C=new st(t._pt,S,O.name,0,1,O.render,O,0,O.priority),O._props.forEach(function(j){N[j]=C}),O.priority&&(A=1)),!R||I)for(w in x)ut[w]&&(O=Vo(w,x,t,$,S,y))?O.priority&&(A=1):N[w]=C=Ln.call(t,S,w,"get",x[w],$,y,0,s.stringFilter);t._op&&t._op[P]&&t.kill(S,t._op[P]),v&&t._pt&&(Ot=t,L.killTweensOf(S,N,t.globalTime(e)),M=!t.parent,Ot=0),t._pt&&u&&(qi[V.id]=1)}A&&Bo(t),t._onInit&&t._onInit(t)}t._onUpdate=h,t._initted=(!t._op||t._pt)&&!M,f&&e<=0&&T.render(Pt,!0,!0)},Eh=function(t,e,i,s,r,o,a,u){var h=(t._pt&&t._ptCache||(t._ptCache={}))[e],l,c,f,d;if(!h)for(h=t._ptCache[e]=[],f=t._ptLookup,d=t._targets.length;d--;){if(l=f[d][e],l&&l.d&&l.d._pt)for(l=l.d._pt;l&&l.p!==e&&l.fp!==e;)l=l._next;if(!l)return en=1,t.vars[e]="+=0",Bn(t,a),en=0,u?Ie(e+" not eligible for reset"):1;h.push(l)}for(d=h.length;d--;)c=h[d],l=c._pt||c,l.s=(s||s===0)&&!r?s:l.s+(s||0)+o*l.c,l.c=i-l.s,c.e&&(c.e=U(i)+K(c.e)),c.b&&(c.b=l.s+K(c.b))},Lh=function(t,e){var i=t[0]?qt(t[0]).harness:0,s=i&&i.aliases,r,o,a,u;if(!s)return e;r=ge({},e);for(o in s)if(o in r)for(u=s[o].split(","),a=u.length;a--;)r[u[a]]=r[o];return r},Bh=function(t,e,i,s){var r=e.ease||s||"power1.inOut",o,a;if(J(e))a=i[t]||(i[t]=[]),e.forEach(function(u,h){return a.push({t:h/(e.length-1)*100,v:u,e:r})});else for(o in e)a=i[o]||(i[o]=[]),o==="ease"||a.push({t:parseFloat(t),v:e[o],e:r})},ke=function(t,e,i,s,r){return F(t)?t.call(e,i,s,r):H(t)&&~t.indexOf("random(")?ze(t):t},Ro=On+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Oo={};nt(Ro+",id,stagger,delay,duration,paused,scrollTrigger",function(n){return Oo[n]=1});var W=function(n){qr(t,n);function t(i,s,r,o){var a;typeof s=="number"&&(r.duration=s,s=r,r=null),a=n.call(this,o?s:Re(s))||this;var u=a.vars,h=u.duration,l=u.delay,c=u.immediateRender,f=u.stagger,d=u.overwrite,m=u.keyframes,p=u.defaults,g=u.scrollTrigger,_=u.yoyoEase,y=s.parent||L,v=(J(i)||Zr(i)?Mt(i[0]):"length"in s)?[i]:_t(i),T,x,P,w,C,S,A,V;if(a._targets=v.length?kn(v):Ie("GSAP target "+i+" not found. https://gsap.com",!ct.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=d,m||f||qe(h)||qe(l)){if(s=a.vars,T=a.timeline=new tt({data:"nested",defaults:p||{},targets:y&&y.data==="nested"?y.vars.targets:v}),T.kill(),T.parent=T._dp=bt(a),T._start=0,f||qe(h)||qe(l)){if(w=v.length,A=f&&go(f),St(f))for(C in f)~Ro.indexOf(C)&&(V||(V={}),V[C]=f[C]);for(x=0;x<w;x++)P=ui(s,Oo),P.stagger=0,_&&(P.yoyoEase=_),V&&ge(P,V),S=v[x],P.duration=+ke(h,bt(a),x,S,v),P.delay=(+ke(l,bt(a),x,S,v)||0)-a._delay,!f&&w===1&&P.delay&&(a._delay=l=P.delay,a._start+=l,P.delay=0),T.to(S,P,A?A(x,S,v):0),T._ease=D.none;T.duration()?h=l=0:a.timeline=0}else if(m){Re(dt(T.vars.defaults,{ease:"none"})),T._ease=Zt(m.ease||s.ease||"none");var R=0,O,N,$;if(J(m))m.forEach(function(I){return T.to(v,I,">")}),T.duration();else{P={};for(C in m)C==="ease"||C==="easeEach"||Bh(C,m[C],P,m.easeEach);for(C in P)for(O=P[C].sort(function(I,M){return I.t-M.t}),R=0,x=0;x<O.length;x++)N=O[x],$={ease:N.e,duration:(N.t-(x?O[x-1].t:0))/100*h},$[C]=N.v,T.to(v,$,R),R+=$.duration;T.duration()<h&&T.to({},{duration:h-T.duration()})}}h||a.duration(h=T.duration())}else a.timeline=0;return d===!0&&!bn&&(Ot=bt(a),L.killTweensOf(v),Ot=0),xt(y,bt(a),r),s.reversed&&a.reverse(),s.paused&&a.paused(!0),(c||!h&&!m&&a._start===G(y._time)&&it(c)&&dh(bt(a))&&y.data!=="nested")&&(a._tTime=-1e-8,a.render(Math.max(0,-l)||0)),g&&co(bt(a),g),a}var e=t.prototype;return e.render=function(s,r,o){var a=this._time,u=this._tDur,h=this._dur,l=s<0,c=s>u-Q&&!l?u:s<Q?0:s,f,d,m,p,g,_,y,v,T;if(!h)mh(this,s,r,o);else if(c!==this._tTime||!s||o||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==l||this._lazy){if(f=c,v=this.timeline,this._repeat){if(p=h+this._rDelay,this._repeat<-1&&l)return this.totalTime(p*100+s,r,o);if(f=G(c%p),c===u?(m=this._repeat,f=h):(g=G(c/p),m=~~g,m&&m===g?(f=h,m--):f>h&&(f=h)),_=this._yoyo&&m&1,_&&(T=this._yEase,f=h-f),g=_e(this._tTime,p),f===a&&!o&&this._initted&&m===g)return this._tTime=c,this;m!==g&&(v&&this._yEase&&Ao(v,_),this.vars.repeatRefresh&&!_&&!this._lock&&f!==p&&this._initted&&(this._lock=o=1,this.render(G(p*m),!0).invalidate()._lock=0))}if(!this._initted){if(fo(this,l?s:f,o,r,c))return this._tTime=0,this;if(a!==this._time&&!(o&&this.vars.repeatRefresh&&m!==g))return this;if(h!==this._dur)return this.render(s,r,o)}if(this._tTime=c,this._time=f,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=y=(T||this._ease)(f/h),this._from&&(this.ratio=y=1-y),!a&&c&&!r&&!g&&(ht(this,"onStart"),this._tTime!==c))return this;for(d=this._pt;d;)d.r(y,d.d),d=d._next;v&&v.render(s<0?s:v._dur*v._ease(f/this._dur),r,o)||this._startAt&&(this._zTime=s),this._onUpdate&&!r&&(l&&Ki(this,s,r,o),ht(this,"onUpdate")),this._repeat&&m!==g&&this.vars.onRepeat&&!r&&this.parent&&ht(this,"onRepeat"),(c===this._tDur||!c)&&this._tTime===c&&(l&&!this._onUpdate&&Ki(this,s,!0,!0),(s||!h)&&(c===this._tDur&&this._ts>0||!c&&this._ts<0)&&Ft(this,1),!r&&!(l&&!a)&&(c||a||_)&&(ht(this,c===u?"onComplete":"onReverseComplete",!0),this._prom&&!(c<u&&this.timeScale()>0)&&this._prom()))}return this},e.targets=function(){return this._targets},e.invalidate=function(s){return(!s||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(s),n.prototype.invalidate.call(this,s)},e.resetTo=function(s,r,o,a,u){Ue||lt.wake(),this._ts||this.play();var h=Math.min(this._dur,(this._dp._time-this._start)*this._ts),l;return this._initted||Bn(this,h),l=this._ease(h/this._dur),Eh(this,s,r,o,a,l,h,u)?this.resetTo(s,r,o,a,1):(vi(this,0),this.parent||lo(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},e.kill=function(s,r){if(r===void 0&&(r="all"),!s&&(!r||r==="all"))return this._lazy=this._pt=0,this.parent?Ce(this):this.scrollTrigger&&this.scrollTrigger.kill(!!q),this;if(this.timeline){var o=this.timeline.totalDuration();return this.timeline.killTweensOf(s,r,Ot&&Ot.vars.overwrite!==!0)._first||Ce(this),this.parent&&o!==this.timeline.totalDuration()&&ye(this,this._dur*this.timeline._tDur/o,0,1),this}var a=this._targets,u=s?_t(s):a,h=this._ptLookup,l=this._pt,c,f,d,m,p,g,_;if((!r||r==="all")&&ch(a,u))return r==="all"&&(this._pt=0),Ce(this);for(c=this._op=this._op||[],r!=="all"&&(H(r)&&(p={},nt(r,function(y){return p[y]=1}),r=p),r=Lh(a,r)),_=a.length;_--;)if(~u.indexOf(a[_])){f=h[_],r==="all"?(c[_]=r,m=f,d={}):(d=c[_]=c[_]||{},m=r);for(p in m)g=f&&f[p],g&&((!("kill"in g.d)||g.d.kill(p)===!0)&&_i(this,g,"_pt"),delete f[p]),d!=="all"&&(d[p]=1)}return this._initted&&!this._pt&&l&&Ce(this),this},t.to=function(s,r){return new t(s,r,arguments[2])},t.from=function(s,r){return Oe(1,arguments)},t.delayedCall=function(s,r,o,a){return new t(r,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:s,onComplete:r,onReverseComplete:r,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:a})},t.fromTo=function(s,r,o){return Oe(2,arguments)},t.set=function(s,r){return r.duration=0,r.repeatDelay||(r.repeat=0),new t(s,r)},t.killTweensOf=function(s,r,o){return L.killTweensOf(s,r,o)},t}(Ne);dt(W.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});nt("staggerTo,staggerFrom,staggerFromTo",function(n){W[n]=function(){var t=new tt,e=Qi.call(arguments,0);return e.splice(n==="staggerFromTo"?5:4,0,0),t[n].apply(t,e)}});var Fn=function(t,e,i){return t[e]=i},ko=function(t,e,i){return t[e](i)},Fh=function(t,e,i,s){return t[e](s.fp,i)},Ih=function(t,e,i){return t.setAttribute(e,i)},In=function(t,e){return F(t[e])?ko:An(t[e])&&t.setAttribute?Ih:Fn},Eo=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e6)/1e6,e)},jh=function(t,e){return e.set(e.t,e.p,!!(e.s+e.c*t),e)},Lo=function(t,e){var i=e._pt,s="";if(!t&&e.b)s=e.b;else if(t===1&&e.e)s=e.e;else{for(;i;)s=i.p+(i.m?i.m(i.s+i.c*t):Math.round((i.s+i.c*t)*1e4)/1e4)+s,i=i._next;s+=e.c}e.set(e.t,e.p,s,e)},jn=function(t,e){for(var i=e._pt;i;)i.r(t,i.d),i=i._next},zh=function(t,e,i,s){for(var r=this._pt,o;r;)o=r._next,r.p===s&&r.modifier(t,e,i),r=o},Uh=function(t){for(var e=this._pt,i,s;e;)s=e._next,e.p===t&&!e.op||e.op===t?_i(this,e,"_pt"):e.dep||(i=1),e=s;return!i},Nh=function(t,e,i,s){s.mSet(t,e,s.m.call(s.tween,i,s.mt),s)},Bo=function(t){for(var e=t._pt,i,s,r,o;e;){for(i=e._next,s=r;s&&s.pr>e.pr;)s=s._next;(e._prev=s?s._prev:o)?e._prev._next=e:r=e,(e._next=s)?s._prev=e:o=e,e=i}t._pt=r},st=function(){function n(e,i,s,r,o,a,u,h,l){this.t=i,this.s=r,this.c=o,this.p=s,this.r=a||Eo,this.d=u||this,this.set=h||Fn,this.pr=l||0,this._next=e,e&&(e._prev=this)}var t=n.prototype;return t.modifier=function(i,s,r){this.mSet=this.mSet||this.set,this.set=Nh,this.m=i,this.mt=r,this.tween=s},n}();nt(On+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(n){return Rn[n]=1});ft.TweenMax=ft.TweenLite=W;ft.TimelineLite=ft.TimelineMax=tt;L=new tt({sortChildren:!1,defaults:me,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});ct.stringFilter=Co;var Qt=[],ei={},Wh=[],Ws=0,Gh=0,ki=function(t){return(ei[t]||Wh).map(function(e){return e()})},nn=function(){var t=Date.now(),e=[];t-Ws>2&&(ki("matchMediaInit"),Qt.forEach(function(i){var s=i.queries,r=i.conditions,o,a,u,h;for(a in s)o=vt.matchMedia(s[a]).matches,o&&(u=1),o!==r[a]&&(r[a]=o,h=1);h&&(i.revert(),u&&e.push(i))}),ki("matchMediaRevert"),e.forEach(function(i){return i.onMatch(i,function(s){return i.add(null,s)})}),Ws=t,ki("matchMedia"))},Fo=function(){function n(e,i){this.selector=i&&Ji(i),this.data=[],this._r=[],this.isReverted=!1,this.id=Gh++,e&&this.add(e)}var t=n.prototype;return t.add=function(i,s,r){F(i)&&(r=s,s=i,i=F);var o=this,a=function(){var h=E,l=o.selector,c;return h&&h!==o&&h.data.push(o),r&&(o.selector=Ji(r)),E=o,c=s.apply(o,arguments),F(c)&&o._r.push(c),E=h,o.selector=l,o.isReverted=!1,c};return o.last=a,i===F?a(o,function(u){return o.add(null,u)}):i?o[i]=a:a},t.ignore=function(i){var s=E;E=null,i(this),E=s},t.getTweens=function(){var i=[];return this.data.forEach(function(s){return s instanceof n?i.push.apply(i,s.getTweens()):s instanceof W&&!(s.parent&&s.parent.data==="nested")&&i.push(s)}),i},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(i,s){var r=this;if(i?function(){for(var a=r.getTweens(),u=r.data.length,h;u--;)h=r.data[u],h.data==="isFlip"&&(h.revert(),h.getChildren(!0,!0,!1).forEach(function(l){return a.splice(a.indexOf(l),1)}));for(a.map(function(l){return{g:l._dur||l._delay||l._sat&&!l._sat.vars.immediateRender?l.globalTime(0):-1/0,t:l}}).sort(function(l,c){return c.g-l.g||-1/0}).forEach(function(l){return l.t.revert(i)}),u=r.data.length;u--;)h=r.data[u],h instanceof tt?h.data!=="nested"&&(h.scrollTrigger&&h.scrollTrigger.revert(),h.kill()):!(h instanceof W)&&h.revert&&h.revert(i);r._r.forEach(function(l){return l(i,r)}),r.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),s)for(var o=Qt.length;o--;)Qt[o].id===this.id&&Qt.splice(o,1)},t.revert=function(i){this.kill(i||{})},n}(),$h=function(){function n(e){this.contexts=[],this.scope=e,E&&E.data.push(this)}var t=n.prototype;return t.add=function(i,s,r){St(i)||(i={matches:i});var o=new Fo(0,r||this.scope),a=o.conditions={},u,h,l;E&&!o.selector&&(o.selector=E.selector),this.contexts.push(o),s=o.add("onMatch",s),o.queries=i;for(h in i)h==="all"?l=1:(u=vt.matchMedia(i[h]),u&&(Qt.indexOf(o)<0&&Qt.push(o),(a[h]=u.matches)&&(l=1),u.addListener?u.addListener(nn):u.addEventListener("change",nn)));return l&&s(o,function(c){return o.add(null,c)}),this},t.revert=function(i){this.kill(i||{})},t.kill=function(i){this.contexts.forEach(function(s){return s.kill(i,!0)})},n}(),hi={registerPlugin:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];e.forEach(function(s){return Po(s)})},timeline:function(t){return new tt(t)},getTweensOf:function(t,e){return L.getTweensOf(t,e)},getProperty:function(t,e,i,s){H(t)&&(t=_t(t)[0]);var r=qt(t||{}).get,o=i?uo:ao;return i==="native"&&(i=""),t&&(e?o((ut[e]&&ut[e].get||r)(t,e,i,s)):function(a,u,h){return o((ut[a]&&ut[a].get||r)(t,a,u,h))})},quickSetter:function(t,e,i){if(t=_t(t),t.length>1){var s=t.map(function(l){return ot.quickSetter(l,e,i)}),r=s.length;return function(l){for(var c=r;c--;)s[c](l)}}t=t[0]||{};var o=ut[e],a=qt(t),u=a.harness&&(a.harness.aliases||{})[e]||e,h=o?function(l){var c=new o;ue._pt=0,c.init(t,i?l+i:l,ue,0,[t]),c.render(1,c),ue._pt&&jn(1,ue)}:a.set(t,u);return o?h:function(l){return h(t,u,i?l+i:l,a,1)}},quickTo:function(t,e,i){var s,r=ot.to(t,dt((s={},s[e]="+=0.1",s.paused=!0,s.stagger=0,s),i||{})),o=function(u,h,l){return r.resetTo(e,u,h,l)};return o.tween=r,o},isTweening:function(t){return L.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=Zt(t.ease,me.ease)),Is(me,t||{})},config:function(t){return Is(ct,t||{})},registerEffect:function(t){var e=t.name,i=t.effect,s=t.plugins,r=t.defaults,o=t.extendTimeline;(s||"").split(",").forEach(function(a){return a&&!ut[a]&&!ft[a]&&Ie(e+" effect requires "+a+" plugin.")}),Mi[e]=function(a,u,h){return i(_t(a),dt(u||{},r),h)},o&&(tt.prototype[e]=function(a,u,h){return this.add(Mi[e](a,St(u)?u:(h=u)&&{},this),h)})},registerEase:function(t,e){D[t]=Zt(e)},parseEase:function(t,e){return arguments.length?Zt(t,e):D},getById:function(t){return L.getById(t)},exportRoot:function(t,e){t===void 0&&(t={});var i=new tt(t),s,r;for(i.smoothChildTiming=it(t.smoothChildTiming),L.remove(i),i._dp=0,i._time=i._tTime=L._time,s=L._first;s;)r=s._next,(e||!(!s._dur&&s instanceof W&&s.vars.onComplete===s._targets[0]))&&xt(i,s,s._start-s._delay),s=r;return xt(L,i,0),i},context:function(t,e){return t?new Fo(t,e):E},matchMedia:function(t){return new $h(t)},matchMediaRefresh:function(){return Qt.forEach(function(t){var e=t.conditions,i,s;for(s in e)e[s]&&(e[s]=!1,i=1);i&&t.revert()})||nn()},addEventListener:function(t,e){var i=ei[t]||(ei[t]=[]);~i.indexOf(e)||i.push(e)},removeEventListener:function(t,e){var i=ei[t],s=i&&i.indexOf(e);s>=0&&i.splice(s,1)},utils:{wrap:Sh,wrapYoyo:wh,distribute:go,random:yo,snap:_o,normalize:Ph,getUnit:K,clamp:yh,splitColor:So,toArray:_t,selector:Ji,mapRange:xo,pipe:xh,unitize:Th,interpolate:Ch,shuffle:mo},install:io,effects:Mi,ticker:lt,updateRoot:tt.updateRoot,plugins:ut,globalTimeline:L,core:{PropTween:st,globals:no,Tween:W,Timeline:tt,Animation:Ne,getCache:qt,_removeLinkedListItem:_i,reverting:function(){return q},context:function(t){return t&&E&&(E.data.push(t),t._ctx=E),E},suppressOverwrites:function(t){return bn=t}}};nt("to,from,fromTo,delayedCall,set,killTweensOf",function(n){return hi[n]=W[n]});lt.add(tt.updateRoot);ue=hi.to({},{duration:0});var Yh=function(t,e){for(var i=t._pt;i&&i.p!==e&&i.op!==e&&i.fp!==e;)i=i._next;return i},Xh=function(t,e){var i=t._targets,s,r,o;for(s in e)for(r=i.length;r--;)o=t._ptLookup[r][s],o&&(o=o.d)&&(o._pt&&(o=Yh(o,s)),o&&o.modifier&&o.modifier(e[s],t,i[r],s))},Ei=function(t,e){return{name:t,headless:1,rawVars:1,init:function(s,r,o){o._onInit=function(a){var u,h;if(H(r)&&(u={},nt(r,function(l){return u[l]=1}),r=u),e){u={};for(h in r)u[h]=e(r[h]);r=u}Xh(a,r)}}}},ot=hi.registerPlugin({name:"attr",init:function(t,e,i,s,r){var o,a,u;this.tween=i;for(o in e)u=t.getAttribute(o)||"",a=this.add(t,"setAttribute",(u||0)+"",e[o],s,r,0,0,o),a.op=o,a.b=u,this._props.push(o)},render:function(t,e){for(var i=e._pt;i;)q?i.set(i.t,i.p,i.b,i):i.r(t,i.d),i=i._next}},{name:"endArray",headless:1,init:function(t,e){for(var i=e.length;i--;)this.add(t,i,t[i]||0,e[i],0,0,0,0,0,1)}},Ei("roundProps",tn),Ei("modifiers"),Ei("snap",_o))||hi;W.version=tt.version=ot.version="3.13.0";eo=1;Dn()&&ve();D.Power0;D.Power1;D.Power2;D.Power3;D.Power4;D.Linear;D.Quad;D.Cubic;D.Quart;D.Quint;D.Strong;D.Elastic;D.Back;D.SteppedEase;D.Bounce;D.Sine;D.Expo;D.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Gs,kt,ce,zn,Ht,$s,Un,Hh=function(){return typeof window<"u"},Vt={},Xt=180/Math.PI,fe=Math.PI/180,ie=Math.atan2,Ys=1e8,Nn=/([A-Z])/g,qh=/(left|right|width|margin|padding|x)/i,Kh=/[\s,\(]\S/,Tt={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},sn=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},Zh=function(t,e){return e.set(e.t,e.p,t===1?e.e:Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},Qh=function(t,e){return e.set(e.t,e.p,t?Math.round((e.s+e.c*t)*1e4)/1e4+e.u:e.b,e)},Jh=function(t,e){var i=e.s+e.c*t;e.set(e.t,e.p,~~(i+(i<0?-.5:.5))+e.u,e)},Io=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},jo=function(t,e){return e.set(e.t,e.p,t!==1?e.b:e.e,e)},tc=function(t,e,i){return t.style[e]=i},ec=function(t,e,i){return t.style.setProperty(e,i)},ic=function(t,e,i){return t._gsap[e]=i},nc=function(t,e,i){return t._gsap.scaleX=t._gsap.scaleY=i},sc=function(t,e,i,s,r){var o=t._gsap;o.scaleX=o.scaleY=i,o.renderTransform(r,o)},rc=function(t,e,i,s,r){var o=t._gsap;o[e]=i,o.renderTransform(r,o)},B="transform",rt=B+"Origin",oc=function n(t,e){var i=this,s=this.target,r=s.style,o=s._gsap;if(t in Vt&&r){if(this.tfm=this.tfm||{},t!=="transform")t=Tt[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return i.tfm[a]=At(s,a)}):this.tfm[t]=o.x?o[t]:At(s,t),t===rt&&(this.tfm.zOrigin=o.zOrigin);else return Tt.transform.split(",").forEach(function(a){return n.call(i,a,e)});if(this.props.indexOf(B)>=0)return;o.svg&&(this.svgo=s.getAttribute("data-svg-origin"),this.props.push(rt,e,"")),t=B}(r||e)&&this.props.push(t,e,r[t])},zo=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},ac=function(){var t=this.props,e=this.target,i=e.style,s=e._gsap,r,o;for(r=0;r<t.length;r+=3)t[r+1]?t[r+1]===2?e[t[r]](t[r+2]):e[t[r]]=t[r+2]:t[r+2]?i[t[r]]=t[r+2]:i.removeProperty(t[r].substr(0,2)==="--"?t[r]:t[r].replace(Nn,"-$1").toLowerCase());if(this.tfm){for(o in this.tfm)s[o]=this.tfm[o];s.svg&&(s.renderTransform(),e.setAttribute("data-svg-origin",this.svgo||"")),r=Un(),(!r||!r.isStart)&&!i[B]&&(zo(i),s.zOrigin&&i[rt]&&(i[rt]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},Uo=function(t,e){var i={target:t,props:[],revert:ac,save:oc};return t._gsap||ot.core.getCache(t),e&&t.style&&t.nodeType&&e.split(",").forEach(function(s){return i.save(s)}),i},No,rn=function(t,e){var i=kt.createElementNS?kt.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):kt.createElement(t);return i&&i.style?i:kt.createElement(t)},yt=function n(t,e,i){var s=getComputedStyle(t);return s[e]||s.getPropertyValue(e.replace(Nn,"-$1").toLowerCase())||s.getPropertyValue(e)||!i&&n(t,xe(e)||e,1)||""},Xs="O,Moz,ms,Ms,Webkit".split(","),xe=function(t,e,i){var s=e||Ht,r=s.style,o=5;if(t in r&&!i)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);o--&&!(Xs[o]+t in r););return o<0?null:(o===3?"ms":o>=0?Xs[o]:"")+t},on=function(){Hh()&&window.document&&(Gs=window,kt=Gs.document,ce=kt.documentElement,Ht=rn("div")||{style:{}},rn("div"),B=xe(B),rt=B+"Origin",Ht.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",No=!!xe("perspective"),Un=ot.core.reverting,zn=1)},Hs=function(t){var e=t.ownerSVGElement,i=rn("svg",e&&e.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=t.cloneNode(!0),r;s.style.display="block",i.appendChild(s),ce.appendChild(i);try{r=s.getBBox()}catch{}return i.removeChild(s),ce.removeChild(i),r},qs=function(t,e){for(var i=e.length;i--;)if(t.hasAttribute(e[i]))return t.getAttribute(e[i])},Wo=function(t){var e,i;try{e=t.getBBox()}catch{e=Hs(t),i=1}return e&&(e.width||e.height)||i||(e=Hs(t)),e&&!e.width&&!e.x&&!e.y?{x:+qs(t,["x","cx","x1"])||0,y:+qs(t,["y","cy","y1"])||0,width:0,height:0}:e},Go=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&Wo(t))},te=function(t,e){if(e){var i=t.style,s;e in Vt&&e!==rt&&(e=B),i.removeProperty?(s=e.substr(0,2),(s==="ms"||e.substr(0,6)==="webkit")&&(e="-"+e),i.removeProperty(s==="--"?e:e.replace(Nn,"-$1").toLowerCase())):i.removeAttribute(e)}},Et=function(t,e,i,s,r,o){var a=new st(t._pt,e,i,0,1,o?jo:Io);return t._pt=a,a.b=s,a.e=r,t._props.push(i),a},Ks={deg:1,rad:1,turn:1},uc={grid:1,flex:1},It=function n(t,e,i,s){var r=parseFloat(i)||0,o=(i+"").trim().substr((r+"").length)||"px",a=Ht.style,u=qh.test(e),h=t.tagName.toLowerCase()==="svg",l=(h?"client":"offset")+(u?"Width":"Height"),c=100,f=s==="px",d=s==="%",m,p,g,_;if(s===o||!r||Ks[s]||Ks[o])return r;if(o!=="px"&&!f&&(r=n(t,e,i,"px")),_=t.getCTM&&Go(t),(d||o==="%")&&(Vt[e]||~e.indexOf("adius")))return m=_?t.getBBox()[u?"width":"height"]:t[l],U(d?r/m*c:r/100*m);if(a[u?"width":"height"]=c+(f?o:s),p=s!=="rem"&&~e.indexOf("adius")||s==="em"&&t.appendChild&&!h?t:t.parentNode,_&&(p=(t.ownerSVGElement||{}).parentNode),(!p||p===kt||!p.appendChild)&&(p=kt.body),g=p._gsap,g&&d&&g.width&&u&&g.time===lt.time&&!g.uncache)return U(r/g.width*c);if(d&&(e==="height"||e==="width")){var y=t.style[e];t.style[e]=c+s,m=t[l],y?t.style[e]=y:te(t,e)}else(d||o==="%")&&!uc[yt(p,"display")]&&(a.position=yt(t,"position")),p===t&&(a.position="static"),p.appendChild(Ht),m=Ht[l],p.removeChild(Ht),a.position="absolute";return u&&d&&(g=qt(p),g.time=lt.time,g.width=p[l]),U(f?m*r/c:m&&r?c/m*r:0)},At=function(t,e,i,s){var r;return zn||on(),e in Tt&&e!=="transform"&&(e=Tt[e],~e.indexOf(",")&&(e=e.split(",")[0])),Vt[e]&&e!=="transform"?(r=Ge(t,s),r=e!=="transformOrigin"?r[e]:r.svg?r.origin:fi(yt(t,rt))+" "+r.zOrigin+"px"):(r=t.style[e],(!r||r==="auto"||s||~(r+"").indexOf("calc("))&&(r=ci[e]&&ci[e](t,e,i)||yt(t,e)||ro(t,e)||(e==="opacity"?1:0))),i&&!~(r+"").trim().indexOf(" ")?It(t,e,r,i)+i:r},lc=function(t,e,i,s){if(!i||i==="none"){var r=xe(e,t,1),o=r&&yt(t,r,1);o&&o!==i?(e=r,i=o):e==="borderColor"&&(i=yt(t,"borderTopColor"))}var a=new st(this._pt,t.style,e,0,1,Lo),u=0,h=0,l,c,f,d,m,p,g,_,y,v,T,x;if(a.b=i,a.e=s,i+="",s+="",s.substring(0,6)==="var(--"&&(s=yt(t,s.substring(4,s.indexOf(")")))),s==="auto"&&(p=t.style[e],t.style[e]=s,s=yt(t,e)||s,p?t.style[e]=p:te(t,e)),l=[i,s],Co(l),i=l[0],s=l[1],f=i.match(ae)||[],x=s.match(ae)||[],x.length){for(;c=ae.exec(s);)g=c[0],y=s.substring(u,c.index),m?m=(m+1)%5:(y.substr(-5)==="rgba("||y.substr(-5)==="hsla(")&&(m=1),g!==(p=f[h++]||"")&&(d=parseFloat(p)||0,T=p.substr((d+"").length),g.charAt(1)==="="&&(g=he(d,g)+T),_=parseFloat(g),v=g.substr((_+"").length),u=ae.lastIndex-v.length,v||(v=v||ct.units[e]||T,u===s.length&&(s+=v,a.e+=v)),T!==v&&(d=It(t,e,p,v)||0),a._pt={_next:a._pt,p:y||h===1?y:",",s:d,c:_-d,m:m&&m<4||e==="zIndex"?Math.round:0});a.c=u<s.length?s.substring(u,s.length):""}else a.r=e==="display"&&s==="none"?jo:Io;return Jr.test(s)&&(a.e=0),this._pt=a,a},Zs={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},hc=function(t){var e=t.split(" "),i=e[0],s=e[1]||"50%";return(i==="top"||i==="bottom"||s==="left"||s==="right")&&(t=i,i=s,s=t),e[0]=Zs[i]||i,e[1]=Zs[s]||s,e.join(" ")},cc=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var i=e.t,s=i.style,r=e.u,o=i._gsap,a,u,h;if(r==="all"||r===!0)s.cssText="",u=1;else for(r=r.split(","),h=r.length;--h>-1;)a=r[h],Vt[a]&&(u=1,a=a==="transformOrigin"?rt:B),te(i,a);u&&(te(i,B),o&&(o.svg&&i.removeAttribute("transform"),s.scale=s.rotate=s.translate="none",Ge(i,1),o.uncache=1,zo(s)))}},ci={clearProps:function(t,e,i,s,r){if(r.data!=="isFromStart"){var o=t._pt=new st(t._pt,e,i,0,0,cc);return o.u=s,o.pr=-10,o.tween=r,t._props.push(i),1}}},We=[1,0,0,1,0,0],$o={},Yo=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Qs=function(t){var e=yt(t,B);return Yo(e)?We:e.substr(7).match(Qr).map(U)},Wn=function(t,e){var i=t._gsap||qt(t),s=t.style,r=Qs(t),o,a,u,h;return i.svg&&t.getAttribute("transform")?(u=t.transform.baseVal.consolidate().matrix,r=[u.a,u.b,u.c,u.d,u.e,u.f],r.join(",")==="1,0,0,1,0,0"?We:r):(r===We&&!t.offsetParent&&t!==ce&&!i.svg&&(u=s.display,s.display="block",o=t.parentNode,(!o||!t.offsetParent&&!t.getBoundingClientRect().width)&&(h=1,a=t.nextElementSibling,ce.appendChild(t)),r=Qs(t),u?s.display=u:te(t,"display"),h&&(a?o.insertBefore(t,a):o?o.appendChild(t):ce.removeChild(t))),e&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r)},an=function(t,e,i,s,r,o){var a=t._gsap,u=r||Wn(t,!0),h=a.xOrigin||0,l=a.yOrigin||0,c=a.xOffset||0,f=a.yOffset||0,d=u[0],m=u[1],p=u[2],g=u[3],_=u[4],y=u[5],v=e.split(" "),T=parseFloat(v[0])||0,x=parseFloat(v[1])||0,P,w,C,S;i?u!==We&&(w=d*g-m*p)&&(C=T*(g/w)+x*(-p/w)+(p*y-g*_)/w,S=T*(-m/w)+x*(d/w)-(d*y-m*_)/w,T=C,x=S):(P=Wo(t),T=P.x+(~v[0].indexOf("%")?T/100*P.width:T),x=P.y+(~(v[1]||v[0]).indexOf("%")?x/100*P.height:x)),s||s!==!1&&a.smooth?(_=T-h,y=x-l,a.xOffset=c+(_*d+y*p)-_,a.yOffset=f+(_*m+y*g)-y):a.xOffset=a.yOffset=0,a.xOrigin=T,a.yOrigin=x,a.smooth=!!s,a.origin=e,a.originIsAbsolute=!!i,t.style[rt]="0px 0px",o&&(Et(o,a,"xOrigin",h,T),Et(o,a,"yOrigin",l,x),Et(o,a,"xOffset",c,a.xOffset),Et(o,a,"yOffset",f,a.yOffset)),t.setAttribute("data-svg-origin",T+" "+x)},Ge=function(t,e){var i=t._gsap||new Mo(t);if("x"in i&&!e&&!i.uncache)return i;var s=t.style,r=i.scaleX<0,o="px",a="deg",u=getComputedStyle(t),h=yt(t,rt)||"0",l,c,f,d,m,p,g,_,y,v,T,x,P,w,C,S,A,V,R,O,N,$,I,M,j,Rt,wt,Pe,Ut,Gn,Ct,Nt;return l=c=f=p=g=_=y=v=T=0,d=m=1,i.svg=!!(t.getCTM&&Go(t)),u.translate&&((u.translate!=="none"||u.scale!=="none"||u.rotate!=="none")&&(s[B]=(u.translate!=="none"?"translate3d("+(u.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(u.rotate!=="none"?"rotate("+u.rotate+") ":"")+(u.scale!=="none"?"scale("+u.scale.split(" ").join(",")+") ":"")+(u[B]!=="none"?u[B]:"")),s.scale=s.rotate=s.translate="none"),w=Wn(t,i.svg),i.svg&&(i.uncache?(j=t.getBBox(),h=i.xOrigin-j.x+"px "+(i.yOrigin-j.y)+"px",M=""):M=!e&&t.getAttribute("data-svg-origin"),an(t,M||h,!!M||i.originIsAbsolute,i.smooth!==!1,w)),x=i.xOrigin||0,P=i.yOrigin||0,w!==We&&(V=w[0],R=w[1],O=w[2],N=w[3],l=$=w[4],c=I=w[5],w.length===6?(d=Math.sqrt(V*V+R*R),m=Math.sqrt(N*N+O*O),p=V||R?ie(R,V)*Xt:0,y=O||N?ie(O,N)*Xt+p:0,y&&(m*=Math.abs(Math.cos(y*fe))),i.svg&&(l-=x-(x*V+P*O),c-=P-(x*R+P*N))):(Nt=w[6],Gn=w[7],wt=w[8],Pe=w[9],Ut=w[10],Ct=w[11],l=w[12],c=w[13],f=w[14],C=ie(Nt,Ut),g=C*Xt,C&&(S=Math.cos(-C),A=Math.sin(-C),M=$*S+wt*A,j=I*S+Pe*A,Rt=Nt*S+Ut*A,wt=$*-A+wt*S,Pe=I*-A+Pe*S,Ut=Nt*-A+Ut*S,Ct=Gn*-A+Ct*S,$=M,I=j,Nt=Rt),C=ie(-O,Ut),_=C*Xt,C&&(S=Math.cos(-C),A=Math.sin(-C),M=V*S-wt*A,j=R*S-Pe*A,Rt=O*S-Ut*A,Ct=N*A+Ct*S,V=M,R=j,O=Rt),C=ie(R,V),p=C*Xt,C&&(S=Math.cos(C),A=Math.sin(C),M=V*S+R*A,j=$*S+I*A,R=R*S-V*A,I=I*S-$*A,V=M,$=j),g&&Math.abs(g)+Math.abs(p)>359.9&&(g=p=0,_=180-_),d=U(Math.sqrt(V*V+R*R+O*O)),m=U(Math.sqrt(I*I+Nt*Nt)),C=ie($,I),y=Math.abs(C)>2e-4?C*Xt:0,T=Ct?1/(Ct<0?-Ct:Ct):0),i.svg&&(M=t.getAttribute("transform"),i.forceCSS=t.setAttribute("transform","")||!Yo(yt(t,B)),M&&t.setAttribute("transform",M))),Math.abs(y)>90&&Math.abs(y)<270&&(r?(d*=-1,y+=p<=0?180:-180,p+=p<=0?180:-180):(m*=-1,y+=y<=0?180:-180)),e=e||i.uncache,i.x=l-((i.xPercent=l&&(!e&&i.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-l)?-50:0)))?t.offsetWidth*i.xPercent/100:0)+o,i.y=c-((i.yPercent=c&&(!e&&i.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-c)?-50:0)))?t.offsetHeight*i.yPercent/100:0)+o,i.z=f+o,i.scaleX=U(d),i.scaleY=U(m),i.rotation=U(p)+a,i.rotationX=U(g)+a,i.rotationY=U(_)+a,i.skewX=y+a,i.skewY=v+a,i.transformPerspective=T+o,(i.zOrigin=parseFloat(h.split(" ")[2])||!e&&i.zOrigin||0)&&(s[rt]=fi(h)),i.xOffset=i.yOffset=0,i.force3D=ct.force3D,i.renderTransform=i.svg?dc:No?Xo:fc,i.uncache=0,i},fi=function(t){return(t=t.split(" "))[0]+" "+t[1]},Li=function(t,e,i){var s=K(e);return U(parseFloat(e)+parseFloat(It(t,"x",i+"px",s)))+s},fc=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,Xo(t,e)},Gt="0deg",we="0px",$t=") ",Xo=function(t,e){var i=e||this,s=i.xPercent,r=i.yPercent,o=i.x,a=i.y,u=i.z,h=i.rotation,l=i.rotationY,c=i.rotationX,f=i.skewX,d=i.skewY,m=i.scaleX,p=i.scaleY,g=i.transformPerspective,_=i.force3D,y=i.target,v=i.zOrigin,T="",x=_==="auto"&&t&&t!==1||_===!0;if(v&&(c!==Gt||l!==Gt)){var P=parseFloat(l)*fe,w=Math.sin(P),C=Math.cos(P),S;P=parseFloat(c)*fe,S=Math.cos(P),o=Li(y,o,w*S*-v),a=Li(y,a,-Math.sin(P)*-v),u=Li(y,u,C*S*-v+v)}g!==we&&(T+="perspective("+g+$t),(s||r)&&(T+="translate("+s+"%, "+r+"%) "),(x||o!==we||a!==we||u!==we)&&(T+=u!==we||x?"translate3d("+o+", "+a+", "+u+") ":"translate("+o+", "+a+$t),h!==Gt&&(T+="rotate("+h+$t),l!==Gt&&(T+="rotateY("+l+$t),c!==Gt&&(T+="rotateX("+c+$t),(f!==Gt||d!==Gt)&&(T+="skew("+f+", "+d+$t),(m!==1||p!==1)&&(T+="scale("+m+", "+p+$t),y.style[B]=T||"translate(0, 0)"},dc=function(t,e){var i=e||this,s=i.xPercent,r=i.yPercent,o=i.x,a=i.y,u=i.rotation,h=i.skewX,l=i.skewY,c=i.scaleX,f=i.scaleY,d=i.target,m=i.xOrigin,p=i.yOrigin,g=i.xOffset,_=i.yOffset,y=i.forceCSS,v=parseFloat(o),T=parseFloat(a),x,P,w,C,S;u=parseFloat(u),h=parseFloat(h),l=parseFloat(l),l&&(l=parseFloat(l),h+=l,u+=l),u||h?(u*=fe,h*=fe,x=Math.cos(u)*c,P=Math.sin(u)*c,w=Math.sin(u-h)*-f,C=Math.cos(u-h)*f,h&&(l*=fe,S=Math.tan(h-l),S=Math.sqrt(1+S*S),w*=S,C*=S,l&&(S=Math.tan(l),S=Math.sqrt(1+S*S),x*=S,P*=S)),x=U(x),P=U(P),w=U(w),C=U(C)):(x=c,C=f,P=w=0),(v&&!~(o+"").indexOf("px")||T&&!~(a+"").indexOf("px"))&&(v=It(d,"x",o,"px"),T=It(d,"y",a,"px")),(m||p||g||_)&&(v=U(v+m-(m*x+p*w)+g),T=U(T+p-(m*P+p*C)+_)),(s||r)&&(S=d.getBBox(),v=U(v+s/100*S.width),T=U(T+r/100*S.height)),S="matrix("+x+","+P+","+w+","+C+","+v+","+T+")",d.setAttribute("transform",S),y&&(d.style[B]=S)},pc=function(t,e,i,s,r){var o=360,a=H(r),u=parseFloat(r)*(a&&~r.indexOf("rad")?Xt:1),h=u-s,l=s+h+"deg",c,f;return a&&(c=r.split("_")[1],c==="short"&&(h%=o,h!==h%(o/2)&&(h+=h<0?o:-360)),c==="cw"&&h<0?h=(h+o*Ys)%o-~~(h/o)*o:c==="ccw"&&h>0&&(h=(h-o*Ys)%o-~~(h/o)*o)),t._pt=f=new st(t._pt,e,i,s,h,Zh),f.e=l,f.u="deg",t._props.push(i),f},Js=function(t,e){for(var i in e)t[i]=e[i];return t},mc=function(t,e,i){var s=Js({},i._gsap),r="perspective,force3D,transformOrigin,svgOrigin",o=i.style,a,u,h,l,c,f,d,m;s.svg?(h=i.getAttribute("transform"),i.setAttribute("transform",""),o[B]=e,a=Ge(i,1),te(i,B),i.setAttribute("transform",h)):(h=getComputedStyle(i)[B],o[B]=e,a=Ge(i,1),o[B]=h);for(u in Vt)h=s[u],l=a[u],h!==l&&r.indexOf(u)<0&&(d=K(h),m=K(l),c=d!==m?It(i,u,h,m):parseFloat(h),f=parseFloat(l),t._pt=new st(t._pt,a,u,c,f-c,sn),t._pt.u=m||0,t._props.push(u));Js(a,s)};nt("padding,margin,Width,Radius",function(n,t){var e="Top",i="Right",s="Bottom",r="Left",o=(t<3?[e,i,s,r]:[e+r,e+i,s+i,s+r]).map(function(a){return t<2?n+a:"border"+a+n});ci[t>1?"border"+n:n]=function(a,u,h,l,c){var f,d;if(arguments.length<4)return f=o.map(function(m){return At(a,m,h)}),d=f.join(" "),d.split(f[0]).length===5?f[0]:d;f=(l+"").split(" "),d={},o.forEach(function(m,p){return d[m]=f[p]=f[p]||f[(p-1)/2|0]}),a.init(u,d,c)}});var Ho={name:"css",register:on,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,i,s,r){var o=this._props,a=t.style,u=i.vars.startAt,h,l,c,f,d,m,p,g,_,y,v,T,x,P,w,C;zn||on(),this.styles=this.styles||Uo(t),C=this.styles.props,this.tween=i;for(p in e)if(p!=="autoRound"&&(l=e[p],!(ut[p]&&Vo(p,e,i,s,t,r)))){if(d=typeof l,m=ci[p],d==="function"&&(l=l.call(i,s,t,r),d=typeof l),d==="string"&&~l.indexOf("random(")&&(l=ze(l)),m)m(this,t,p,l,i)&&(w=1);else if(p.substr(0,2)==="--")h=(getComputedStyle(t).getPropertyValue(p)+"").trim(),l+="",Bt.lastIndex=0,Bt.test(h)||(g=K(h),_=K(l)),_?g!==_&&(h=It(t,p,h,_)+_):g&&(l+=g),this.add(a,"setProperty",h,l,s,r,0,0,p),o.push(p),C.push(p,0,a[p]);else if(d!=="undefined"){if(u&&p in u?(h=typeof u[p]=="function"?u[p].call(i,s,t,r):u[p],H(h)&&~h.indexOf("random(")&&(h=ze(h)),K(h+"")||h==="auto"||(h+=ct.units[p]||K(At(t,p))||""),(h+"").charAt(1)==="="&&(h=At(t,p))):h=At(t,p),f=parseFloat(h),y=d==="string"&&l.charAt(1)==="="&&l.substr(0,2),y&&(l=l.substr(2)),c=parseFloat(l),p in Tt&&(p==="autoAlpha"&&(f===1&&At(t,"visibility")==="hidden"&&c&&(f=0),C.push("visibility",0,a.visibility),Et(this,a,"visibility",f?"inherit":"hidden",c?"inherit":"hidden",!c)),p!=="scale"&&p!=="transform"&&(p=Tt[p],~p.indexOf(",")&&(p=p.split(",")[0]))),v=p in Vt,v){if(this.styles.save(p),d==="string"&&l.substring(0,6)==="var(--"&&(l=yt(t,l.substring(4,l.indexOf(")"))),c=parseFloat(l)),T||(x=t._gsap,x.renderTransform&&!e.parseTransform||Ge(t,e.parseTransform),P=e.smoothOrigin!==!1&&x.smooth,T=this._pt=new st(this._pt,a,B,0,1,x.renderTransform,x,0,-1),T.dep=1),p==="scale")this._pt=new st(this._pt,x,"scaleY",x.scaleY,(y?he(x.scaleY,y+c):c)-x.scaleY||0,sn),this._pt.u=0,o.push("scaleY",p),p+="X";else if(p==="transformOrigin"){C.push(rt,0,a[rt]),l=hc(l),x.svg?an(t,l,0,P,0,this):(_=parseFloat(l.split(" ")[2])||0,_!==x.zOrigin&&Et(this,x,"zOrigin",x.zOrigin,_),Et(this,a,p,fi(h),fi(l)));continue}else if(p==="svgOrigin"){an(t,l,1,P,0,this);continue}else if(p in $o){pc(this,x,p,f,y?he(f,y+l):l);continue}else if(p==="smoothOrigin"){Et(this,x,"smooth",x.smooth,l);continue}else if(p==="force3D"){x[p]=l;continue}else if(p==="transform"){mc(this,l,t);continue}}else p in a||(p=xe(p)||p);if(v||(c||c===0)&&(f||f===0)&&!Kh.test(l)&&p in a)g=(h+"").substr((f+"").length),c||(c=0),_=K(l)||(p in ct.units?ct.units[p]:g),g!==_&&(f=It(t,p,h,_)),this._pt=new st(this._pt,v?x:a,p,f,(y?he(f,y+c):c)-f,!v&&(_==="px"||p==="zIndex")&&e.autoRound!==!1?Jh:sn),this._pt.u=_||0,g!==_&&_!=="%"&&(this._pt.b=h,this._pt.r=Qh);else if(p in a)lc.call(this,t,p,h,y?y+l:l);else if(p in t)this.add(t,p,h||t[p],y?y+l:l,s,r);else if(p!=="parseTransform"){Vn(p,l);continue}v||(p in a?C.push(p,0,a[p]):typeof t[p]=="function"?C.push(p,2,t[p]()):C.push(p,1,h||t[p])),o.push(p)}}w&&Bo(this)},render:function(t,e){if(e.tween._time||!Un())for(var i=e._pt;i;)i.r(t,i.d),i=i._next;else e.styles.revert()},get:At,aliases:Tt,getSetter:function(t,e,i){var s=Tt[e];return s&&s.indexOf(",")<0&&(e=s),e in Vt&&e!==rt&&(t._gsap.x||At(t,"x"))?i&&$s===i?e==="scale"?nc:ic:($s=i||{})&&(e==="scale"?sc:rc):t.style&&!An(t.style[e])?tc:~e.indexOf("-")?ec:In(t,e)},core:{_removeProperty:te,_getMatrix:Wn}};ot.utils.checkPrefix=xe;ot.core.getStyleSaver=Uo;(function(n,t,e,i){var s=nt(n+","+t+","+e,function(r){Vt[r]=1});nt(t,function(r){ct.units[r]="deg",$o[r]=1}),Tt[s[13]]=n+","+t,nt(i,function(r){var o=r.split(":");Tt[o[1]]=s[o[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");nt("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(n){ct.units[n]="px"});ot.registerPlugin(Ho);var gc=ot.registerPlugin(Ho)||ot;gc.core.Tween;export{vc as A,gc as g,Tc as m};
